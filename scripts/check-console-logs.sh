#!/bin/bash

# Script para verificar se há declarações de console restantes no projeto
# Uso: ./scripts/check-console-logs.sh

set -e

echo "🔍 Verificando declarações de console no projeto FluxoMax..."
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Contadores
TOTAL_ISSUES=0
FRONTEND_ISSUES=0
BACKEND_ISSUES=0

# Função para verificar arquivos
check_console_logs() {
    local directory=$1
    local label=$2
    local file_pattern=$3
    
    echo -e "\n📁 Verificando ${label}..."
    echo "Diretório: ${directory}"
    echo "Padrão de arquivos: ${file_pattern}"
    
    # Buscar por declarações de console
    local console_files=$(find "$directory" -name "$file_pattern" -type f \
        -not -path "*/node_modules/*" \
        -not -path "*/dist/*" \
        -not -path "*/build/*" \
        -not -path "*/.git/*" \
        -exec grep -l "console\." {} \; 2>/dev/null || true)
    
    if [ -z "$console_files" ]; then
        echo -e "${GREEN}✅ Nenhuma declaração de console encontrada${NC}"
        return 0
    fi
    
    echo -e "${RED}❌ Declarações de console encontradas nos seguintes arquivos:${NC}"
    
    local count=0
    for file in $console_files; do
        echo -e "${YELLOW}📄 $file${NC}"
        
        # Mostrar as linhas específicas com console
        grep -n "console\." "$file" | head -5 | while read line; do
            echo "   $line"
        done
        
        # Contar linhas com console neste arquivo
        local file_count=$(grep -c "console\." "$file" 2>/dev/null || echo "0")
        count=$((count + file_count))
        echo "   → $file_count ocorrências"
        echo ""
    done
    
    if [ "$label" = "Frontend" ]; then
        FRONTEND_ISSUES=$count
    else
        BACKEND_ISSUES=$count
    fi
    
    TOTAL_ISSUES=$((TOTAL_ISSUES + count))
    
    echo -e "${RED}Total de declarações de console em ${label}: $count${NC}"
    return $count
}

# Verificar se os diretórios existem
if [ ! -d "frontend/src" ]; then
    echo -e "${RED}❌ Diretório frontend/src não encontrado${NC}"
    exit 1
fi

if [ ! -d "backend/src" ]; then
    echo -e "${RED}❌ Diretório backend/src não encontrado${NC}"
    exit 1
fi

# Verificar frontend
check_console_logs "frontend/src" "Frontend" "*.{ts,tsx,js,jsx}"

# Verificar backend
check_console_logs "backend/src" "Backend" "*.{ts,js}"

# Verificar scripts
if [ -d "backend/scripts" ]; then
    check_console_logs "backend/scripts" "Backend Scripts" "*.{ts,js}"
fi

# Verificar arquivos de teste específicos
echo -e "\n🧪 Verificando arquivos de teste específicos..."
test_files=("backend/test-auth.js")

for test_file in "${test_files[@]}"; do
    if [ -f "$test_file" ]; then
        console_count=$(grep -c "console\." "$test_file" 2>/dev/null || echo "0")
        if [ "$console_count" -gt 0 ]; then
            echo -e "${YELLOW}⚠️  $test_file: $console_count declarações de console${NC}"
            TOTAL_ISSUES=$((TOTAL_ISSUES + console_count))
        fi
    fi
done

# Verificar arquivos de configuração que podem ter logs
echo -e "\n⚙️  Verificando arquivos de configuração..."
config_files=("dev.sh" "backend/consolidate_migrations.sh")

for config_file in "${config_files[@]}"; do
    if [ -f "$config_file" ]; then
        # Para scripts shell, verificar echo que podem expor informações
        echo_count=$(grep -c "echo.*:" "$config_file" 2>/dev/null || echo "0")
        if [ "$echo_count" -gt 5 ]; then  # Threshold para muitos echos
            echo -e "${YELLOW}⚠️  $config_file: $echo_count comandos echo (revisar se expõem informações sensíveis)${NC}"
        fi
    fi
done

# Resumo final
echo -e "\n📊 RESUMO DA VERIFICAÇÃO"
echo "=================================================="
echo -e "Frontend: ${FRONTEND_ISSUES} declarações de console"
echo -e "Backend: ${BACKEND_ISSUES} declarações de console"
echo -e "Total: ${TOTAL_ISSUES} declarações de console"

if [ $TOTAL_ISSUES -eq 0 ]; then
    echo -e "\n${GREEN}🎉 SUCESSO: Nenhuma declaração de console encontrada!${NC}"
    echo -e "${GREEN}✅ O projeto está em conformidade com as diretrizes de segurança${NC}"
    exit 0
else
    echo -e "\n${RED}❌ FALHA: $TOTAL_ISSUES declarações de console encontradas${NC}"
    echo -e "${RED}🔧 Ação necessária: Remover ou substituir pelos logs seguros${NC}"
    echo -e "\n💡 Dicas:"
    echo -e "   • Use o sistema de logging seguro: import { logger } from '@/utils/secureLogger'"
    echo -e "   • Para debugging: logger.debug('message', context)"
    echo -e "   • Para erros: logger.error('message', error, context)"
    echo -e "   • Consulte: docs/SECURITY_LOGGING_GUIDELINES.md"
    exit 1
fi
