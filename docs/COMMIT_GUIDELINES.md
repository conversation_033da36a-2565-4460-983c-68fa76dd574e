# Diretrizes de Commits - FluxoMax

## Índice

- [Introdução](#introdução)
- [Conventional Commits](#conventional-commits)
- [Estrutura da Mensagem](#estrutura-da-mensagem)
- [Tipos de Commit](#tipos-de-commit)
- [Escopos do Projeto](#escopos-do-projeto)
- [Vinculação com Issues](#vinculação-com-issues)
- [Exemplos Práticos](#exemplos-práticos)
- [Boas Práticas](#boas-práticas)
- [Casos Especiais](#casos-especiais)
- [Templates](#templates)
- [Checklist](#checklist)

## Introdução

Este documento estabelece as diretrizes para mensagens de commit no projeto FluxoMax. Commits bem estruturados facilitam:

- **Rastreabilidade**: Conexão clara entre código e requisitos
- **Automação**: Geração automática de changelogs e versionamento
- **Colaboração**: Comunicação eficiente entre desenvolvedores
- **Manutenção**: Facilita debugging e rollbacks
- **Documentação**: Histórico claro das mudanças

## Conventional Commits

Seguimos o padrão [Conventional Commits](https://www.conventionalcommits.org/) adaptado para português brasileiro.

### Estrutura da Mensagem

```
<tipo>[escopo opcional]: <descrição>

[corpo opcional]

[rodapé opcional]
```

### Formato Detalhado

```
feat(auth): implementar autenticação JWT

Adicionar middleware de autenticação JWT para proteger rotas
da API. Inclui validação de token e extração de dados do usuário.

- Criar JwtAuthGuard para NestJS
- Implementar JwtStrategy com Passport
- Adicionar middleware de validação de empresa

Fixes: #123
Breaking Change: Altera estrutura de resposta da API de auth
```

## Tipos de Commit

| Tipo | Descrição | Exemplo |
|------|-----------|---------|
| `feat` | Nova funcionalidade | `feat(bank-accounts): adicionar filtro por tipo de conta` |
| `fix` | Correção de bug | `fix(auth): corrigir logout não limpando localStorage` |
| `docs` | Documentação | `docs(api): atualizar documentação do Swagger` |
| `style` | Formatação, espaços | `style(frontend): corrigir indentação em componentes` |
| `refactor` | Refatoração de código | `refactor(services): extrair lógica comum para utils` |
| `test` | Testes | `test(auth): adicionar testes unitários para AuthService` |
| `chore` | Tarefas de manutenção | `chore(deps): atualizar dependências do frontend` |
| `perf` | Melhoria de performance | `perf(queries): otimizar consultas de contas bancárias` |
| `ci` | Integração contínua | `ci(docker): otimizar build do container backend` |
| `build` | Sistema de build | `build(webpack): configurar code splitting` |
| `revert` | Reverter commit | `revert: reverter "feat(auth): implementar 2FA"` |

## Escopos do Projeto

### Frontend (React/TypeScript)
- `auth` - Autenticação e autorização
- `companies` - Gestão de empresas
- `bank-accounts` - Contas bancárias
- `accounts-payable` - Contas a pagar
- `accounts-receivable` - Contas a receber
- `entities` - Gestão de entidades
- `projects` - Gestão de projetos
- `ui` - Componentes de interface
- `hooks` - Custom hooks
- `services` - Serviços e APIs
- `utils` - Utilitários
- `types` - Definições de tipos

### Backend (NestJS)
- `auth` - Autenticação e JWT
- `users` - Gestão de usuários
- `companies` - Serviços de empresas
- `bank-accounts` - API de contas bancárias
- `accounts-payable` - API contas a pagar
- `accounts-receivable` - API contas a receber
- `entities` - API de entidades
- `projects` - API de projetos
- `database` - Migrações e seeds
- `guards` - Guards de autorização
- `middlewares` - Middlewares
- `dto` - Data Transfer Objects
- `prisma` - Configurações do Prisma

### Geral
- `config` - Configurações gerais
- `docker` - Configurações Docker
- `scripts` - Scripts de automação
- `deps` - Dependências
- `security` - Segurança

## Vinculação com Issues

### Palavras-chave para Fechamento Automático

```bash
# Fecha a issue automaticamente quando mergeado
Fixes: #123
Closes: #456
Resolves: #789

# Múltiplas issues
Fixes: #123, #456
Closes: #789

# Referência sem fechar
Refs: #123
See: #456
```

### Sintaxe Completa

```
fix(auth): corrigir validação de token expirado

Adicionar verificação adequada para tokens JWT expirados
e retornar erro apropriado para o frontend.

Fixes: #123
Refs: #456
```

## Exemplos Práticos

### ✅ Commits Bem Estruturados

```bash
# Nova funcionalidade
feat(bank-accounts): implementar filtro por banco
feat(ui): adicionar componente DateRangePicker
feat(auth): implementar refresh automático de token

# Correções
fix(companies): corrigir filtro global após refresh da página
fix(api): resolver erro 404 na atualização de contas bancárias
fix(frontend): corrigir estado loading infinito no CompanySelector

# Refatoração
refactor(services): extrair lógica de validação para utils
refactor(components): simplificar estrutura do BankAccountModal

# Documentação
docs(readme): atualizar instruções de instalação
docs(api): adicionar exemplos de uso no Swagger

# Testes
test(auth): adicionar testes para AuthContext
test(services): implementar testes unitários para BankAccountService

# Configuração
chore(deps): atualizar React para v18.2.0
chore(docker): otimizar Dockerfile do backend
```

### ❌ Commits a Evitar

```bash
# Muito vago
fix: correção
update: mudanças
wip: trabalho em progresso

# Sem contexto
fix bug
add feature
remove code

# Muito longo no título
feat(bank-accounts): implementar funcionalidade completa de filtros avançados com múltiplos critérios incluindo data, valor, tipo de conta e banco

# Mistura múltiplas mudanças
feat: adicionar filtro + fix: corrigir bug + docs: atualizar readme
```

## Boas Práticas

### 📝 Escrita da Mensagem

#### ✅ Fazer
- **Use o imperativo**: "adicionar", "corrigir", "implementar"
- **Seja específico**: Descreva o que foi alterado e por quê
- **Mantenha o título conciso**: Máximo 50 caracteres
- **Use português brasileiro**: Consistência na linguagem
- **Primeira letra minúscula**: Exceto nomes próprios
- **Sem ponto final**: No título da mensagem

#### ❌ Evitar
- **Tempo passado**: "adicionado", "corrigido"
- **Descrições vagas**: "melhorias", "ajustes"
- **Títulos longos**: Mais de 50 caracteres
- **Mistura de idiomas**: Português + inglês
- **Pontuação desnecessária**: Pontos, vírgulas no título

### 🔄 Frequência de Commits

#### ✅ Fazer
- **Commits atômicos**: Uma mudança lógica por commit
- **Commits frequentes**: Não acumule muitas mudanças
- **Estado funcional**: Cada commit deve deixar o código funcionando
- **Testes passando**: Sempre commite com testes verdes

#### ❌ Evitar
- **Commits gigantes**: Múltiplas funcionalidades juntas
- **Commits quebrados**: Código que não compila
- **WIP commits**: Work in Progress na branch principal
- **Commits de debug**: Console.log, debugger, etc.

### 🏗️ Estrutura do Monorepo

Para mudanças que afetam múltiplas partes:

```bash
# Mudança específica
feat(frontend/auth): implementar login com Google
fix(backend/api): corrigir validação de CNPJ

# Mudança que afeta ambos
feat(auth): implementar sistema de permissões RBAC

Implementar controle de acesso baseado em roles para
frontend e backend.

Frontend:
- Adicionar guards de rota por permissão
- Implementar componente PermissionGate
- Atualizar contexto de autenticação

Backend:
- Criar PermissionsGuard
- Implementar decorador @Permissions
- Atualizar middleware JWT

Fixes: #234
```

## Casos Especiais

### 🔥 Hotfixes

```bash
hotfix(auth): corrigir vulnerabilidade de segurança crítica

Corrigir validação de token JWT que permitia acesso
não autorizado em produção.

Fixes: #CRITICAL-001
```

### 💥 Breaking Changes

```bash
feat(api): migrar autenticação para OAuth 2.0

BREAKING CHANGE: Remove suporte a autenticação básica.
Clientes devem migrar para OAuth 2.0.

Migração:
- Atualizar configuração de cliente OAuth
- Substituir headers Authorization Basic por Bearer
- Atualizar fluxo de refresh token

Fixes: #456
```

### 🔀 Merges

```bash
# Merge de feature branch
merge: integrar funcionalidade de relatórios financeiros

Merge branch 'feature/financial-reports' into main

# Merge de hotfix
merge: aplicar correção crítica de segurança

Merge branch 'hotfix/security-patch' into main
```

### ⏪ Reverts

```bash
revert: reverter "feat(auth): implementar 2FA"

Esta funcionalidade causou problemas de performance
em produção. Será reimplementada na próxima versão.

Reverts: abc1234
Refs: #789
```

### 📦 Dependências

```bash
# Atualização de dependência
chore(deps): atualizar @nestjs/core para v10.2.0

# Adição de dependência
chore(deps): adicionar biblioteca date-fns

Adicionar date-fns para manipulação de datas no frontend.
Substitui moment.js para reduzir bundle size.

# Remoção de dependência
chore(deps): remover lodash não utilizado

Remove lodash e substitui por métodos nativos do JavaScript
para reduzir tamanho do bundle.
```

## Templates

### 🎯 Template Básico

```
<tipo>(<escopo>): <descrição concisa>

[Explicação detalhada do que foi alterado e por quê]

[Lista de mudanças específicas se necessário]
- Item 1
- Item 2
- Item 3

[Vinculação com issues]
Fixes: #123
Refs: #456
```

### 🚀 Template para Features

```
feat(<escopo>): <nova funcionalidade>

Implementar <funcionalidade> para <objetivo/benefício>.

Funcionalidades incluídas:
- <item 1>
- <item 2>
- <item 3>

Testes:
- <teste 1>
- <teste 2>

Fixes: #123
```

### 🐛 Template para Fixes

```
fix(<escopo>): <problema corrigido>

<Descrição do problema e como foi resolvido>

Causa raiz:
<Explicação da causa>

Solução:
<Explicação da solução implementada>

Fixes: #123
```

### 📚 Template para Documentação

```
docs(<escopo>): <tipo de documentação>

<Descrição das mudanças na documentação>

Mudanças incluem:
- <mudança 1>
- <mudança 2>

Refs: #123
```

## Checklist

Antes de fazer commit, verifique:

### ✅ Código
- [ ] Código compila sem erros
- [ ] Testes estão passando
- [ ] Não há console.log ou debugger
- [ ] Código segue padrões do projeto
- [ ] Não há credenciais ou dados sensíveis

### ✅ Mensagem
- [ ] Tipo de commit correto
- [ ] Escopo apropriado
- [ ] Descrição clara e concisa
- [ ] Português brasileiro correto
- [ ] Imperativo na descrição
- [ ] Vinculação com issue (se aplicável)

### ✅ Estrutura
- [ ] Commit atômico (uma mudança lógica)
- [ ] Título com máximo 50 caracteres
- [ ] Corpo explicativo (se necessário)
- [ ] Breaking changes documentadas
- [ ] Co-autores creditados (se aplicável)

---

## Ferramentas Recomendadas

### Commitizen
Para auxiliar na criação de commits padronizados:

```bash
npm install -g commitizen
npm install -g cz-conventional-changelog
```

### Commitlint
Para validar mensagens de commit:

```bash
npm install --save-dev @commitlint/cli @commitlint/config-conventional
```

### Husky
Para hooks de pre-commit:

```bash
npm install --save-dev husky
npx husky install
```

---

**Lembre-se**: Commits bem estruturados são um investimento na qualidade e manutenibilidade do projeto. Eles facilitam a colaboração e tornam o histórico do projeto uma ferramenta valiosa para toda a equipe.
