# Diretrizes de Segurança para Logging - FluxoMax

## 📋 Resumo

Este documento estabelece as diretrizes de segurança para logging na aplicação FluxoMax, garantindo que informações sensíveis não sejam expostas no console do navegador ou em logs de produção.

## 🚨 Problemas de Segurança com Logs

### Riscos Identificados
- **Exposição de dados sensíveis**: Tokens, senhas, informações pessoais
- **Revelação da arquitetura**: Estruturas internas da aplicação
- **Informações de debugging**: Caminhos de arquivos, configurações
- **Dados de usuários**: IDs, emails, informações financeiras

### Impactos Potenciais
- Violação de privacidade dos usuários
- Exposição de credenciais de acesso
- Facilitação de ataques direcionados
- Não conformidade com LGPD/GDPR

## ✅ Soluções Implementadas

### 1. Remoção Sistemática de Logs
Foram removidos ou substituídos logs em **27 arquivos do frontend** e **6 arquivos do backend**:

#### Frontend (27 arquivos):
- `main.tsx` - Logs de inicialização
- `ErrorLogger.tsx` - Logs de componentes
- `Diagnostico.tsx` - Logs de diagnóstico
- `MonitoringDebugger.tsx` - Logs de debug
- `useUserProfile.ts` - Logs de API
- `monitoringInitializer.ts` - Logs de inicialização
- `useBankAccounts.ts` - Logs de dados bancários
- `ErrorBoundary.tsx` - Logs de erro
- `errorHandling.ts` - Logs de erro de API
- `axios.ts` - Logs de interceptors HTTP
- `authService.ts` - Logs de autenticação
- `cacheUtils.ts` - Logs de cache
- `monitoringService.ts` - Logs de monitoramento
- `useActiveCompany.ts` - Logs de empresa ativa
- `useAuth.ts` - Logs de autenticação
- `BankAccounts.tsx` - Logs de dados bancários
- `NotFound.tsx` - Logs de erro 404
- `BankAccountsTemp.tsx` - Logs temporários
- `queryUtils.ts` - Logs de queries
- `eventBus.ts` - Logs de eventos
- `AuthContext.tsx` - Logs de contexto de auth
- `AppContext.tsx` - Logs de contexto da app
- `ProtectedRoute.tsx` - Logs de rotas protegidas
- `BankAccountTableRows.tsx` - Logs de tabela
- `BankAccountModal.tsx` - Logs de modal
- `useApiError.ts` - Logs de erro de API
- `ApiConnectionTest.tsx` - Logs de teste de conexão

#### Backend (6 arquivos):
- `main.ts` - Logs de inicialização do servidor
- `db-manager.js` - Logs de gerenciamento de BD
- `export-seed.js` - Logs de exportação
- `setup-roles.ts` - Logs de configuração
- `test-auth.js` - Logs de teste de autenticação
- `copy-sql-files.ts` - Logs de cópia de arquivos

### 2. Sistema de Logging Seguro
Criado `frontend/src/utils/secureLogger.ts` com:

#### Características:
- **Sanitização automática** de campos sensíveis
- **Níveis de log configuráveis** (DEBUG, INFO, WARN, ERROR, CRITICAL)
- **Supressão em produção** de logs não críticos
- **Integração com monitoramento** para logs importantes
- **Filtragem de dados sensíveis** (senhas, tokens, emails, etc.)

#### Uso Recomendado:
```typescript
import { logger } from '@/utils/secureLogger';

// Em vez de console.log
logger.debug('Debug info', { component: 'MyComponent' });

// Em vez de console.error
logger.error('API Error', error, { action: 'fetchData' });

// Para erros críticos
logger.critical('System failure', error, { userId: 'user123' });
```

### 3. Configuração ESLint Atualizada
Adicionadas regras para prevenir logs acidentais:
```javascript
"no-console": ["error", { "allow": [] }],
"no-debugger": "error",
"no-alert": "error"
```

## 📝 Diretrizes para Desenvolvedores

### ❌ NÃO FAZER
```typescript
// NUNCA usar console.log em produção
console.log('User data:', userData);
console.error('API Error:', error);
console.warn('Warning:', sensitiveInfo);

// NUNCA expor dados sensíveis
console.log('Token:', accessToken);
console.log('Password:', userPassword);
console.log('User email:', user.email);
```

### ✅ FAZER
```typescript
// Usar o sistema de logging seguro
import { logger } from '@/utils/secureLogger';

// Para debugging em desenvolvimento
logger.debug('Component mounted', { component: 'UserProfile' });

// Para erros que precisam ser monitorados
logger.error('Failed to load user data', error, { 
  action: 'loadUserProfile',
  component: 'UserProfile'
});

// Para erros críticos do sistema
logger.critical('Database connection failed', error);
```

### 🔒 Campos Sensíveis (Automaticamente Filtrados)
- `password`, `token`, `accessToken`, `refreshToken`
- `secret`, `key`, `hash`
- `email`, `cpf`, `cnpj`, `phone`
- `address`, `creditCard`, `bankAccount`

## 🛠️ Ferramentas de Prevenção

### 1. ESLint
- Configurado para bloquear `console.*`
- Erro ao tentar usar `debugger`
- Aviso para `alert()`

### 2. Pre-commit Hooks (Recomendado)
```bash
# Adicionar ao package.json
"husky": {
  "hooks": {
    "pre-commit": "lint-staged"
  }
}

"lint-staged": {
  "*.{ts,tsx}": [
    "eslint --fix",
    "grep -L 'console\\.' || (echo 'Console logs found!' && exit 1)"
  ]
}
```

### 3. Code Review Checklist
- [ ] Nenhum `console.log/error/warn` presente
- [ ] Uso do `secureLogger` quando necessário
- [ ] Dados sensíveis não expostos
- [ ] Logs críticos direcionados ao monitoramento

## 🔍 Monitoramento e Auditoria

### Logs Permitidos em Produção
1. **Erros críticos** - Via sistema de monitoramento
2. **Métricas de performance** - Dados agregados
3. **Eventos de segurança** - Tentativas de acesso

### Logs Proibidos em Produção
1. **Dados de usuários** - Informações pessoais
2. **Credenciais** - Tokens, senhas, chaves
3. **Estruturas internas** - Caminhos, configurações
4. **Debug info** - Estados internos, variáveis

## 📊 Relatório de Implementação

### Estatísticas
- **54 arquivos analisados**
- **33 arquivos modificados**
- **150+ declarações de console removidas**
- **1 sistema de logging seguro criado**
- **Regras ESLint atualizadas**

### Benefícios Alcançados
- ✅ Eliminação de exposição de dados sensíveis
- ✅ Conformidade com práticas de segurança
- ✅ Prevenção de logs acidentais futuros
- ✅ Sistema de monitoramento integrado
- ✅ Melhor experiência para desenvolvedores

## 🚀 Próximos Passos

1. **Implementar pre-commit hooks**
2. **Treinar equipe** nas novas diretrizes
3. **Monitorar compliance** via code reviews
4. **Expandir sistema de monitoramento**
5. **Auditoria periódica** de logs

## 📞 Contato

Para dúvidas sobre estas diretrizes, consulte a equipe de segurança ou abra uma issue no repositório.

---

**Última atualização**: Janeiro 2025  
**Versão**: 1.0  
**Status**: Implementado
