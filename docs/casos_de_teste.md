# Casos de Teste - Sistema de Gestão Financeira FluxoMax

## Índice
1. [Sistema de Filtro Global](#1-sistema-de-filtro-global)
2. [Dashboard](#2-dashboard)
3. [<PERSON><PERSON> a <PERSON>](#3-contas-a-pagar)
4. [Contas a Receber](#4-contas-a-receber)
5. [Sistema de Calendário Dual](#5-sistema-de-calendário-dual)
6. [Formulários e Transações](#6-formulários-e-transações)
7. [Autenticação e Segurança](#7-autenticação-e-segurança)
8. [Gerenciamento de Entidades](#8-gerenciamento-de-entidades)

---

## 1. Sistema de Filtro Global

### TC001 - Filtro de Empresa - Seleção Válida
**Descrição:** Verificar se o filtro de empresa funciona corretamente ao selecionar uma empresa válida.

**Condições Prévias:**
- <PERSON>u<PERSON><PERSON> autenticado
- Usuário possui acesso a múltiplas empresas
- Sistema carregado na página inicial

**Etapas:**
1. Acessar o header da aplicação
2. Clicar no seletor de empresa
3. Selecionar uma empresa da lista
4. Verificar se todos os dados são atualizados

**Resultado Esperado:**
- Lista de empresas é exibida
- Empresa selecionada é destacada
- Todos os dados nas páginas são filtrados pela empresa selecionada
- Dashboard atualiza com dados da empresa selecionada

**Dados de Teste:**
- Empresa A: "Tech Solutions LTDA"
- Empresa B: "Agro Fazenda S.A."

### TC002 - Filtro de Empresa - Usuário Sem Permissão
**Descrição:** Verificar comportamento quando usuário tenta acessar empresa sem permissão.

**Condições Prévias:**
- Usuário autenticado
- Usuário possui acesso limitado a empresas
- Tentativa de acesso via URL ou manipulação

**Etapas:**
1. Tentar acessar empresa não autorizada
2. Verificar resposta do sistema

**Resultado Esperado:**
- Acesso negado
- Redirecionamento para empresa padrão
- Mensagem de erro apropriada

**Dados de Teste:**
- ID empresa não autorizada: 999
- Usuário com permissão apenas para empresa ID: 1

### TC003 - Filtro de Período - Seleção Padrão
**Descrição:** Verificar funcionamento dos períodos padrão do sistema.

**Condições Prévias:**
- Usuário autenticado
- Empresa com calendário padrão selecionada
- Sistema na página inicial

**Etapas:**
1. Clicar no seletor de período
2. Selecionar "Este Mês"
3. Verificar dados exibidos
4. Repetir para outros períodos: "Mês Anterior", "Este Ano", "Hoje"

**Resultado Esperado:**
- Dados filtrados corretamente para cada período
- Datas de início e fim calculadas corretamente
- Todas as páginas respeitam o filtro aplicado

**Dados de Teste:**
- Data atual: 15/03/2024
- Este Mês: 01/03/2024 a 31/03/2024
- Mês Anterior: 01/02/2024 a 29/02/2024

### TC004 - Filtro de Período - Período Personalizado
**Descrição:** Verificar funcionamento de períodos personalizados para empresas agrícolas.

**Condições Prévias:**
- Usuário autenticado
- Empresa com calendário periódico selecionada
- Períodos customizados cadastrados

**Etapas:**
1. Selecionar empresa com calendário periódico
2. Verificar se períodos customizados aparecem no filtro
3. Selecionar um período customizado
4. Verificar se dados são filtrados corretamente

**Resultado Esperado:**
- Períodos customizados listados no seletor
- Dados filtrados pelo intervalo do período selecionado
- Header exibe nome do período selecionado

**Dados de Teste:**
- Período: "Safra 2024/2025"
- Data início: 01/09/2024
- Data fim: 31/08/2025

### TC005 - Filtro Avançado - Sobreposição
**Descrição:** Verificar comportamento quando filtro avançado sobrepõe filtro global.

**Condições Prévias:**
- Usuário na página de Contas a Pagar
- Filtro global ativo

**Etapas:**
1. Aplicar filtro avançado de período
2. Verificar header
3. Navegar para outras páginas
4. Retornar à página de Contas a Pagar

**Resultado Esperado:**
- Header exibe "Filtro Avançado"
- Outras páginas mantêm filtro global
- Página de Contas a Pagar mantém filtro avançado

**Dados de Teste:**
- Filtro global: "Este Mês"
- Filtro avançado: "Últimos 6 Meses"

---

## 2. Dashboard

### TC006 - Cards de Resumo - Cálculo Correto
**Descrição:** Verificar se os cards de resumo exibem valores corretos.

**Condições Prévias:**
- Usuário autenticado
- Empresa com transações cadastradas
- Período definido

**Etapas:**
1. Acessar dashboard
2. Verificar valores nos cards:
   - Saldo Total
   - A Receber
   - A Pagar
   - Saldo Líquido

**Resultado Esperado:**
- Valores calculados corretamente
- Indicadores de variação exibidos
- Formatação monetária brasileira (R$)

**Dados de Teste:**
- Saldo em conta: R$ 10.000,00
- A Receber: R$ 5.000,00
- A Pagar: R$ 3.000,00
- Saldo Líquido esperado: R$ 12.000,00

### TC007 - Gráfico de Fluxo de Caixa - Dados Válidos
**Descrição:** Verificar se o gráfico de fluxo de caixa exibe dados corretos.

**Condições Prévias:**
- Transações de receita e despesa no período
- Dashboard carregado

**Etapas:**
1. Verificar se gráfico é exibido
2. Validar dados de receitas (barras verdes)
3. Validar dados de despesas (barras vermelhas)
4. Verificar legendas e tooltips

**Resultado Esperado:**
- Gráfico renderizado corretamente
- Barras proporcionais aos valores
- Cores diferenciadas para receitas e despesas
- Tooltips com valores formatados

**Dados de Teste:**
- Janeiro: Receitas R$ 15.000, Despesas R$ 8.000
- Fevereiro: Receitas R$ 12.000, Despesas R$ 10.000

### TC008 - Gráfico de Pizza - Distribuição por Categoria
**Descrição:** Verificar distribuição de despesas por categoria no gráfico de pizza.

**Condições Prévias:**
- Despesas cadastradas com diferentes categorias
- Dashboard carregado

**Etapas:**
1. Verificar renderização do gráfico de pizza
2. Validar proporções das fatias
3. Verificar legendas das categorias
4. Testar hover sobre fatias

**Resultado Esperado:**
- Fatias proporcionais aos valores por categoria
- Legendas corretas
- Cores distintas para cada categoria
- Percentuais calculados corretamente

**Dados de Teste:**
- Alimentação: R$ 2.000 (40%)
- Transporte: R$ 1.500 (30%)
- Serviços: R$ 1.000 (20%)
- Outros: R$ 500 (10%)

### TC009 - Dashboard - Sem Dados
**Descrição:** Verificar comportamento do dashboard quando não há dados.

**Condições Prévias:**
- Empresa sem transações
- Período selecionado sem movimentações

**Etapas:**
1. Acessar dashboard
2. Verificar exibição dos cards
3. Verificar gráficos
4. Verificar lista de transações

**Resultado Esperado:**
- Cards exibem valores zerados
- Gráficos mostram estado vazio
- Mensagem informativa sobre ausência de dados
- Lista de transações vazia com mensagem apropriada

**Dados de Teste:**
- Empresa nova sem transações
- Período: "Este Mês"

### TC010 - Dashboard - Performance com Muitos Dados
**Descrição:** Verificar performance do dashboard com grande volume de dados.

**Condições Prévias:**
- Empresa com mais de 10.000 transações
- Período abrangente selecionado

**Etapas:**
1. Selecionar período "Este Ano"
2. Acessar dashboard
3. Medir tempo de carregamento
4. Verificar responsividade da interface

**Resultado Esperado:**
- Carregamento em menos de 3 segundos
- Interface responsiva
- Dados agregados corretamente
- Gráficos renderizados sem travamentos

**Dados de Teste:**
- 15.000 transações no ano
- Período: 01/01/2024 a 31/12/2024

---

## 3. Contas a Pagar

### TC011 - Listagem de Contas a Pagar - Exibição Padrão
**Descrição:** Verificar se a listagem de contas a pagar exibe informações corretas.

**Condições Prévias:**
- Usuário autenticado
- Contas a pagar cadastradas
- Página de Contas a Pagar acessada

**Etapas:**
1. Acessar página de Contas a Pagar
2. Verificar colunas exibidas
3. Verificar ordenação padrão
4. Verificar status das contas

**Resultado Esperado:**
- Colunas: Descrição, Fornecedor, Vencimento, Valor, Status
- Ordenação por data de vencimento (mais próximas primeiro)
- Status coloridos: Pendente (amarelo), Pago (verde), Atrasado (vermelho), Parcial (azul)

**Dados de Teste:**
- Conta 1: "Energia Elétrica", Fornecedor "CEMIG", Vencimento: 15/03/2024, Valor: R$ 450,00, Status: Pendente
- Conta 2: "Internet", Fornecedor "NET", Vencimento: 10/03/2024, Valor: R$ 120,00, Status: Atrasado

### TC012 - Filtro de Pesquisa - Busca por Descrição
**Descrição:** Verificar funcionamento do filtro de pesquisa por descrição.

**Condições Prévias:**
- Múltiplas contas a pagar cadastradas
- Página de Contas a Pagar carregada

**Etapas:**
1. Digitar "energia" no campo de pesquisa
2. Verificar resultados filtrados
3. Limpar pesquisa
4. Digitar termo inexistente

**Resultado Esperado:**
- Apenas contas com "energia" na descrição são exibidas
- Busca é case-insensitive
- Ao limpar, todas as contas retornam
- Termo inexistente exibe lista vazia com mensagem

**Dados de Teste:**
- Termo válido: "energia"
- Termo inexistente: "xyzabc"

### TC013 - Filtro de Status - Múltiplas Seleções
**Descrição:** Verificar filtro por status das contas.

**Condições Prévias:**
- Contas com diferentes status cadastradas
- Página de Contas a Pagar carregada

**Etapas:**
1. Selecionar apenas status "Pendente"
2. Verificar resultados
3. Adicionar status "Atrasado"
4. Verificar resultados combinados

**Resultado Esperado:**
- Apenas contas pendentes exibidas inicialmente
- Ao adicionar "Atrasado", contas pendentes e atrasadas são exibidas
- Contador de resultados atualizado

**Dados de Teste:**
- 5 contas pendentes
- 3 contas atrasadas
- 2 contas pagas
- Resultado esperado: 8 contas (pendentes + atrasadas)

### TC014 - Filtro Avançado de Período - Últimos 6 Meses
**Descrição:** Verificar funcionamento do filtro avançado de período.

**Condições Prévias:**
- Contas a pagar em diferentes períodos
- Data atual conhecida

**Etapas:**
1. Clicar em "Filtro Avançado"
2. Selecionar "Últimos 6 Meses"
3. Aplicar filtro
4. Verificar header global

**Resultado Esperado:**
- Apenas contas dos últimos 6 meses exibidas
- Header exibe "Filtro Avançado"
- Filtro persiste ao navegar na página

**Dados de Teste:**
- Data atual: 15/03/2024
- Período esperado: 15/09/2023 a 15/03/2024

### TC015 - Pagamento Total - Fluxo Completo
**Descrição:** Verificar processo completo de pagamento total de uma conta.

**Condições Prévias:**
- Conta a pagar pendente
- Conta bancária com saldo suficiente
- Usuário com permissões de pagamento

**Etapas:**
1. Clicar em "Pagar" na conta selecionada
2. Verificar valor pré-preenchido
3. Selecionar conta bancária
4. Definir data de pagamento
5. Confirmar pagamento

**Resultado Esperado:**
- Modal de pagamento aberto
- Valor total da conta pré-preenchido
- Lista de contas bancárias disponível
- Após confirmação: status muda para "Pago"
- Transação bancária criada automaticamente

**Dados de Teste:**
- Conta: R$ 500,00
- Conta bancária: "Banco do Brasil - CC"
- Data pagamento: 15/03/2024

### TC016 - Pagamento Parcial - Validação de Valor
**Descrição:** Verificar validações no pagamento parcial.

**Condições Prévias:**
- Conta a pagar de R$ 1.000,00 pendente
- Modal de pagamento parcial aberto

**Etapas:**
1. Tentar pagar R$ 1.200,00 (valor maior que o devido)
2. Verificar mensagem de erro
3. Inserir valor válido de R$ 300,00
4. Confirmar pagamento

**Resultado Esperado:**
- Erro exibido para valor maior que o devido
- Pagamento de R$ 300,00 aceito
- Status muda para "Parcial"
- Saldo pendente: R$ 700,00

**Dados de Teste:**
- Valor original: R$ 1.000,00
- Valor inválido: R$ 1.200,00
- Valor válido: R$ 300,00

### TC017 - Pagamento com Juros e Desconto
**Descrição:** Verificar cálculo de juros e desconto no pagamento.

**Condições Prévias:**
- Conta a pagar de R$ 1.000,00
- Modal de pagamento aberto

**Etapas:**
1. Inserir valor de pagamento: R$ 1.000,00
2. Adicionar juros: R$ 50,00
3. Adicionar desconto: R$ 30,00
4. Verificar valor final calculado
5. Confirmar pagamento

**Resultado Esperado:**
- Valor final calculado: R$ 1.020,00 (1000 + 50 - 30)
- Cálculo atualizado em tempo real
- Transação registrada com valor final

**Dados de Teste:**
- Valor base: R$ 1.000,00
- Juros: R$ 50,00
- Desconto: R$ 30,00
- Valor final: R$ 1.020,00

### TC018 - Contas a Pagar - Limite de Paginação
**Descrição:** Verificar comportamento com muitas contas (teste de limite).

**Condições Prévias:**
- Mais de 1000 contas a pagar cadastradas
- Página de Contas a Pagar acessada

**Etapas:**
1. Verificar carregamento inicial
2. Navegar pelas páginas
3. Testar busca com muitos resultados
4. Verificar performance

**Resultado Esperado:**
- Paginação funcional
- Carregamento rápido (< 2 segundos por página)
- Busca responsiva
- Indicadores de carregamento apropriados

**Dados de Teste:**
- 1500 contas cadastradas
- 50 contas por página
- 30 páginas total

---

## 4. Contas a Receber

### TC019 - Listagem de Contas a Receber - Exibição Padrão
**Descrição:** Verificar se a listagem de contas a receber exibe informações corretas.

**Condições Prévias:**
- Usuário autenticado
- Contas a receber cadastradas
- Página de Contas a Receber acessada

**Etapas:**
1. Acessar página de Contas a Receber
2. Verificar colunas exibidas
3. Verificar ordenação padrão
4. Verificar status das contas

**Resultado Esperado:**
- Colunas: Descrição, Cliente, Vencimento, Valor, Status
- Ordenação por data de vencimento
- Status coloridos: Pendente, Recebido, Atrasado, Parcial

**Dados de Teste:**
- Conta 1: "Venda de Produtos", Cliente "João Silva", Vencimento: 20/03/2024, Valor: R$ 2.500,00, Status: Pendente

### TC020 - Recebimento Total - Fluxo Completo
**Descrição:** Verificar processo completo de recebimento total de uma conta.

**Condições Prévias:**
- Conta a receber pendente
- Conta bancária disponível
- Usuário com permissões de recebimento

**Etapas:**
1. Clicar em "Receber" na conta selecionada
2. Verificar valor pré-preenchido
3. Selecionar conta bancária
4. Definir data de recebimento
5. Confirmar recebimento

**Resultado Esperado:**
- Modal de recebimento aberto
- Valor total da conta pré-preenchido
- Após confirmação: status muda para "Recebido"
- Transação bancária de crédito criada

**Dados de Teste:**
- Conta: R$ 2.500,00
- Conta bancária: "Itaú - Conta Corrente"
- Data recebimento: 20/03/2024

### TC021 - Recebimento Parcial - Múltiplos Pagamentos
**Descrição:** Verificar recebimento parcial em múltiplas etapas.

**Condições Prévias:**
- Conta a receber de R$ 3.000,00 pendente

**Etapas:**
1. Receber R$ 1.000,00 (primeira parcela)
2. Verificar status "Parcial"
3. Receber R$ 1.500,00 (segunda parcela)
4. Verificar saldo pendente
5. Receber R$ 500,00 (valor final)

**Resultado Esperado:**
- Após 1ª parcela: Status "Parcial", Saldo R$ 2.000,00
- Após 2ª parcela: Status "Parcial", Saldo R$ 500,00
- Após 3ª parcela: Status "Recebido", Saldo R$ 0,00

**Dados de Teste:**
- Valor total: R$ 3.000,00
- Parcelas: R$ 1.000,00 + R$ 1.500,00 + R$ 500,00

### TC022 - Filtro por Cliente - Busca Específica
**Descrição:** Verificar filtro de pesquisa por nome do cliente.

**Condições Prévias:**
- Contas a receber de diferentes clientes
- Página carregada

**Etapas:**
1. Digitar "João" no campo de pesquisa
2. Verificar resultados
3. Digitar nome completo "João Silva"
4. Verificar refinamento

**Resultado Esperado:**
- Busca por "João" retorna todas as contas de clientes com "João" no nome
- Busca por "João Silva" retorna apenas contas específicas deste cliente
- Busca é case-insensitive

**Dados de Teste:**
- Cliente 1: "João Silva" (3 contas)
- Cliente 2: "João Santos" (2 contas)
- Cliente 3: "Maria João" (1 conta)

### TC023 - Contas Atrasadas - Identificação Automática
**Descrição:** Verificar identificação automática de contas atrasadas.

**Condições Prévias:**
- Contas com vencimento anterior à data atual
- Sistema com data atual configurada

**Etapas:**
1. Verificar contas com vencimento passado
2. Confirmar status "Atrasado"
3. Verificar destaque visual
4. Verificar ordenação (atrasadas primeiro)

**Resultado Esperado:**
- Contas vencidas automaticamente marcadas como "Atrasadas"
- Cor vermelha ou destaque visual
- Aparecem no topo da lista

**Dados de Teste:**
- Data atual: 15/03/2024
- Conta vencida: Vencimento 10/03/2024
- Conta futura: Vencimento 20/03/2024

---

## 5. Sistema de Calendário Dual

### TC024 - Configuração de Calendário - Empresa Padrão
**Descrição:** Verificar configuração de calendário padrão para empresa comercial.

**Condições Prévias:**
- Usuário administrador
- Página de configurações da empresa

**Etapas:**
1. Acessar configurações da empresa
2. Selecionar tipo de calendário "Padrão"
3. Salvar configuração
4. Verificar filtros disponíveis no header

**Resultado Esperado:**
- Configuração salva com sucesso
- Filtros padrão disponíveis: "Este Mês", "Mês Anterior", etc.
- Campo `calendar_type` = "standard" no banco

**Dados de Teste:**
- Empresa: "Tech Solutions LTDA"
- Tipo: "Padrão"

### TC025 - Configuração de Calendário - Empresa Periódica
**Descrição:** Verificar configuração de calendário periódico para empresa agrícola.

**Condições Prévias:**
- Usuário administrador
- Página de configurações da empresa

**Etapas:**
1. Acessar configurações da empresa
2. Selecionar tipo de calendário "Periódico"
3. Salvar configuração
4. Acessar gerenciamento de períodos
5. Cadastrar período customizado

**Resultado Esperado:**
- Configuração salva com sucesso
- Interface de gerenciamento de períodos disponível
- Campo `calendar_type` = "periodic" no banco

**Dados de Teste:**
- Empresa: "Agro Fazenda S.A."
- Tipo: "Periódico"

### TC026 - Gerenciamento de Períodos - Cadastro
**Descrição:** Verificar cadastro de período customizado.

**Condições Prévias:**
- Empresa com calendário periódico
- Interface de gerenciamento de períodos

**Etapas:**
1. Clicar em "Novo Período"
2. Preencher nome: "Safra 2024/2025"
3. Definir data início: 01/09/2024
4. Definir data fim: 31/08/2025
5. Salvar período

**Resultado Esperado:**
- Período cadastrado com sucesso
- Aparece na lista de períodos
- Disponível no filtro global

**Dados de Teste:**
- Nome: "Safra 2024/2025"
- Início: 01/09/2024
- Fim: 31/08/2025

### TC027 - Transição entre Tipos de Calendário
**Descrição:** Verificar comportamento ao alternar entre empresas com diferentes tipos de calendário.

**Condições Prévias:**
- Empresa A com calendário padrão
- Empresa B com calendário periódico
- Usuário com acesso a ambas

**Etapas:**
1. Selecionar Empresa A (padrão)
2. Verificar filtros disponíveis
3. Selecionar "Este Mês"
4. Alternar para Empresa B (periódico)
5. Verificar filtros disponíveis

**Resultado Esperado:**
- Empresa A: Filtros padrão disponíveis
- Ao alternar para Empresa B: Períodos customizados carregados
- Primeiro período customizado selecionado automaticamente
- Dados filtrados corretamente

**Dados de Teste:**
- Empresa A: "Tech Solutions" (padrão)
- Empresa B: "Agro Fazenda" (periódico)

### TC028 - Validação de Períodos - Datas Sobrepostas
**Descrição:** Verificar validação ao cadastrar períodos com datas sobrepostas.

**Condições Prévias:**
- Período existente: 01/09/2024 a 31/08/2025
- Interface de cadastro de novo período

**Etapas:**
1. Tentar cadastrar período: 01/06/2024 a 30/11/2024
2. Verificar mensagem de erro
3. Ajustar datas para não sobrepor
4. Salvar período válido

**Resultado Esperado:**
- Erro exibido para datas sobrepostas
- Mensagem clara sobre o conflito
- Período válido aceito após correção

**Dados de Teste:**
- Período existente: 01/09/2024 a 31/08/2025
- Período inválido: 01/06/2024 a 30/11/2024
- Período válido: 01/06/2024 a 31/08/2024

### TC029 - Períodos - Limite de Cadastro
**Descrição:** Verificar comportamento com muitos períodos cadastrados.

**Condições Prévias:**
- Empresa com calendário periódico
- Interface de gerenciamento

**Etapas:**
1. Cadastrar 50 períodos diferentes
2. Verificar performance da listagem
3. Verificar filtro global
4. Testar busca de períodos

**Resultado Esperado:**
- Sistema suporta múltiplos períodos
- Listagem paginada se necessário
- Filtro global responsivo
- Busca funcional

**Dados de Teste:**
- 50 períodos com nomes sequenciais
- Datas não sobrepostas

---

## 6. Formulários e Transações

### TC030 - Formulário de Nova Despesa - Campos Obrigatórios
**Descrição:** Verificar validação de campos obrigatórios no formulário de despesa.

**Condições Prévias:**
- Usuário na página de nova despesa
- Formulário carregado

**Etapas:**
1. Tentar salvar formulário vazio
2. Verificar mensagens de erro
3. Preencher apenas descrição
4. Tentar salvar novamente
5. Preencher todos os campos obrigatórios

**Resultado Esperado:**
- Formulário vazio: Múltiplos erros de validação
- Apenas descrição: Erros para outros campos obrigatórios
- Todos os campos: Formulário aceito

**Dados de Teste:**
- Campos obrigatórios: Descrição, Fornecedor, Valor, Data de Vencimento, Categoria, Conta

### TC031 - Formulário de Despesa - Formatação Monetária
**Descrição:** Verificar formatação automática do campo de valor.

**Condições Prévias:**
- Formulário de nova despesa aberto
- Campo de valor em foco

**Etapas:**
1. Digitar "1000"
2. Verificar formatação automática
3. Digitar "1000.50"
4. Verificar formatação
5. Tentar digitar texto inválido

**Resultado Esperado:**
- "1000" formatado para "R$ 1.000,00"
- "1000.50" formatado para "R$ 1.000,50"
- Texto inválido rejeitado ou removido

**Dados de Teste:**
- Entrada: "1000" → Saída: "R$ 1.000,00"
- Entrada: "1000.50" → Saída: "R$ 1.000,50"
- Entrada inválida: "abc123"

### TC032 - Parcelamento de Despesa - Cálculo de Datas
**Descrição:** Verificar cálculo correto das datas de vencimento no parcelamento.

**Condições Prévias:**
- Formulário de nova despesa aberto
- Opção "Dividir em Parcelas" ativada

**Etapas:**
1. Ativar "Dividir em Parcelas"
2. Definir 6 parcelas
3. Selecionar frequência "Mensal"
4. Definir primeira parcela para 15/03/2024
5. Verificar cálculo das datas

**Resultado Esperado:**
- Parcela 1: 15/03/2024
- Parcela 2: 15/04/2024
- Parcela 3: 15/05/2024
- Parcela 4: 15/06/2024
- Parcela 5: 15/07/2024
- Parcela 6: 15/08/2024

**Dados de Teste:**
- Valor total: R$ 1.200,00
- Parcelas: 6x R$ 200,00
- Frequência: Mensal

### TC033 - Parcelamento - Frequência Quinzenal
**Descrição:** Verificar cálculo de parcelas quinzenais.

**Condições Prévias:**
- Formulário de despesa com parcelamento ativo

**Etapas:**
1. Definir 4 parcelas quinzenais
2. Data primeira parcela: 01/03/2024
3. Verificar datas calculadas

**Resultado Esperado:**
- Parcela 1: 01/03/2024
- Parcela 2: 16/03/2024
- Parcela 3: 31/03/2024
- Parcela 4: 15/04/2024

**Dados de Teste:**
- Valor: R$ 800,00
- Parcelas: 4x R$ 200,00

### TC034 - Parcelamento - Limite Máximo
**Descrição:** Verificar limite máximo de parcelas (36).

**Condições Prévias:**
- Formulário de despesa aberto

**Etapas:**
1. Tentar definir 50 parcelas
2. Verificar validação
3. Definir 36 parcelas (máximo)
4. Verificar aceitação

**Resultado Esperado:**
- Erro para mais de 36 parcelas
- 36 parcelas aceitas
- Cálculo correto para todas as parcelas

**Dados de Teste:**
- Valor: R$ 3.600,00
- Parcelas: 36x R$ 100,00

### TC035 - Formulário de Receita - Autocomplete de Cliente
**Descrição:** Verificar funcionamento do autocomplete no campo cliente.

**Condições Prévias:**
- Clientes cadastrados no sistema
- Formulário de nova receita aberto

**Etapas:**
1. Digitar "João" no campo cliente
2. Verificar sugestões exibidas
3. Selecionar cliente da lista
4. Verificar preenchimento automático

**Resultado Esperado:**
- Lista de clientes com "João" no nome
- Seleção preenche campo corretamente
- Dados do cliente carregados

**Dados de Teste:**
- Clientes: "João Silva", "João Santos", "Maria João"
- Busca: "João"

### TC036 - Marcar como Paga no Cadastro
**Descrição:** Verificar funcionalidade de marcar despesa como paga durante o cadastro.

**Condições Prévias:**
- Formulário de nova despesa preenchido
- Checkbox "Marcar como Paga" disponível

**Etapas:**
1. Preencher todos os campos obrigatórios
2. Marcar checkbox "Marcar como Paga"
3. Selecionar conta bancária
4. Salvar despesa

**Resultado Esperado:**
- Despesa criada com status "Pago"
- Transação bancária de débito criada automaticamente
- Saldo da conta bancária atualizado

**Dados de Teste:**
- Despesa: R$ 500,00
- Conta: "Banco do Brasil - CC"
- Saldo anterior: R$ 2.000,00
- Saldo após: R$ 1.500,00

### TC037 - Validação de Valores - Limite Mínimo
**Descrição:** Verificar validação de valores mínimos nos formulários.

**Condições Prévias:**
- Formulário de despesa ou receita aberto

**Etapas:**
1. Tentar inserir valor R$ 0,00
2. Verificar mensagem de erro
3. Inserir valor negativo
4. Verificar validação
5. Inserir valor válido mínimo

**Resultado Esperado:**
- Valor zero rejeitado
- Valor negativo rejeitado
- Valor mínimo R$ 0,01 aceito

**Dados de Teste:**
- Valor inválido: R$ 0,00
- Valor inválido: R$ -100,00
- Valor válido: R$ 0,01

### TC038 - Formulário - Valores Extremos
**Descrição:** Verificar comportamento com valores muito altos.

**Condições Prévias:**
- Formulário de transação aberto

**Etapas:**
1. Inserir valor R$ 999.999.999,99
2. Verificar formatação
3. Tentar salvar
4. Verificar processamento

**Resultado Esperado:**
- Valor formatado corretamente
- Sistema aceita valor alto
- Cálculos mantêm precisão

**Dados de Teste:**
- Valor extremo: R$ 999.999.999,99
- Formatação: "R$ 999.999.999,99"

---

## 7. Autenticação e Segurança

### TC039 - Login - Credenciais Válidas
**Descrição:** Verificar processo de login com credenciais corretas.

**Condições Prévias:**
- Usuário cadastrado no sistema
- Página de login carregada

**Etapas:**
1. Inserir email válido
2. Inserir senha correta
3. Clicar em "Entrar"
4. Verificar redirecionamento

**Resultado Esperado:**
- Login realizado com sucesso
- Redirecionamento para dashboard
- Token JWT gerado
- Sessão ativa

**Dados de Teste:**
- Email: "<EMAIL>"
- Senha: "Admin123"

### TC040 - Login - Credenciais Inválidas
**Descrição:** Verificar comportamento com credenciais incorretas.

**Condições Prévias:**
- Página de login carregada

**Etapas:**
1. Inserir email válido
2. Inserir senha incorreta
3. Tentar fazer login
4. Verificar mensagem de erro

**Resultado Esperado:**
- Login rejeitado
- Mensagem de erro clara
- Usuário permanece na tela de login
- Campos limpos ou mantidos

**Dados de Teste:**
- Email: "<EMAIL>"
- Senha incorreta: "senhaerrada"

### TC041 - Controle de Acesso - Página Restrita
**Descrição:** Verificar controle de acesso a páginas restritas.

**Condições Prévias:**
- Usuário não autenticado
- Tentativa de acesso direto via URL

**Etapas:**
1. Tentar acessar "/dashboard" sem login
2. Verificar redirecionamento
3. Fazer login
4. Tentar acessar novamente

**Resultado Esperado:**
- Redirecionamento para página de login
- Após login: acesso permitido
- URL original preservada para redirecionamento

**Dados de Teste:**
- URL restrita: "/dashboard"
- URL de login: "/login"

### TC042 - Permissões por Empresa
**Descrição:** Verificar segregação de dados por empresa.

**Condições Prévias:**
- Usuário com acesso apenas à Empresa A
- Dados existentes na Empresa B

**Etapas:**
1. Fazer login
2. Verificar dados exibidos
3. Tentar acessar dados da Empresa B via API
4. Verificar resposta

**Resultado Esperado:**
- Apenas dados da Empresa A visíveis
- Acesso negado para dados da Empresa B
- Erro 403 (Forbidden) na API

**Dados de Teste:**
- Usuário: acesso apenas Empresa ID 1
- Tentativa: acessar dados Empresa ID 2

### TC043 - Sessão Expirada
**Descrição:** Verificar comportamento quando sessão expira.

**Condições Prévias:**
- Usuário logado
- Token JWT próximo do vencimento

**Etapas:**
1. Aguardar expiração do token
2. Tentar realizar ação no sistema
3. Verificar resposta
4. Verificar redirecionamento

**Resultado Esperado:**
- Ação rejeitada
- Mensagem sobre sessão expirada
- Redirecionamento para login
- Dados não perdidos (se possível)

**Dados de Teste:**
- Token com expiração de 1 hora
- Ação: salvar transação

### TC044 - Refresh Token
**Descrição:** Verificar funcionamento do refresh token.

**Condições Prévias:**
- Usuário logado
- Refresh token válido
- Access token próximo do vencimento

**Etapas:**
1. Realizar ação que requer autenticação
2. Verificar renovação automática do token
3. Confirmar continuidade da sessão

**Resultado Esperado:**
- Token renovado automaticamente
- Usuário não percebe interrupção
- Ação executada com sucesso

**Dados de Teste:**
- Access token: 15 min de validade
- Refresh token: 7 dias de validade

### TC045 - Logout
**Descrição:** Verificar processo de logout.

**Condições Prévias:**
- Usuário autenticado
- Sessão ativa

**Etapas:**
1. Clicar em "Sair" ou "Logout"
2. Verificar limpeza da sessão
3. Tentar acessar página restrita
4. Verificar redirecionamento

**Resultado Esperado:**
- Sessão encerrada
- Tokens removidos
- Redirecionamento para login
- Acesso negado a páginas restritas

**Dados de Teste:**
- Usuário logado: "<EMAIL>"

### TC046 - Tentativas de Login - Limite
**Descrição:** Verificar proteção contra ataques de força bruta.

**Condições Prévias:**
- Página de login carregada
- Limite configurado (ex: 5 tentativas)

**Etapas:**
1. Realizar 5 tentativas de login com senha incorreta
2. Verificar bloqueio temporário
3. Aguardar período de desbloqueio
4. Tentar login novamente

**Resultado Esperado:**
- Após 5 tentativas: conta temporariamente bloqueada
- Mensagem informando sobre bloqueio
- Após período: login normal restaurado

**Dados de Teste:**
- Email: "<EMAIL>"
- Tentativas: 5x senha incorreta
- Bloqueio: 15 minutos

---

## 8. Gerenciamento de Entidades

### TC047 - Cadastro de Fornecedor - Dados Válidos
**Descrição:** Verificar cadastro de novo fornecedor com dados válidos.

**Condições Prévias:**
- Usuário autenticado
- Página de cadastro de fornecedor

**Etapas:**
1. Preencher nome: "Fornecedor ABC LTDA"
2. Preencher CNPJ: "12.345.678/0001-90"
3. Preencher email: "<EMAIL>"
4. Preencher telefone: "(11) 99999-9999"
5. Salvar fornecedor

**Resultado Esperado:**
- Fornecedor cadastrado com sucesso
- Dados salvos corretamente
- Disponível para seleção em formulários
- Mensagem de confirmação exibida

**Dados de Teste:**
- Nome: "Fornecedor ABC LTDA"
- CNPJ: "12.345.678/0001-90"
- Email: "<EMAIL>"
- Telefone: "(11) 99999-9999"

### TC048 - Cadastro de Cliente - CNPJ Duplicado
**Descrição:** Verificar validação de CNPJ duplicado.

**Condições Prévias:**
- Cliente existente com CNPJ "12.345.678/0001-90"
- Formulário de novo cliente

**Etapas:**
1. Preencher dados do novo cliente
2. Inserir CNPJ já existente
3. Tentar salvar
4. Verificar mensagem de erro

**Resultado Esperado:**
- Erro de CNPJ duplicado
- Cadastro não realizado
- Mensagem clara sobre duplicação
- Campo CNPJ destacado

**Dados de Teste:**
- CNPJ existente: "12.345.678/0001-90"
- Novo cliente: "Cliente XYZ LTDA"

### TC049 - Validação de CNPJ - Formato Inválido
**Descrição:** Verificar validação de formato de CNPJ.

**Condições Prévias:**
- Formulário de cadastro de entidade

**Etapas:**
1. Inserir CNPJ com formato inválido
2. Verificar validação em tempo real
3. Inserir CNPJ com dígitos verificadores incorretos
4. Tentar salvar

**Resultado Esperado:**
- Erro para formato inválido
- Erro para dígitos verificadores incorretos
- Formatação automática aplicada
- Cadastro bloqueado até correção

**Dados de Teste:**
- CNPJ inválido: "123.456.789/0001-00"
- CNPJ formato errado: "12345678000190"

### TC050 - Edição de Entidade - Atualização
**Descrição:** Verificar edição de dados de entidade existente.

**Condições Prévias:**
- Entidade cadastrada
- Página de edição acessada

**Etapas:**
1. Alterar nome da entidade
2. Atualizar telefone
3. Salvar alterações
4. Verificar atualização em formulários

**Resultado Esperado:**
- Dados atualizados com sucesso
- Alterações refletidas em toda aplicação
- Histórico de alterações mantido (se aplicável)

**Dados de Teste:**
- Nome original: "Fornecedor ABC"
- Nome novo: "Fornecedor ABC LTDA"
- Telefone novo: "(11) 88888-8888"

### TC051 - Exclusão de Entidade - Com Transações
**Descrição:** Verificar comportamento ao tentar excluir entidade com transações vinculadas.

**Condições Prévias:**
- Entidade com transações associadas
- Página de gerenciamento de entidades

**Etapas:**
1. Tentar excluir entidade
2. Verificar mensagem de aviso
3. Confirmar ou cancelar exclusão

**Resultado Esperado:**
- Aviso sobre transações vinculadas
- Opções: cancelar ou inativar entidade
- Exclusão física bloqueada
- Exclusão lógica permitida

**Dados de Teste:**
- Entidade: "Fornecedor XYZ"
- Transações vinculadas: 5 contas a pagar

### TC052 - Busca de Entidades - Filtro por Tipo
**Descrição:** Verificar filtro de entidades por tipo (cliente/fornecedor).

**Condições Prévias:**
- Clientes e fornecedores cadastrados
- Página de listagem de entidades

**Etapas:**
1. Aplicar filtro "Apenas Clientes"
2. Verificar resultados
3. Aplicar filtro "Apenas Fornecedores"
4. Verificar resultados
5. Remover filtros

**Resultado Esperado:**
- Filtro "Clientes": apenas clientes exibidos
- Filtro "Fornecedores": apenas fornecedores exibidos
- Sem filtro: todas as entidades exibidas

**Dados de Teste:**
- 10 clientes cadastrados
- 8 fornecedores cadastrados
- 3 entidades que são ambos (cliente e fornecedor)

### TC053 - Entidade - Campos Opcionais
**Descrição:** Verificar cadastro com apenas campos obrigatórios.

**Condições Prévias:**
- Formulário de nova entidade

**Etapas:**
1. Preencher apenas campos obrigatórios
2. Deixar campos opcionais vazios
3. Salvar entidade
4. Verificar cadastro

**Resultado Esperado:**
- Cadastro realizado com sucesso
- Campos opcionais salvos como nulos/vazios
- Entidade disponível para uso

**Dados de Teste:**
- Obrigatórios: Nome, Tipo
- Opcionais: CNPJ, Email, Telefone, Endereço

### TC054 - Autocomplete - Performance
**Descrição:** Verificar performance do autocomplete com muitas entidades.

**Condições Prévias:**
- Mais de 1000 entidades cadastradas
- Formulário com campo de entidade

**Etapas:**
1. Digitar caracteres no campo
2. Medir tempo de resposta
3. Verificar relevância dos resultados
4. Testar com diferentes termos

**Resultado Esperado:**
- Resposta em menos de 500ms
- Máximo 10 resultados exibidos
- Resultados ordenados por relevância
- Busca funciona por nome e documento

**Dados de Teste:**
- 1500 entidades cadastradas
- Busca: "João" (50 resultados possíveis)
- Busca: "123" (busca por CNPJ)

---

## 9. Casos Extremos e Stress Tests

### TC055 - Sistema - Múltiplos Usuários Simultâneos
**Descrição:** Verificar comportamento com múltiplos usuários simultâneos.

**Condições Prévias:**
- 50 usuários diferentes
- Acesso simultâneo ao sistema

**Etapas:**
1. 50 usuários fazem login simultaneamente
2. Todos acessam dashboard
3. Realizam transações ao mesmo tempo
4. Verificar performance e consistência

**Resultado Esperado:**
- Sistema mantém performance
- Dados consistentes para cada usuário
- Sem conflitos de concorrência
- Tempo de resposta aceitável

**Dados de Teste:**
- 50 usuários simultâneos
- 10 empresas diferentes
- 500 transações em 5 minutos

### TC056 - Banco de Dados - Transações Concorrentes
**Descrição:** Verificar integridade com transações concorrentes.

**Condições Prévias:**
- Conta bancária com saldo R$ 1.000,00
- Dois usuários tentando pagar contas simultaneamente

**Etapas:**
1. Usuário A tenta pagar R$ 800,00
2. Usuário B tenta pagar R$ 600,00 simultaneamente
3. Verificar resultado final
4. Verificar integridade dos dados

**Resultado Esperado:**
- Apenas uma transação processada
- Saldo final consistente
- Erro apropriado para transação rejeitada
- Logs de auditoria corretos

**Dados de Teste:**
- Saldo inicial: R$ 1.000,00
- Pagamento A: R$ 800,00
- Pagamento B: R$ 600,00
- Resultado: Uma transação aceita, uma rejeitada

### TC057 - Importação - Arquivo Grande
**Descrição:** Verificar importação de arquivo com muitos registros.

**Condições Prévias:**
- Arquivo CSV com 10.000 transações
- Funcionalidade de importação disponível

**Etapas:**
1. Selecionar arquivo grande
2. Iniciar importação
3. Monitorar progresso
4. Verificar resultado final

**Resultado Esperado:**
- Importação processada com sucesso
- Indicador de progresso funcional
- Relatório de erros (se houver)
- Performance aceitável (< 5 minutos)

**Dados de Teste:**
- 10.000 registros no CSV
- Tamanho do arquivo: ~5MB
- Tempo limite: 5 minutos

### TC058 - Backup e Recuperação
**Descrição:** Verificar processo de backup e recuperação de dados.

**Condições Prévias:**
- Sistema com dados de produção
- Funcionalidade de backup disponível

**Etapas:**
1. Executar backup completo
2. Simular perda de dados
3. Restaurar backup
4. Verificar integridade dos dados

**Resultado Esperado:**
- Backup criado com sucesso
- Restauração completa
- Dados íntegros após restauração
- Funcionalidades operacionais

**Dados de Teste:**
- 100.000 transações
- 50 empresas
- 1.000 usuários

---

## 10. Testes de Integração

### TC059 - API - Endpoints de Transações
**Descrição:** Verificar funcionamento dos endpoints da API.

**Condições Prévias:**
- API documentada no Swagger
- Token de autenticação válido

**Etapas:**
1. Testar GET /api/transactions
2. Testar POST /api/transactions
3. Testar PUT /api/transactions/:id
4. Testar DELETE /api/transactions/:id

**Resultado Esperado:**
- Todos os endpoints respondem corretamente
- Códigos de status HTTP apropriados
- Dados retornados no formato esperado
- Validações funcionando

**Dados de Teste:**
- Transação válida para POST
- ID existente para PUT/DELETE
- Filtros para GET

### TC060 - Integração Frontend-Backend
**Descrição:** Verificar comunicação entre frontend e backend.

**Condições Prévias:**
- Frontend e backend executando
- Rede configurada corretamente

**Etapas:**
1. Realizar login no frontend
2. Navegar pelas páginas
3. Criar nova transação
4. Verificar dados no backend

**Resultado Esperado:**
- Comunicação fluida
- Dados sincronizados
- Erros tratados adequadamente
- Performance aceitável

**Dados de Teste:**
- Usuário: "<EMAIL>"
- Transação: Despesa de R$ 100,00

---

## Conclusão

Este documento apresenta 60 casos de teste abrangentes que cobrem todas as principais funcionalidades do Sistema de Gestão Financeira FluxoMax. Os testes incluem:

- **Cenários Positivos**: Fluxos normais de uso
- **Cenários Negativos**: Tratamento de erros e validações
- **Condições de Limite**: Valores extremos e limites do sistema
- **Casos Extremos**: Situações de stress e alta carga

### Métricas de Cobertura

- **Funcionalidades Principais**: 100% cobertas
- **Fluxos de Usuário**: Todos os principais fluxos testados
- **Validações**: Todas as validações críticas verificadas
- **Performance**: Testes de carga incluídos
- **Segurança**: Controles de acesso e autenticação testados

### Recomendações para Execução

1. **Automação**: Priorizar automação dos casos TC001-TC030 (funcionalidades core)
2. **Testes Manuais**: TC031-TC060 podem ser executados manualmente
3. **Ambiente**: Usar dados de teste isolados
4. **Frequência**: Executar testes críticos a cada deploy
5. **Monitoramento**: Acompanhar métricas de performance nos testes de stress

### Dados de Teste Recomendados

- **Empresas**: Mínimo 3 empresas com diferentes configurações
- **Usuários**: 10 usuários com diferentes permissões
- **Transações**: 1000+ transações para testes de performance
- **Entidades**: 100+ clientes e fornecedores
- **Períodos**: Dados cobrindo pelo menos 2 anos

Este conjunto de testes garante a qualidade e confiabilidade do sistema FluxoMax em produção.
