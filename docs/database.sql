-- ====================================================
-- Sistema de Controle Financeiro
-- ====================================================

/*

Resumo das Tabelas do Sistema de Controle Financeiro

1. companies - Armazena informações das empresas cadastradas no sistema, incluindo dados de contato, status e configurações de calendário.
2. custom_periods - Registra períodos personalizados para relatórios financeiros, permitindo análises em intervalos específicos definidos pela empresa.
3. users - Contém dados dos usuários do sistema, como credenciais de acesso e status da conta.
4. profiles - Armazena informações detalhadas do perfil de cada usuário, incluindo dados pessoais e preferências.
5. addresses - Mantém registros de endereços associados a empresas, entidades ou outros elementos do sistema.
6. projects - Gerencia informações sobre projetos empresariais, incluindo orçamentos e prazos.
7. entities - Registra dados de clientes, fornecedores e parceiros comerciais, centralizando o cadastro de pessoas físicas e jurídicas.
8. categories - Classifica transações financeiras em categorias e subcategorias para melhor organização.
9. banks - Mantém um cadastro dos bancos disponíveis no sistema.
10. bank_accounts - Armazena informações das contas bancárias de cada empresa, incluindo saldos e limites.
11. zip_codes - Mantém um cadastro de CEPs para facilitar o preenchimento de endereços.
12. notifications - Gerencia notificações do sistema para usuários sobre eventos importantes.
13. currencies - Armazena informações sobre moedas utilizadas nas transações financeiras.
14. recurrence_types - Define padrões de recorrência para transações periódicas.
15. payment_methods - Cadastra métodos de pagamento disponíveis para transações.
16. system_permissions - Mantém o registro de permissões globais do sistema.
17. permissions - Gerencia permissões específicas por empresa, derivadas das permissões do sistema.
18. roles - Define papéis e funções que podem ser atribuídos a usuários dentro de empresas.
19. role_permissions - Estabelece relações entre papéis e permissões, definindo o que cada papel pode acessar.
20. user_company_roles - Vincula usuários a empresas com papéis específicos, estabelecendo suas atribuições.
21. accounts_payable - Registra contas a pagar, incluindo valores, datas de vencimento e status.
22. accounts_receivable - Controla contas a receber, com informações sobre valores, vencimentos e status.
23. transactions - Registra transações financeiras efetivas entre contas, representando o fluxo real de dinheiro.
24. recurring_schedules - Gerencia a configuração de transações recorrentes, automatizando a criação de contas.
25. financial_audit_logs - Mantém registros de auditoria para todas as operações financeiras realizadas.
26. exchange_rates - Armazena taxas de câmbio entre diferentes moedas para suporte a operações multi-moeda.


*/

-- ====================================================
-- 1. DEFINIÇÃO DE TABELAS BÁSICAS
-- ====================================================

-- Tabela Companies 
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) UNIQUE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address_id UUID,
    logo VARCHAR(255),
    active BOOLEAN DEFAULT true,
    calendar_type TEXT DEFAULT 'standard' CHECK (calendar_type IN ('standard', 'custom')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tabela de períodos customizados
CREATE TABLE IF NOT EXISTS public.custom_periods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    company_id UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);

-- Tabela Users
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tabela de perfis de usuários
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    username VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    preferences JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tabela de Tipos de Endereço (Lookup Table)
CREATE TABLE IF NOT EXISTS public.address_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Inserir os tipos padrão de endereço
INSERT INTO public.address_types (name, description) VALUES
('principal', 'Endereço principal'),
('cobranca', 'Endereço de cobrança'),
('entrega', 'Endereço de entrega')
ON CONFLICT (name) DO NOTHING;

-- Tabela Addresses
CREATE TABLE IF NOT EXISTS public.addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID,
    entity_id UUID,
    address_type_id UUID NOT NULL,
    zip_code VARCHAR(9),
    street VARCHAR(255) NOT NULL,
    number VARCHAR(20) NOT NULL,
    complement VARCHAR(100),
    neighborhood VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(2) NOT NULL,
    country VARCHAR(50) NOT NULL DEFAULT 'Brasil',
    data_origin VARCHAR(10) NOT NULL DEFAULT 'manual' CHECK (data_origin IN ('manual', 'auto')),
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (address_type_id) REFERENCES address_types(id) ON DELETE RESTRICT,
    CONSTRAINT check_owner CHECK (company_id IS NOT NULL OR entity_id IS NOT NULL)
);

-- Adicionar referência de endereço na tabela de empresas
ALTER TABLE public.companies
ADD CONSTRAINT fk_address
FOREIGN KEY (address_id) REFERENCES "addresses"(id) ON DELETE SET NULL;

-- Tabela Projects
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    budget DOUBLE PRECISION,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    status VARCHAR(50) DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'on_hold','canceled')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);

-- Tabela Entities
CREATE TABLE IF NOT EXISTS public.entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('customer', 'supplier')),
    name VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) CHECK (cnpj IS NULL OR cnpj ~* '^[0-9]{2}\.?[0-9]{3}\.?[0-9]{3}\/?[0-9]{4}\-?[0-9]{2}$'),
    phone VARCHAR(20),
    contact VARCHAR(100),
    email VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);

-- Adicionar referência de entidade na tabela de endereços
ALTER TABLE public.addresses
ADD CONSTRAINT fk_entity
FOREIGN KEY (entity_id) REFERENCES "entities"(id) ON DELETE CASCADE;

-- Tabela Categories
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('receivable', 'payable')),
    parent_category_id UUID,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_category_id) REFERENCES "categories"(id) ON DELETE SET NULL
);

-- Tabela Banks (Global)
CREATE TABLE IF NOT EXISTS public.banks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    logo VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabela Bank Accounts
CREATE TABLE IF NOT EXISTS public.bank_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    bank_id UUID NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('corrente', 'poupanca', 'investimento', 'dinheiro', 'outro')),
    
    -- Campos de saldo reformulados
    initial_balance NUMERIC NOT NULL DEFAULT 0,
    current_balance NUMERIC NOT NULL DEFAULT 0,
    is_initial_balance_locked BOOLEAN DEFAULT FALSE,
    
    balance_date TIMESTAMPTZ NOT NULL,
    credit_limit NUMERIC DEFAULT 0.00,
    first_transaction_at TIMESTAMPTZ NULL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_id) REFERENCES "banks"(id) ON DELETE RESTRICT
);

-- Tabela Ceps
CREATE TABLE IF NOT EXISTS public.zip_codes (
    zip_code VARCHAR(9) PRIMARY KEY,
    street VARCHAR(255) NOT NULL,
    neighborhood VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(2) NOT NULL,
    registration_origin VARCHAR(10) NOT NULL DEFAULT 'auto' CHECK (registration_origin IN ('manual', 'auto')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabela Notifications
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    user_id UUID,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('email', 'in-app', 'both')),
    status VARCHAR(50) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'sent')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES "users"(id) ON DELETE SET NULL
);

-- ====================================================
-- 2. DEFINIÇÃO DE TABELAS DE PERMISSÕES E CONTROLE DE ACESSO
-- ====================================================

-- Tabela de permissões do sistema (global)
CREATE TABLE IF NOT EXISTS public.system_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    module VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE public.system_permissions IS 'Permissões do sistema global, independentes de empresa';
COMMENT ON COLUMN public.system_permissions.code IS 'Código único da permissão (ex: accounts_payable.create)';
COMMENT ON COLUMN public.system_permissions.module IS 'Módulo do sistema ao qual a permissão pertence';

-- Tabela Permissions (por empresa)
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    action VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    system_permission_id UUID,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (system_permission_id) REFERENCES system_permissions(id) ON DELETE CASCADE
);

COMMENT ON COLUMN public.permissions.system_permission_id IS 'Referência à permissão do sistema correspondente';

-- Tabela Roles
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);

-- Tabela Role Permissions 
CREATE TABLE IF NOT EXISTS public.role_permissions (
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES "roles"(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES "permissions"(id) ON DELETE CASCADE
);

-- Tabela User Company Roles
CREATE TABLE IF NOT EXISTS public.user_company_roles (
    user_id UUID NOT NULL,
    company_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, company_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- ====================================================
-- 3. DEFINIÇÃO DE TABELAS FINANCEIRAS
-- ====================================================

-- Tabela de Moedas (Lookup Table)
CREATE TABLE IF NOT EXISTS public.currencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(3) NOT NULL UNIQUE,
  name VARCHAR(50) NOT NULL,
  symbol VARCHAR(5) NOT NULL,
  decimal_places INTEGER NOT NULL DEFAULT 2,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inserir moedas padrão
INSERT INTO public.currencies (code, name, symbol, decimal_places, is_default) VALUES
('BRL', 'Real Brasileiro', 'R$', 2, true),
('USD', 'Dólar Americano', '$', 2, false),
('EUR', 'Euro', '€', 2, false)
ON CONFLICT (code) DO NOTHING;

-- Tabela de Métodos de Pagamento (Lookup Table)
CREATE TABLE IF NOT EXISTS public.payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) UNIQUE NOT NULL,
  description VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Inserir métodos de pagamento padrão
INSERT INTO public.payment_methods (name, description) VALUES
('dinheiro', 'Pagamento em dinheiro'),
('pix', 'Transferência via PIX'),
('cartao_credito', 'Pagamento com cartão de crédito'),
('cartao_debito', 'Pagamento com cartão de débito'),
('boleto', 'Pagamento via boleto bancário'),
('transferencia', 'Transferência bancária'),
('cheque', 'Pagamento com cheque'),
('debito_automatico', 'Débito automático em conta')
ON CONFLICT (name) DO NOTHING;

-- Tabela de Tipos de Recorrência (Lookup Table)
CREATE TABLE IF NOT EXISTS public.recurrence_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) UNIQUE NOT NULL,
  description VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Inserir tipos de recorrência padrão
INSERT INTO public.recurrence_types (name, description) VALUES
('unico', 'Pagamento/recebimento único'),
('diario', 'Recorrência diária'),
('semanal', 'Recorrência semanal'),
('quinzenal', 'Recorrência quinzenal'),
('mensal', 'Recorrência mensal'),
('bimestral', 'Recorrência bimestral'),
('trimestral', 'Recorrência trimestral'),
('semestral', 'Recorrência semestral'),
('anual', 'Recorrência anual')
ON CONFLICT (name) DO NOTHING;

-- Tabela Accounts Payable
CREATE TABLE IF NOT EXISTS public.accounts_payable (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL,
  description VARCHAR(255) NOT NULL,
  entity_id UUID,
  due_date TIMESTAMPTZ NOT NULL,
  amount NUMERIC NOT NULL,
  paid_amount NUMERIC NOT NULL DEFAULT 0,
  currency_id UUID NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'open' CHECK (status IN ('pending', 'open', 'paid', 'partial', 'canceled', 'overdue')),
  category_id UUID,
  project_id UUID,
  payment_method_id UUID,
  bank_account_id UUID NOT NULL,
  recurrence_type_id UUID,
  invoice_number VARCHAR(50),
  notes VARCHAR(500),
  interest_amount NUMERIC DEFAULT 0.00,
  discount_amount NUMERIC DEFAULT 0.00,
  final_value NUMERIC GENERATED ALWAYS AS (amount + interest_amount - discount_amount) STORED,
  installments INTEGER DEFAULT 1,
  installment_number INTEGER DEFAULT 1,
  parent_id UUID,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  deleted_at TIMESTAMPTZ,
  FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES "categories"(id) ON DELETE SET NULL,
  FOREIGN KEY (project_id) REFERENCES "projects"(id) ON DELETE SET NULL,
  FOREIGN KEY (entity_id) REFERENCES "entities"(id) ON DELETE SET NULL,
  FOREIGN KEY (bank_account_id) REFERENCES "bank_accounts"(id) ON DELETE SET NULL,
  FOREIGN KEY (payment_method_id) REFERENCES "payment_methods"(id) ON DELETE SET NULL,
  FOREIGN KEY (recurrence_type_id) REFERENCES "recurrence_types"(id) ON DELETE SET NULL,
  FOREIGN KEY (currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT,
  FOREIGN KEY (parent_id) REFERENCES "accounts_payable"(id) ON DELETE CASCADE
);

COMMENT ON COLUMN public.accounts_payable.interest_amount IS 'Valor de juros adicional aplicado à transação';
COMMENT ON COLUMN public.accounts_payable.discount_amount IS 'Valor de desconto aplicado à transação';
COMMENT ON COLUMN public.accounts_payable.final_value IS 'Valor final calculado: valor original + juros - descontos';
COMMENT ON TABLE public.accounts_payable IS 'Tabela para registrar contas a pagar (eventos financeiros planejados)';
COMMENT ON COLUMN public.accounts_payable.status IS 'Status da conta: open (em aberto), paid (paga), partial (parcialmente paga), canceled (cancelada), pending (pendente), overdue (vencida)';
COMMENT ON COLUMN public.accounts_payable.payment_method_id IS 'Referência ao método de pagamento utilizado';
COMMENT ON COLUMN public.accounts_payable.recurrence_type_id IS 'Referência ao tipo de recorrência da conta';
COMMENT ON COLUMN public.accounts_payable.currency_id IS 'Moeda da transação';

-- Tabela Accounts Receivable
CREATE TABLE IF NOT EXISTS public.accounts_receivable (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL,
  description VARCHAR(255) NOT NULL, 
  entity_id UUID,
  due_date TIMESTAMPTZ NOT NULL,
  amount NUMERIC NOT NULL,
  paid_amount NUMERIC NOT NULL DEFAULT 0,
  currency_id UUID NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'open' CHECK (status IN ('pending', 'open', 'received', 'partial', 'canceled', 'overdue')),
  category_id UUID,
  project_id UUID,
  payment_method_id UUID,
  bank_account_id UUID NOT NULL,
  recurrence_type_id UUID,
  invoice_number VARCHAR(50),
  notes VARCHAR(500),
  interest_amount NUMERIC DEFAULT 0.00,
  discount_amount NUMERIC DEFAULT 0.00,
  final_value NUMERIC GENERATED ALWAYS AS (amount + interest_amount - discount_amount) STORED,
  installments INTEGER DEFAULT 1,
  installment_number INTEGER DEFAULT 1,
  parent_id UUID,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  deleted_at TIMESTAMPTZ,
  FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES "categories"(id) ON DELETE SET NULL,
  FOREIGN KEY (project_id) REFERENCES "projects"(id) ON DELETE SET NULL,
  FOREIGN KEY (entity_id) REFERENCES "entities"(id) ON DELETE SET NULL,
  FOREIGN KEY (bank_account_id) REFERENCES "bank_accounts"(id) ON DELETE SET NULL,
  FOREIGN KEY (payment_method_id) REFERENCES "payment_methods"(id) ON DELETE SET NULL,
  FOREIGN KEY (recurrence_type_id) REFERENCES "recurrence_types"(id) ON DELETE SET NULL,
  FOREIGN KEY (currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT,
  FOREIGN KEY (parent_id) REFERENCES "accounts_receivable"(id) ON DELETE CASCADE
);

COMMENT ON COLUMN public.accounts_receivable.interest_amount IS 'Valor de juros adicional aplicado à transação';
COMMENT ON COLUMN public.accounts_receivable.discount_amount IS 'Valor de desconto aplicado à transação';
COMMENT ON COLUMN public.accounts_receivable.final_value IS 'Valor final calculado: valor original + juros - descontos';
COMMENT ON TABLE public.accounts_receivable IS 'Tabela para registrar contas a receber (eventos financeiros planejados)';
COMMENT ON COLUMN public.accounts_receivable.status IS 'Status da conta: open (em aberto), received (recebida), partial (parcialmente recebida), canceled (cancelada), pending (pendente), overdue (vencida)';
COMMENT ON COLUMN public.accounts_receivable.payment_method_id IS 'Referência ao método de pagamento utilizado';
COMMENT ON COLUMN public.accounts_receivable.recurrence_type_id IS 'Referência ao tipo de recorrência da conta';
COMMENT ON COLUMN public.accounts_receivable.currency_id IS 'Moeda da transação';

-- Tabela Transactions
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('income', 'expense', 'transfer')),
    accounts_payable_id UUID,
    accounts_receivable_id UUID,
    amount NUMERIC NOT NULL,
    currency_id UUID NOT NULL,
    transaction_date TIMESTAMPTZ NOT NULL,
    bank_account_id UUID,
    destination_bank_account_id UUID,
    description VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (accounts_payable_id) REFERENCES "accounts_payable"(id) ON DELETE CASCADE,
    FOREIGN KEY (accounts_receivable_id) REFERENCES "accounts_receivable"(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES "bank_accounts"(id) ON DELETE SET NULL,
    FOREIGN KEY (destination_bank_account_id) REFERENCES "bank_accounts"(id) ON DELETE SET NULL,
    FOREIGN KEY (currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT,
    CONSTRAINT chk_transaction_refs CHECK (
        (transaction_type = 'income' AND accounts_receivable_id IS NOT NULL AND accounts_payable_id IS NULL) OR 
        (transaction_type = 'expense' AND accounts_payable_id IS NOT NULL AND accounts_receivable_id IS NULL) OR 
        (transaction_type = 'transfer' AND accounts_payable_id IS NULL AND accounts_receivable_id IS NULL)
    ),
    CONSTRAINT chk_transfer_accounts CHECK (
        transaction_type != 'transfer' OR 
        (transaction_type = 'transfer' AND bank_account_id IS NOT NULL AND destination_bank_account_id IS NOT NULL AND bank_account_id != destination_bank_account_id)
    )
);

COMMENT ON COLUMN public.transactions.transaction_type IS 'Tipo de transação: income (receita), expense (despesa), transfer (transferência)';
COMMENT ON COLUMN public.transactions.accounts_payable_id IS 'Referência à conta a pagar relacionada a esta transação, se aplicável';
COMMENT ON COLUMN public.transactions.accounts_receivable_id IS 'Referência à conta a receber relacionada a esta transação, se aplicável';
COMMENT ON COLUMN public.transactions.destination_bank_account_id IS 'Conta bancária de destino, usado apenas para transferências';
COMMENT ON COLUMN public.transactions.currency_id IS 'Moeda da transação';
COMMENT ON TABLE public.transactions IS 'Tabela para registrar movimentações financeiras efetivas';

-- ====================================================
-- 4. TRIGGERS PARA ATUALIZAÇÃO DE CAMPOS TIMESTAMP
-- ====================================================

-- Criar trigger para atualizar o campo updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar triggers para todas as tabelas
CREATE TRIGGER update_companies_modtime
BEFORE UPDATE ON public.companies
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_custom_periods_modtime
BEFORE UPDATE ON public.custom_periods
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_permissions_modtime
BEFORE UPDATE ON public.permissions
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_roles_modtime
BEFORE UPDATE ON public.roles
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_role_permissions_modtime
BEFORE UPDATE ON public.role_permissions
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_user_company_roles_modtime
BEFORE UPDATE ON public.user_company_roles
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_users_modtime
BEFORE UPDATE ON public.users
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_profiles_modtime
BEFORE UPDATE ON public.profiles
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_addresses_modtime
BEFORE UPDATE ON public.addresses
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_projects_modtime
BEFORE UPDATE ON public.projects
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_entities_modtime
BEFORE UPDATE ON public.entities
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_accounts_payable_modtime
BEFORE UPDATE ON public.accounts_payable
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_accounts_receivable_modtime
BEFORE UPDATE ON public.accounts_receivable
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_transactions_modtime
BEFORE UPDATE ON public.transactions
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_categories_modtime
BEFORE UPDATE ON public.categories
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_banks_modtime
BEFORE UPDATE ON public.banks
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_bank_accounts_modtime
BEFORE UPDATE ON public.bank_accounts
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_ceps_modtime
BEFORE UPDATE ON public.ceps
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_notifications_modtime
BEFORE UPDATE ON public.notifications
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_system_permissions_modtime
BEFORE UPDATE ON public.system_permissions
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_payment_methods_modtime
BEFORE UPDATE ON public.payment_methods
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_recurrence_types_modtime
BEFORE UPDATE ON public.recurrence_types
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- ====================================================
-- 5. POLÍTICAS DE CONTROLE DE ACESSO (RLS)
-- ====================================================

-- Habilitar RLS para todas as tabelas
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.address_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurrence_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_company_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts_payable ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts_receivable ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bank_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ceps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.custom_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_permissions ENABLE ROW LEVEL SECURITY;

-- Políticas para tabelas relacionadas às empresas
CREATE POLICY company_access ON companies
    USING (id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);

-- Política otimizada para consultas SELECT de empresas
CREATE POLICY company_select ON companies FOR SELECT
    USING (deleted_at IS NULL AND id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Perfis de usuário (acesso ao próprio perfil ou perfis de usuários nas mesmas empresas)
CREATE POLICY profiles_access ON public.profiles
    USING (
        id = auth.uid() OR -- Próprio perfil
        id IN (
            SELECT u.user_id FROM user_company_roles u
            WHERE u.company_id IN (
                SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()
            )
        ) AND deleted_at IS NULL
    );

-- Permissões (acesso às permissões das empresas do usuário)
CREATE POLICY permissions_access ON public.permissions
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));
    
-- Política otimizada para consultas SELECT de permissões
CREATE POLICY permissions_select ON public.permissions FOR SELECT
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Papéis/Roles (acesso aos papéis das empresas do usuário)
CREATE POLICY roles_access ON public.roles
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de papéis
CREATE POLICY roles_select ON public.roles FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Role_permissions (acesso às permissões de papéis das empresas do usuário)
CREATE POLICY role_permissions_access ON public.role_permissions
    USING (role_id IN (SELECT id FROM roles WHERE company_id IN 
        (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid())));

-- User_company_roles (acesso aos registros das empresas do usuário)
CREATE POLICY user_company_roles_access ON public.user_company_roles
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Endereços (acesso aos endereços das empresas do usuário)
CREATE POLICY addresses_access ON public.addresses
    USING (
        company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid())
        OR entity_id IN (SELECT id FROM entities WHERE company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()))
    );

-- Projetos (acesso aos projetos das empresas do usuário)
CREATE POLICY projects_access ON public.projects
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de projetos
CREATE POLICY projects_select ON public.projects FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Entidades (acesso às entidades das empresas do usuário)
CREATE POLICY entity_access ON public.entities
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de entidades
CREATE POLICY entity_select ON public.entities FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Contas a pagar (acesso às contas das empresas do usuário)
CREATE POLICY accounts_payable_access ON public.accounts_payable
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de contas a pagar
CREATE POLICY accounts_payable_select ON public.accounts_payable FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Contas a receber (acesso às contas das empresas do usuário)
CREATE POLICY accounts_receivable_access ON public.accounts_receivable
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de contas a receber
CREATE POLICY accounts_receivable_select ON public.accounts_receivable FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Transações (acesso às transações das empresas do usuário)
CREATE POLICY transactions_access ON public.transactions
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de transações
CREATE POLICY transactions_select ON public.transactions FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Categorias (acesso às categorias das empresas do usuário)
CREATE POLICY categories_access ON public.categories
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de categorias
CREATE POLICY categories_select ON public.categories FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Contas bancárias (acesso às contas das empresas do usuário)
CREATE POLICY bank_accounts_access ON public.bank_accounts
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
-- Política otimizada para consultas SELECT de contas bancárias
CREATE POLICY bank_accounts_select ON public.bank_accounts FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- CEPs (acesso aos CEPs das empresas do usuário)
CREATE POLICY ceps_access ON public.ceps
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Notificações (acesso às notificações das empresas do usuário ou diretamente ao usuário)
CREATE POLICY notifications_access ON public.notifications
    USING (
        company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid())
        OR user_id = auth.uid()
    );

-- Períodos customizados (acesso aos períodos das empresas do usuário)
CREATE POLICY custom_periods_access ON public.custom_periods
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Permissões do sistema (acesso global para todos os usuários autenticados)
CREATE POLICY system_permissions_access ON public.system_permissions
    USING (auth.uid() IS NOT NULL);
    
-- Tipos de endereço (acesso global para todos os usuários autenticados)
CREATE POLICY address_types_access ON public.address_types
    USING (auth.uid() IS NOT NULL);

-- ====================================================
-- 6. AUDITORIA E CONTROLE DE VERSÃO
-- ====================================================

-- Tabela de log de transações financeiras
CREATE TABLE IF NOT EXISTS public.financial_audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    record_id UUID NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    old_data JSONB,
    new_data JSONB,
    changed_by UUID,
    changed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Função para registro de auditoria
CREATE OR REPLACE FUNCTION log_financial_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO financial_audit_logs (record_id, table_name, operation, new_data, changed_by)
        VALUES (NEW.id, TG_TABLE_NAME, TG_OP, to_jsonb(NEW), auth.uid());
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO financial_audit_logs (record_id, table_name, operation, old_data, new_data, changed_by)
        VALUES (NEW.id, TG_TABLE_NAME, TG_OP, to_jsonb(OLD), to_jsonb(NEW), auth.uid());
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO financial_audit_logs (record_id, table_name, operation, old_data, changed_by)
        VALUES (OLD.id, TG_TABLE_NAME, TG_OP, to_jsonb(OLD), auth.uid());
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers para registrar alterações em tabelas financeiras críticas
CREATE TRIGGER log_accounts_payable_changes
AFTER INSERT OR UPDATE OR DELETE ON public.accounts_payable
FOR EACH ROW EXECUTE FUNCTION log_financial_changes();

CREATE TRIGGER log_accounts_receivable_changes
AFTER INSERT OR UPDATE OR DELETE ON public.accounts_receivable
FOR EACH ROW EXECUTE FUNCTION log_financial_changes();

CREATE TRIGGER log_transactions_changes
AFTER INSERT OR UPDATE OR DELETE ON public.transactions
FOR EACH ROW EXECUTE FUNCTION log_financial_changes();

CREATE TRIGGER log_bank_accounts_changes
AFTER INSERT OR UPDATE OR DELETE ON public.bank_accounts
FOR EACH ROW EXECUTE FUNCTION log_financial_changes();

-- ====================================================
-- 7. IMPLEMENTAÇÃO DE SOFT DELETE
-- ====================================================

-- Função para soft delete
CREATE OR REPLACE FUNCTION soft_delete_record()
RETURNS TRIGGER AS $$
BEGIN
    NEW.deleted_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ====================================================
-- 8. VALIDAÇÃO DE DADOS
-- ====================================================

-- Validação de e-mail
ALTER TABLE public.companies 
ADD CONSTRAINT chk_companies_email_valid 
CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$');

ALTER TABLE public.entities 
ADD CONSTRAINT chk_entities_email_valid 
CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$');

-- Validação de telefone
ALTER TABLE public.companies 
ADD CONSTRAINT chk_companies_phone_valid 
CHECK (phone IS NULL OR phone ~* '^\+?[0-9().-]{10,15}$');

ALTER TABLE public.entities 
ADD CONSTRAINT chk_entities_phone_valid 
CHECK (phone IS NULL OR phone ~* '^\+?[0-9().-]{10,15}$');

ALTER TABLE public.profiles 
ADD CONSTRAINT chk_profiles_phone_valid 
CHECK (phone IS NULL OR phone ~* '^\+?[0-9().-]{10,15}$');

-- ====================================================
-- 9. ATUALIZAÇÃO DE STATUS DE CONTAS VENCIDAS
-- ====================================================

-- Função para atualizar status de contas vencidas
CREATE OR REPLACE FUNCTION update_overdue_accounts()
RETURNS VOID AS $$
BEGIN
    -- Atualizar contas a pagar vencidas
    UPDATE public.accounts_payable
    SET status = 'overdue'
    WHERE status IN ('open', 'pending') 
      AND due_date < CURRENT_DATE
      AND paid_amount < amount
      AND deleted_at IS NULL;

    -- Atualizar contas a receber vencidas
    UPDATE public.accounts_receivable
    SET status = 'overdue'
    WHERE status IN ('open', 'pending') 
      AND due_date < CURRENT_DATE
      AND paid_amount < amount
      AND deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Criar uma função de trigger para chamar quando uma conta for criada ou atualizada
CREATE OR REPLACE FUNCTION check_account_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Para contas a pagar
    IF TG_TABLE_NAME = 'accounts_payable' THEN
        IF NEW.due_date < CURRENT_DATE AND NEW.status IN ('open', 'pending') AND NEW.paid_amount < NEW.amount THEN
            NEW.status := 'overdue';
        END IF;
    -- Para contas a receber
    ELSIF TG_TABLE_NAME = 'accounts_receivable' THEN
        IF NEW.due_date < CURRENT_DATE AND NEW.status IN ('open', 'pending') AND NEW.paid_amount < NEW.amount THEN
            NEW.status := 'overdue';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger para verificar status automaticamente quando registros são criados ou atualizados
CREATE TRIGGER check_accounts_payable_status
BEFORE INSERT OR UPDATE ON public.accounts_payable
FOR EACH ROW EXECUTE FUNCTION check_account_status();

CREATE TRIGGER check_accounts_receivable_status
BEFORE INSERT OR UPDATE ON public.accounts_receivable
FOR EACH ROW EXECUTE FUNCTION check_account_status();

-- Função para ser executada diariamente via cron job para atualizar todas as contas
COMMENT ON FUNCTION update_overdue_accounts() IS 'Executar diariamente via cron job para atualizar status de contas vencidas';

-- ====================================================
-- 10. INICIALIZAÇÃO DE PERMISSÕES DO SISTEMA
-- ====================================================

-- Inserção de permissões de sistema padrão para funcionalidades principais
INSERT INTO public.system_permissions (code, name, description, module) VALUES
    ('companies.view', 'Visualizar empresas', 'Permite visualizar dados da empresa', 'companies'),
    ('companies.edit', 'Editar empresas', 'Permite editar dados da empresa', 'companies'),
    ('users.view', 'Visualizar usuários', 'Permite visualizar usuários da empresa', 'users'),
    ('users.create', 'Criar usuários', 'Permite criar novos usuários', 'users'),
    ('users.edit', 'Editar usuários', 'Permite editar usuários existentes', 'users'),
    ('users.delete', 'Remover usuários', 'Permite remover usuários', 'users'),
    ('roles.view', 'Visualizar papéis', 'Permite visualizar papéis', 'roles'),
    ('roles.create', 'Criar papéis', 'Permite criar novos papéis', 'roles'),
    ('roles.edit', 'Editar papéis', 'Permite editar papéis existentes', 'roles'),
    ('roles.delete', 'Remover papéis', 'Permite remover papéis', 'roles'),
    ('accounts_payable.view', 'Visualizar contas a pagar', 'Permite visualizar contas a pagar', 'finance'),
    ('accounts_payable.create', 'Criar contas a pagar', 'Permite criar contas a pagar', 'finance'),
    ('accounts_payable.edit', 'Editar contas a pagar', 'Permite editar contas a pagar', 'finance'),
    ('accounts_payable.delete', 'Remover contas a pagar', 'Permite remover contas a pagar', 'finance'),
    ('accounts_receivable.view', 'Visualizar contas a receber', 'Permite visualizar contas a receber', 'finance'),
    ('accounts_receivable.create', 'Criar contas a receber', 'Permite criar contas a receber', 'finance'),
    ('accounts_receivable.edit', 'Editar contas a receber', 'Permite editar contas a receber', 'finance'),
    ('accounts_receivable.delete', 'Remover contas a receber', 'Permite remover contas a receber', 'finance'),
    ('transactions.view', 'Visualizar transações', 'Permite visualizar transações', 'finance'),
    ('transactions.create', 'Criar transações', 'Permite criar transações', 'finance'),
    ('transactions.edit', 'Editar transações', 'Permite editar transações', 'finance'),
    ('transactions.delete', 'Remover transações', 'Permite remover transações', 'finance'),
    ('reports.view', 'Visualizar relatórios', 'Permite visualizar relatórios', 'reports'),
    ('reports.export', 'Exportar relatórios', 'Permite exportar relatórios', 'reports')
ON CONFLICT (code) DO NOTHING;

-- Trigger para criar permissões da empresa automaticamente
CREATE OR REPLACE FUNCTION create_company_permissions()
RETURNS TRIGGER AS $$
BEGIN
    -- Inserir permissões da empresa com base nas permissões do sistema
    INSERT INTO public.permissions (company_id, action, description, system_permission_id)
    SELECT 
        NEW.id, 
        sp.code, 
        sp.description, 
        sp.id
    FROM 
        system_permissions sp;
    
    -- Criar papel de administrador com todas as permissões
    INSERT INTO public.roles (company_id, name, description, is_admin)
    VALUES (NEW.id, 'Administrador', 'Administrador com acesso completo', true);
    
    -- Adicionar todas as permissões ao papel de administrador
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT 
        (SELECT id FROM public.roles WHERE company_id = NEW.id AND is_admin = true LIMIT 1),
        id
    FROM 
        public.permissions 
    WHERE 
        company_id = NEW.id;
        
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para criar permissões ao criar uma nova empresa
CREATE TRIGGER trigger_create_company_permissions
AFTER INSERT ON public.companies
FOR EACH ROW EXECUTE FUNCTION create_company_permissions();

-- ====================================================
-- 11. ÍNDICES PARA OTIMIZAÇÃO DE CONSULTAS
-- ====================================================

-- Índices para tabelas principais
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_user_company_roles_user_id ON public.user_company_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_company_roles_company_id ON public.user_company_roles(company_id);
CREATE INDEX IF NOT EXISTS idx_user_company_roles_role_id ON public.user_company_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_transactions_accounts_payable_id ON public.transactions(accounts_payable_id);
CREATE INDEX IF NOT EXISTS idx_transactions_accounts_receivable_id ON public.transactions(accounts_receivable_id);
CREATE INDEX IF NOT EXISTS idx_transactions_company_id ON public.transactions(company_id);
CREATE INDEX IF NOT EXISTS idx_transactions_bank_account_id ON public.transactions(bank_account_id);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_date ON public.transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_type ON public.transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_deleted_at ON public.transactions(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_accounts_payable_company_id ON public.accounts_payable(company_id);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_due_date ON public.accounts_payable(due_date);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_status ON public.accounts_payable(status);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_deleted_at ON public.accounts_payable(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_company_id ON public.accounts_receivable(company_id);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_due_date ON public.accounts_receivable(due_date);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_status ON public.accounts_receivable(status);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_deleted_at ON public.accounts_receivable(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_entities_company_id ON public.entities(company_id);
CREATE INDEX IF NOT EXISTS idx_entities_type ON public.entities(type);
CREATE INDEX IF NOT EXISTS idx_entities_deleted_at ON public.entities(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_projects_company_id ON public.projects(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_deleted_at ON public.projects(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_categories_company_id ON public.categories(company_id);
CREATE INDEX IF NOT EXISTS idx_categories_transaction_type ON public.categories(transaction_type);
CREATE INDEX IF NOT EXISTS idx_categories_deleted_at ON public.categories(deleted_at) WHERE deleted_at IS NULL;

-- Índices compostos para consultas frequentes
CREATE INDEX IF NOT EXISTS idx_transactions_company_date_type ON public.transactions(company_id, transaction_date, transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_bank_account_date ON public.transactions(bank_account_id, transaction_date);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_company_status_date ON public.accounts_payable(company_id, status, due_date);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_company_status_date ON public.accounts_receivable(company_id, status, due_date);
CREATE INDEX IF NOT EXISTS idx_user_company_roles_composite ON public.user_company_roles(user_id, company_id, role_id);
CREATE INDEX IF NOT EXISTS idx_recurring_schedules_active_date ON public.recurring_schedules(active, next_generation_date) WHERE active = true;
CREATE INDEX IF NOT EXISTS idx_financial_audit_logs_company_date ON public.financial_audit_logs(company_id, changed_at);

-- Índices para busca por texto
CREATE INDEX IF NOT EXISTS idx_entities_name_gin ON public.entities USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_description_gin ON public.accounts_payable USING gin(description gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_description_gin ON public.accounts_receivable USING gin(description gin_trgm_ops);

-- Extenção para índices de texto
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Índice de cobertura para consultas de relatórios financeiros
CREATE INDEX IF NOT EXISTS idx_transactions_financial_report ON public.transactions(company_id, transaction_date, transaction_type, bank_account_id)
INCLUDE (amount, description);

CREATE INDEX IF NOT EXISTS idx_accounts_payable_financial_report ON public.accounts_payable(company_id, due_date, status)
INCLUDE (amount, paid_amount, description);

CREATE INDEX IF NOT EXISTS idx_accounts_receivable_financial_report ON public.accounts_receivable(company_id, due_date, status)
INCLUDE (amount, paid_amount, description);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_payment_method_id ON public.accounts_payable(payment_method_id);
CREATE INDEX IF NOT EXISTS idx_accounts_payable_recurrence_type_id ON public.accounts_payable(recurrence_type_id);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_payment_method_id ON public.accounts_receivable(payment_method_id);
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_recurrence_type_id ON public.accounts_receivable(recurrence_type_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_company_id ON public.bank_accounts(company_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_deleted_at ON public.bank_accounts(deleted_at) WHERE deleted_at IS NULL;

-- Índices para otimizar consultas na nova estrutura de bank_accounts
CREATE INDEX IF NOT EXISTS idx_bank_accounts_company_balance 
ON public.bank_accounts(company_id, current_balance) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_bank_accounts_locked_status 
ON public.bank_accounts(is_initial_balance_locked, first_transaction_at) 
WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_companies_deleted_at ON public.companies(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_roles_is_admin ON public.roles(is_admin);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON public.role_permissions(permission_id);

-- Índices compostos para consultas frequentes
CREATE INDEX IF NOT EXISTS idx_accounts_payable_company_due_date ON public.accounts_payable(company_id, due_date) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_accounts_receivable_company_due_date ON public.accounts_receivable(company_id, due_date) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_company_date ON public.transactions(company_id, transaction_date) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_company_type ON public.transactions(company_id, transaction_type) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_user_company_roles_combined ON public.user_company_roles(user_id, company_id);

-- Atualizar função de transação para usar FOR UPDATE
CREATE OR REPLACE FUNCTION process_transaction(transaction_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Lock na linha da conta bancária durante atualização
    UPDATE public.bank_accounts
    SET current_balance = current_balance + (
        SELECT amount FROM public.transactions WHERE id = transaction_id
    )
    WHERE id = (SELECT bank_account_id FROM public.transactions WHERE id = transaction_id)
    FOR UPDATE;
END;
$$ LANGUAGE plpgsql;

-- Criar extensão para monitoramento
CREATE EXTENSION pg_stat_statements;

-- Criar função para identificar consultas lentas
CREATE OR REPLACE FUNCTION identify_slow_queries()
RETURNS TABLE (
    query text,
    calls bigint,
    total_time double precision,
    mean_time double precision
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        query, 
        calls, 
        total_time, 
        mean_time
    FROM pg_stat_statements
    ORDER BY total_time DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Função para prevenir alteração do saldo inicial após bloqueio
CREATE OR REPLACE FUNCTION prevent_initial_balance_modification()
RETURNS TRIGGER AS $$
BEGIN
    -- Impede alteração do saldo inicial se estiver bloqueado
    IF OLD.is_initial_balance_locked = TRUE 
       AND NEW.initial_balance != OLD.initial_balance THEN
        RAISE EXCEPTION 'Cannot modify initial_balance after first transaction. Account ID: %', NEW.id;
    END IF;
    
    -- Atualiza o timestamp de updated_at
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para aplicar a proteção
CREATE OR REPLACE TRIGGER trigger_protect_initial_balance
    BEFORE UPDATE ON public.bank_accounts
    FOR EACH ROW
    EXECUTE FUNCTION prevent_initial_balance_modification();

-- Função para marcar primeira transação e bloquear saldo inicial
CREATE OR REPLACE FUNCTION lock_initial_balance(account_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.bank_accounts 
    SET 
        is_initial_balance_locked = TRUE,
        first_transaction_at = COALESCE(first_transaction_at, NOW()),
        updated_at = NOW()
    WHERE id = account_uuid 
      AND is_initial_balance_locked = FALSE;
      
    IF NOT FOUND THEN
        RAISE NOTICE 'Account % not found or already locked', account_uuid;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Criar função para recalcular saldo de contas bancárias
CREATE OR REPLACE FUNCTION recalculate_bank_account_balance(account_id UUID)
RETURNS VOID AS $$
DECLARE
    total_income NUMERIC;
    total_expense NUMERIC;
    account_initial_balance NUMERIC;
BEGIN
    -- Obter o saldo inicial da conta
    SELECT initial_balance INTO account_initial_balance
    FROM public.bank_accounts
    WHERE id = account_id;
    
    -- Calcular receitas
    SELECT COALESCE(SUM(amount), 0) INTO total_income
    FROM public.transactions
    WHERE bank_account_id = account_id
      AND accounts_receivable_id IS NOT NULL
      AND deleted_at IS NULL;
      
    -- Calcular despesas
    SELECT COALESCE(SUM(amount), 0) INTO total_expense
    FROM public.transactions
    WHERE bank_account_id = account_id
      AND accounts_payable_id IS NOT NULL
      AND deleted_at IS NULL;
      
    -- Atualizar apenas o saldo atual
    UPDATE public.bank_accounts
    SET current_balance = account_initial_balance + total_income - total_expense,
        balance_date = NOW()
    WHERE id = account_id;
    
    -- Bloquear saldo inicial se houver transações
    IF total_income > 0 OR total_expense > 0 THEN
        PERFORM lock_initial_balance(account_id);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- validação de que um pagamento não exceda o valor total da conta
CREATE OR REPLACE FUNCTION validate_transaction()
RETURNS TRIGGER AS $$
DECLARE
    account_total NUMERIC;
    account_paid NUMERIC;
BEGIN
    -- Para contas a pagar
    IF NEW.accounts_payable_id IS NOT NULL THEN
        SELECT amount, paid_amount INTO account_total, account_paid
        FROM public.accounts_payable
        WHERE id = NEW.accounts_payable_id;
        
        IF (account_paid + NEW.amount) > account_total THEN
            RAISE EXCEPTION 'O valor do pagamento excede o saldo da conta';
        END IF;
        
        -- Atualizar o valor pago
        UPDATE public.accounts_payable
        SET paid_amount = paid_amount + NEW.amount,
            status = CASE 
                        WHEN paid_amount + NEW.amount >= amount THEN 'paid'
                        ELSE 'partial'
                     END
        WHERE id = NEW.accounts_payable_id;
    END IF;
    
    -- Para contas a receber
    IF NEW.accounts_receivable_id IS NOT NULL THEN
        SELECT amount, paid_amount INTO account_total, account_paid
        FROM public.accounts_receivable
        WHERE id = NEW.accounts_receivable_id;
        
        IF (account_paid + NEW.amount) > account_total THEN
            RAISE EXCEPTION 'O valor do pagamento excede o saldo da conta';
        END IF;
        
        -- Atualizar o valor pago
        UPDATE public.accounts_receivable
        SET paid_amount = paid_amount + NEW.amount,
            status = CASE 
                        WHEN paid_amount + NEW.amount >= amount THEN 'paid'
                        ELSE 'partial'
                     END
        WHERE id = NEW.accounts_receivable_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para validação de transações
CREATE TRIGGER validate_transaction_trigger
BEFORE INSERT ON public.transactions
FOR EACH ROW EXECUTE FUNCTION validate_transaction();

-- Criar trigger para recalcular saldo de contas bancárias
CREATE TRIGGER recalculate_bank_account_balance_trigger
AFTER INSERT OR UPDATE ON public.transactions
FOR EACH ROW EXECUTE FUNCTION recalculate_bank_account_balance(NEW.bank_account_id);

-- Constraints de Integridade para contas bancárias
-- Constraint para validar saldos positivos (exceto contas de crédito)
ALTER TABLE public.bank_accounts 
ADD CONSTRAINT check_positive_balances 
CHECK (
    (current_balance >= 0 AND initial_balance >= 0) 
    OR account_type = 'investimento'
);

-- Constraint para garantir consistência entre saldos iniciais
ALTER TABLE public.bank_accounts 
ADD CONSTRAINT check_initial_balance_consistency 
CHECK (
    NOT is_initial_balance_locked 
    OR first_transaction_at IS NOT NULL
);

-- ====================================================
-- 12. SISTEMA DE RECORRÊNCIA
-- ====================================================

-- Tabela para gerenciar eventos recorrentes
CREATE TABLE IF NOT EXISTS public.recurring_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL,
  recurrence_type_id UUID NOT NULL,
  entity_id UUID,
  description VARCHAR(255) NOT NULL,
  reference_table VARCHAR(50) NOT NULL CHECK (reference_table IN ('accounts_payable', 'accounts_receivable')),
  reference_id UUID, -- A primeira instância criada
  day_of_month INTEGER CHECK (day_of_month BETWEEN 1 AND 31 OR day_of_month IS NULL),
  day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6 OR day_of_week IS NULL), -- 0 = Domingo, 6 = Sábado
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  amount NUMERIC NOT NULL,
  currency_id UUID NOT NULL,
  category_id UUID,
  project_id UUID,
  payment_method_id UUID,
  bank_account_id UUID NOT NULL,
  next_generation_date TIMESTAMPTZ NOT NULL,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
  FOREIGN KEY (recurrence_type_id) REFERENCES "recurrence_types"(id) ON DELETE RESTRICT,
  FOREIGN KEY (entity_id) REFERENCES "entities"(id) ON DELETE SET NULL,
  FOREIGN KEY (currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT,
  FOREIGN KEY (category_id) REFERENCES "categories"(id) ON DELETE SET NULL,
  FOREIGN KEY (project_id) REFERENCES "projects"(id) ON DELETE SET NULL,
  FOREIGN KEY (payment_method_id) REFERENCES "payment_methods"(id) ON DELETE SET NULL,
  FOREIGN KEY (bank_account_id) REFERENCES "bank_accounts"(id) ON DELETE RESTRICT
);

COMMENT ON TABLE public.recurring_schedules IS 'Tabela para gerenciar agendamentos recorrentes de contas a pagar e receber';
COMMENT ON COLUMN public.recurring_schedules.reference_table IS 'Nome da tabela de referência (accounts_payable ou accounts_receivable)';
COMMENT ON COLUMN public.recurring_schedules.reference_id IS 'ID da primeira instância criada';
COMMENT ON COLUMN public.recurring_schedules.day_of_month IS 'Dia do mês para recorrências mensais ou superiores';
COMMENT ON COLUMN public.recurring_schedules.day_of_week IS 'Dia da semana para recorrências semanais';
COMMENT ON COLUMN public.recurring_schedules.next_generation_date IS 'Data da próxima geração de conta';

-- Criar índice para facilitar busca por agendamentos a serem processados
CREATE INDEX idx_recurring_schedules_next_generation_date 
ON public.recurring_schedules(next_generation_date);
CREATE INDEX idx_recurring_schedules_active 
ON public.recurring_schedules(active);

-- Função para calcular próxima data de recorrência
CREATE OR REPLACE FUNCTION calculate_next_recurring_date(
  schedule_id UUID
) RETURNS TIMESTAMPTZ AS $$
DECLARE
  rec_schedule RECORD;
  next_date TIMESTAMPTZ;
  rec_type VARCHAR;
BEGIN
  -- Buscar dados do agendamento
  SELECT rs.*, rt.name as recurrence_type_name
  INTO rec_schedule
  FROM recurring_schedules rs
  JOIN recurrence_types rt ON rs.recurrence_type_id = rt.id
  WHERE rs.id = schedule_id;
  
  -- Data base para cálculo será a última data de geração ou a data de início
  next_date := rec_schedule.next_generation_date;
  rec_type := rec_schedule.recurrence_type_name;
  
  -- Calcular próxima data com base no tipo de recorrência
  IF rec_type = 'diario' THEN
    next_date := next_date + INTERVAL '1 day';
  ELSIF rec_type = 'semanal' THEN
    next_date := next_date + INTERVAL '1 week';
  ELSIF rec_type = 'quinzenal' THEN
    next_date := next_date + INTERVAL '15 days';
  ELSIF rec_type = 'mensal' THEN
    next_date := next_date + INTERVAL '1 month';
    -- Ajuste para dia específico do mês se configurado
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'bimestral' THEN
    next_date := next_date + INTERVAL '2 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'trimestral' THEN
    next_date := next_date + INTERVAL '3 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'semestral' THEN
    next_date := next_date + INTERVAL '6 months';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  ELSIF rec_type = 'anual' THEN
    next_date := next_date + INTERVAL '1 year';
    IF rec_schedule.day_of_month IS NOT NULL THEN
      next_date := date_trunc('month', next_date) + ((rec_schedule.day_of_month - 1) || ' days')::interval;
    END IF;
  END IF;
  
  -- Verificar se a data não ultrapassa a data de fim (se existir)
  IF rec_schedule.end_date IS NOT NULL AND next_date > rec_schedule.end_date THEN
    RETURN NULL; -- Indica que não haverã mais recorrências
  END IF;
  
  RETURN next_date;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar contas recorrentes agendadas
CREATE OR REPLACE FUNCTION generate_recurring_accounts() RETURNS INTEGER AS $$
DECLARE
  rec_schedule RECORD;
  new_id UUID;
  accounts_created INTEGER := 0;
BEGIN
  -- Processar todos os agendamentos ativos que já estão no prazo
  FOR rec_schedule IN
    SELECT *
    FROM recurring_schedules
    WHERE active = true
      AND next_generation_date <= CURRENT_TIMESTAMP
      AND (end_date IS NULL OR end_date >= CURRENT_TIMESTAMP)
      AND deleted_at IS NULL
  LOOP
    -- Gerar nova conta com base no tipo (a pagar ou a receber)
    IF rec_schedule.reference_table = 'accounts_payable' THEN
      INSERT INTO accounts_payable (
        company_id, description, entity_id, due_date, amount, status,
        category_id, project_id, payment_method_id, bank_account_id, currency_id
      ) VALUES (
        rec_schedule.company_id, rec_schedule.description, rec_schedule.entity_id,
        rec_schedule.next_generation_date, rec_schedule.amount, 'open',
        rec_schedule.category_id, rec_schedule.project_id, rec_schedule.payment_method_id,
        rec_schedule.bank_account_id, rec_schedule.currency_id
      )
      RETURNING id INTO new_id;
      
    ELSIF rec_schedule.reference_table = 'accounts_receivable' THEN
      INSERT INTO accounts_receivable (
        company_id, description, entity_id, due_date, amount, status,
        category_id, project_id, payment_method_id, bank_account_id, currency_id
      ) VALUES (
        rec_schedule.company_id, rec_schedule.description, rec_schedule.entity_id,
        rec_schedule.next_generation_date, rec_schedule.amount, 'open',
        rec_schedule.category_id, rec_schedule.project_id, rec_schedule.payment_method_id,
        rec_schedule.bank_account_id, rec_schedule.currency_id
      )
      RETURNING id INTO new_id;
    END IF;
    
    accounts_created := accounts_created + 1;
    
    -- Calcular próxima data de geração
    UPDATE recurring_schedules
    SET next_generation_date = calculate_next_recurring_date(id),
        active = CASE WHEN calculate_next_recurring_date(id) IS NULL THEN false ELSE true END
    WHERE id = rec_schedule.id;
  END LOOP;
  
  RETURN accounts_created;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION generate_recurring_accounts() IS 'Função para ser executada diariamente via cron para gerar contas recorrentes';

-- Adiciona RLS para a tabela de agendamentos recorrentes
ALTER TABLE public.recurring_schedules ENABLE ROW LEVEL SECURITY;

-- Política para agendamentos recorrentes (acesso aos agendamentos das empresas do usuário)
CREATE POLICY recurring_schedules_access ON public.recurring_schedules
  USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
  
-- Política otimizada para consultas SELECT de agendamentos recorrentes
CREATE POLICY recurring_schedules_select ON public.recurring_schedules FOR SELECT
  USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- ====================================================
-- 11. FUNÇÕES OTIMIZADAS PARA VERIFICAÇÃO DE PERMISSÕES
-- ====================================================

-- Função para verificar se um usuário tem uma determinada permissão em uma empresa
CREATE OR REPLACE FUNCTION user_has_permission(p_user_id UUID, p_company_id UUID, p_permission VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
  has_permission BOOLEAN;
BEGIN
  -- Verificar cache primeiro (pode ser implementado com uma tabela temporária ou Redis na aplicação)
  
  -- Consulta otimizada com índices e limitação de resultados
  SELECT EXISTS (
    SELECT 1
    FROM user_company_roles ucr
    JOIN role_permissions rp ON ucr.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.id
    WHERE ucr.user_id = p_user_id
    AND ucr.company_id = p_company_id
    AND p.action = p_permission
    AND ucr.deleted_at IS NULL
    AND p.deleted_at IS NULL
    LIMIT 1
  ) INTO has_permission;
  
  RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public, pg_temp;

COMMENT ON FUNCTION user_has_permission(UUID, UUID, VARCHAR) IS 'Verifica se um usuário tem uma determinada permissão em uma empresa de forma otimizada';

-- Função para obter todas as permissões de um usuário em uma empresa
CREATE OR REPLACE FUNCTION get_user_permissions(p_user_id UUID, p_company_id UUID)
RETURNS TABLE (permission_code VARCHAR, permission_description VARCHAR) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT p.action, p.description
  FROM user_company_roles ucr
  JOIN role_permissions rp ON ucr.role_id = rp.role_id
  JOIN permissions p ON rp.permission_id = p.id
  WHERE ucr.user_id = p_user_id
  AND ucr.company_id = p_company_id
  AND ucr.deleted_at IS NULL
  AND p.deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public, pg_temp;

COMMENT ON FUNCTION get_user_permissions(UUID, UUID) IS 'Retorna todas as permissões de um usuário em uma empresa';

-- Função para manter o cache de permissões atualizado
CREATE OR REPLACE FUNCTION refresh_permissions_cache(p_user_id UUID DEFAULT NULL)
RETURNS VOID AS $$
BEGIN
  -- Esta função seria implementada na aplicação para atualizar um cache Redis ou similar
  -- Aqui apenas definimos a interface
  
  -- Se p_user_id for NULL, atualiza o cache para todos os usuários
  -- Se não, atualiza apenas para o usuário específico
  
  -- Lógica da aplicação implementará este cache
  NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public, pg_temp;

COMMENT ON FUNCTION refresh_permissions_cache(UUID) IS 'Função para atualizar o cache de permissões';

-- Trigger para atualizar o cache quando as permissões são modificadas
CREATE OR REPLACE FUNCTION update_permissions_cache_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Chamar a função de refresh no cache
  -- Na implementação real, isto pode notificar a aplicação para atualizar o cache
  PERFORM refresh_permissions_cache();
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Aplicar triggers nas tabelas relacionadas a permissões
CREATE TRIGGER update_permissions_cache
AFTER INSERT OR UPDATE OR DELETE ON role_permissions
FOR EACH STATEMENT EXECUTE FUNCTION update_permissions_cache_trigger();

CREATE TRIGGER update_user_roles_cache
AFTER INSERT OR UPDATE OR DELETE ON user_company_roles
FOR EACH STATEMENT EXECUTE FUNCTION update_permissions_cache_trigger();

-- ====================================================
-- 12. FUNÇÕES OTIMIZADAS PARA CÁLCULO DE SALDO COM MÚLTIPLAS MOEDAS
-- ====================================================

-- Função para calcular o saldo de uma conta bancária considerando conversão de moedas
CREATE OR REPLACE FUNCTION calculate_bank_account_balance_with_currency(
  p_account_id UUID,
  p_target_currency_id UUID DEFAULT NULL
)
RETURNS TABLE (
  original_balance NUMERIC,
  converted_balance NUMERIC,
  original_currency_id UUID,
  original_currency_code VARCHAR(3),
  target_currency_id UUID,
  target_currency_code VARCHAR(3)
) AS $$
DECLARE
  account_currency_id UUID;
  account_currency_code VARCHAR(3);
  target_currency_code VARCHAR(3);
  exchange_rate NUMERIC;
BEGIN
  -- Obter a moeda padrão da conta bancária
  SELECT ba.currency_id, c.code INTO account_currency_id, account_currency_code
  FROM bank_accounts ba
  JOIN currencies c ON ba.currency_id = c.id
  WHERE ba.id = p_account_id;
  
  -- Se a moeda alvo não foi especificada, usar a moeda da conta
  IF p_target_currency_id IS NULL THEN
    p_target_currency_id := account_currency_id;
  END IF;
  
  -- Obter o código da moeda alvo
  SELECT code INTO target_currency_code
  FROM currencies
  WHERE id = p_target_currency_id;
  
  -- Calcular o saldo na moeda original
  SELECT
    COALESCE(SUM(
      CASE
        WHEN t.transaction_type = 'income' THEN t.amount
        WHEN t.transaction_type = 'expense' THEN -t.amount
        WHEN t.transaction_type = 'transfer' AND t.bank_account_id = p_account_id THEN -t.amount
        WHEN t.transaction_type = 'transfer' AND t.destination_bank_account_id = p_account_id THEN t.amount
        ELSE 0
      END
    ), 0) INTO original_balance
  FROM transactions t
  WHERE (t.bank_account_id = p_account_id OR t.destination_bank_account_id = p_account_id)
    AND t.deleted_at IS NULL;
    
  -- Se as moedas são iguais, o saldo convertido é igual ao original
  IF account_currency_id = p_target_currency_id THEN
    exchange_rate := 1;
  ELSE
    -- Obter a taxa de câmbio mais recente
    -- Esta é uma implementação simplificada. Em um sistema real,
    -- você teria uma tabela de taxas de câmbio e uma lógica mais complexa
    -- para obter a taxa de câmbio correta para cada transação baseada na data
    SELECT rate INTO exchange_rate
    FROM (
      SELECT rate
      FROM exchange_rates
      WHERE from_currency_id = account_currency_id
        AND to_currency_id = p_target_currency_id
        AND effective_date <= NOW()
      ORDER BY effective_date DESC
      LIMIT 1
    ) AS latest_rate;
    
    -- Se não encontrou taxa direta, tentar através de uma moeda intermediária (ex: USD)
    IF exchange_rate IS NULL THEN
      -- Encontrar uma moeda intermediária (geralmente USD ou EUR)
      DECLARE
        intermediate_currency_id UUID;
        rate_to_intermediate NUMERIC;
        rate_from_intermediate NUMERIC;
      BEGIN
        -- Pegar o ID do USD ou EUR
        SELECT id INTO intermediate_currency_id
        FROM currencies
        WHERE code = 'USD'
        LIMIT 1;
        
        -- Taxa da moeda da conta para a intermediária
        SELECT rate INTO rate_to_intermediate
        FROM (
          SELECT rate
          FROM exchange_rates
          WHERE from_currency_id = account_currency_id
            AND to_currency_id = intermediate_currency_id
            AND effective_date <= NOW()
          ORDER BY effective_date DESC
          LIMIT 1
        ) AS rate1;
        
        -- Taxa da intermediária para a moeda alvo
        SELECT rate INTO rate_from_intermediate
        FROM (
          SELECT rate
          FROM exchange_rates
          WHERE from_currency_id = intermediate_currency_id
            AND to_currency_id = p_target_currency_id
            AND effective_date <= NOW()
          ORDER BY effective_date DESC
          LIMIT 1
        ) AS rate2;
        
        -- Calcular a taxa composta
        IF rate_to_intermediate IS NOT NULL AND rate_from_intermediate IS NOT NULL THEN
          exchange_rate := rate_to_intermediate * rate_from_intermediate;
        ELSE
          -- Se não conseguir calcular, usar 1 como taxa
          exchange_rate := 1;
        END IF;
      END;
    END IF;
  END IF;
  
  -- Calcular o saldo convertido
  converted_balance := original_balance * exchange_rate;
  
  -- Retornar os resultados
  RETURN QUERY
  SELECT 
    original_balance,
    converted_balance,
    account_currency_id,
    account_currency_code,
    p_target_currency_id,
    target_currency_code;
    
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION calculate_bank_account_balance_with_currency(UUID, UUID) IS 'Calcula o saldo de uma conta bancária com conversão de moeda';

-- Tabela para armazenar taxas de câmbio
CREATE TABLE IF NOT EXISTS public.exchange_rates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL,
  from_currency_id UUID NOT NULL,
  to_currency_id UUID NOT NULL,
  rate NUMERIC NOT NULL,
  effective_date TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ,
  FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
  FOREIGN KEY (from_currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT,
  FOREIGN KEY (to_currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT
);

COMMENT ON TABLE public.exchange_rates IS 'Tabela para armazenar taxas de câmbio entre moedas';
COMMENT ON COLUMN public.exchange_rates.from_currency_id IS 'Moeda de origem';
COMMENT ON COLUMN public.exchange_rates.to_currency_id IS 'Moeda de destino';
COMMENT ON COLUMN public.exchange_rates.rate IS 'Taxa de câmbio';
COMMENT ON COLUMN public.exchange_rates.effective_date IS 'Data de vigência da taxa';

-- Habilitar RLS para a tabela de taxas de câmbio
ALTER TABLE public.exchange_rates ENABLE ROW LEVEL SECURITY;

-- Política para taxas de câmbio (acesso às taxas das empresas do usuário)
CREATE POLICY exchange_rates_access ON public.exchange_rates
  USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
  
-- Política otimizada para consultas SELECT de taxas de câmbio
CREATE POLICY exchange_rates_select ON public.exchange_rates FOR SELECT
  USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Índices para a tabela de taxas de câmbio
CREATE INDEX IF NOT EXISTS idx_exchange_rates_company_id ON public.exchange_rates(company_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_from_currency_id ON public.exchange_rates(from_currency_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_to_currency_id ON public.exchange_rates(to_currency_id);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_effective_date ON public.exchange_rates(effective_date);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_composite ON public.exchange_rates(from_currency_id, to_currency_id, effective_date);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_deleted_at ON public.exchange_rates(deleted_at) WHERE deleted_at IS NULL;
