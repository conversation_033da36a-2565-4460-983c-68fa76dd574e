# Plano para Criação do Documento ANALISE_FLUXOMAX.md

Este documento detalha o plano para a criação do arquivo `ANALISE_FLUXOMAX.md`, que conterá uma análise abrangente do sistema FluxoMax, com foco no parcelamento de contas e no controle por categorias/subcategorias.

## 1. Estrutura do Documento `ANALISE_FLUXOMAX.md`

O documento final será estruturado da seguinte forma:

*   **Introdução**
    *   Objetivo da análise
    *   Escopo da análise (foco em parcelamento e categorias)
*   **Perspectiva do Arquiteto de Software**
    *   Visão Geral da Arquitetura (componentes principais: Frontend, Backend, Banco de Dados)
    *   Modelagem de Dados Chave (principais entidades envolvidas: `accounts_payable`, `accounts_receivable`, `transactions`, `categories`, e como elas se relacionam para suportar parcelamento e categorização)
        *   Inclusão de um Diagrama de Entidade e Relacionamento (DER) utilizando Mermaid.
    *   Principais Fluxos de Dados relacionados ao parcelamento e categorização.
*   **Perspectiva do Desenvolvedor de Software**
    *   **Detalhes da Implementação do Parcelamento**
        *   Explicação de como contas parceladas são representadas (múltiplos registros, `parentId`, `installments`, `installmentNumber`).
        *   Fluxo de Criação de Contas Parceladas:
            *   Diagrama de Sequência (Mermaid) ilustrando a interação entre cliente (frontend) e backend para criar as múltiplas parcelas.
        *   Fluxo de Liquidação de Parcelas:
            *   Diagrama de Sequência (Mermaid) mostrando a criação da `Transaction` e sua vinculação à parcela.
            *   Destaque para o "gap" identificado: a ausência de atualização automática do `status` e `paidAmount`/`receivedAmount` da parcela no mesmo fluxo de criação da transação no backend.
    *   **Detalhes da Implementação de Categorias e Subcategorias**
        *   Explicação da estrutura hierárquica de `Category` (uso do `parentId`).
        *   Como categorias/subcategorias são associadas a contas a pagar/receber no momento do cadastro.
        *   Como categorias são associadas a transações.
        *   Implicações para relatórios (prioridade da categoria da transação).
    *   **Pontos de Atenção e Possíveis Melhorias**
        *   Discussão sobre o "gap" na liquidação de parcelas e sugestões para abordá-lo (ex: atualização atômica no backend).
*   **Perspectiva do Gerente de Produto**
    *   Funcionalidades Cobertas pela Análise (parcelamento de contas, categorização de despesas/receitas).
    *   User Stories Chave (exemplos práticos de como um usuário interage com o parcelamento e as categorias).
    *   Impacto do "Gap" na Liquidação de Parcelas na Experiência do Usuário e na integridade dos dados.
*   **Conclusão e Recomendações**
    *   Resumo das principais descobertas.
    *   Recomendações para futuras evoluções ou correções.

## 2. Conteúdo Específico sobre Parcelamento

*   Explicação detalhada de como uma conta a pagar/receber é dividida em múltiplas parcelas (modelagem como registros individuais com `parentId`, e o papel dos campos `installments` e `installmentNumber`).
*   Diagrama de Sequência: "Criação de Conta a Receber Parcelada" (detalhando as chamadas do frontend para o backend, e como os dados de cada parcela são enviados).
*   Diagrama de Sequência: "Pagamento de Parcela de Conta a Receber" (mostrando a criação da transação, sua vinculação à parcela específica, e a observação sobre a não atualização do status/valor da parcela no mesmo fluxo).
*   Discussão sobre o gerenciamento do `status` (PENDENTE, PAGO, VENCIDO, etc.) e dos campos `receivedAmount`/`paidAmount` para cada parcela individual.

## 3. Conteúdo Específico sobre Categorias e Subcategorias

*   Explicação da implementação da hierarquia de categorias utilizando o campo `parentId` na entidade `Category`.
*   Como uma subcategoria é informada no cadastro de contas a pagar/receber (o usuário seleciona a subcategoria, e o `categoryId` da conta/parcela armazena o ID dessa subcategoria).
*   Como as categorias (e, por extensão, subcategorias através da hierarquia) são utilizadas na geração de relatórios financeiros, com ênfase na prioridade da categoria definida na `Transaction`.
*   Diagrama de Classes (simplificado em Mermaid) mostrando as entidades `Category`, `AccountPayable`, `AccountReceivable`, `Transaction` e seus atributos e relacionamentos relevantes para a categorização.

## 4. Criação dos Diagramas Mermaid

Serão criados os seguintes diagramas em sintaxe Mermaid para inclusão no documento:

*   **Diagrama de Entidade e Relacionamento (DER):** Focado nas tabelas `accounts_payable`, `accounts_receivable`, `transactions`, `categories` e os campos que suportam o parcelamento e a hierarquia de categorias.
*   **Diagramas de Sequência:**
    *   Criação de Conta Parcelada.
    *   Pagamento/Recebimento de Parcela.
*   **Diagrama de Classes:** Simplificado, mostrando as principais entidades de negócio e seus relacionamentos.

Este plano servirá como guia para a elaboração do documento `ANALISE_FLUXOMAX.md`.