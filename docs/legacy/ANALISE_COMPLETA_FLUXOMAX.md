# Análise Completa do FluxoMax

---

## Visão Geral do Produto

O **FluxoMax** é uma plataforma multi-tenant de gestão financeira para PMEs, com foco em controle de contas a pagar/receber, transações, projetos, entidades (clientes/fornecedores) e integração bancária. O sistema prioriza segurança, escalabilidade e experiência do usuário, com arquitetura moderna e recursos avançados como parcelamento, pagamentos parciais, relatórios customizados e RBAC granular.

---

## Arquitetura de Software

### Vis<PERSON>

```mermaid
graph LR
  subgraph Frontend
    FE[React + Vite + Tailwind + shadcn-ui]
  end
  subgraph Backend
    BE[NestJS + Prisma]
  end
  subgraph Database
    DB[(PostgreSQL)]
  end
  FE -- REST/Swagger --> BE
  BE -- Prisma ORM --> DB
```

- **Frontend**: React, TypeScript, Vite, <PERSON>lwind, shadcn-ui, React Query, Recharts
- **Backend**: NestJS, TypeScript, Prisma, JWT, RBAC, Swagger
- **Banco**: PostgreSQL, RLS, multi-tenant, índices otimizados
- **Infra**: Docker Compose, ambientes dev/prod, hot-reload, scripts de automação

### Diagrama de Infraestrutura

```mermaid
graph TD
  User((Usuário))
  Browser[Browser]
  FE[Frontend (React/Vite)]
  BE[Backend (NestJS)]
  DB[(PostgreSQL)]
  User --> Browser --> FE --> BE --> DB
```

- **Ambiente de desenvolvimento**: hot-reload, volumes Docker, scripts `dev.sh`
- **Ambiente de produção**: containers isolados, persistência de dados, escalabilidade horizontal

---

## Modelagem de Dados (Domínio)

### Diagrama Simplificado das Entidades

```mermaid
classDiagram
  Company <|-- UserCompanyRole
  User <|-- UserCompanyRole
  Role <|-- UserCompanyRole
  Role <|-- RolePermission
  Permission <|-- RolePermission
  Company <|-- Role
  Company <|-- Permission
  Company <|-- Category
  Company <|-- Project
  Company <|-- BankAccount
  Company <|-- Entity
  Company <|-- AccountsPayable
  Company <|-- AccountsReceivable
  Company <|-- Transaction
  Company <|-- CustomPeriod
  Category <|-- AccountsPayable
  Category <|-- AccountsReceivable
  Category <|-- Transaction
  Project <|-- AccountsPayable
  Project <|-- AccountsReceivable
  Project <|-- Transaction
  Entity <|-- AccountsPayable
  Entity <|-- AccountsReceivable
  Entity <|-- Transaction
  BankAccount <|-- AccountsPayable
  BankAccount <|-- AccountsReceivable
  BankAccount <|-- Transaction
  AccountsPayable <|-- Transaction
  AccountsReceivable <|-- Transaction
```

- **Principais entidades**: Company, User, Role, Permission, Category, Project, Entity, BankAccount, AccountsPayable, AccountsReceivable, Transaction
- **Multi-tenancy**: Isolamento por Company, RLS no banco
- **RBAC**: Papéis e permissões por empresa
- **Parcelamento**: Relação pai/filho em AccountsPayable/Receivable
- **Recorrência**: RecurringSchedule, RecurrenceType

---

## Fluxos Principais de Uso

### Cadastro de Conta a Pagar/Receber

```mermaid
sequenceDiagram
  participant U as Usuário
  participant FE as Frontend
  participant BE as Backend
  participant DB as Banco
  U->>FE: Preenche formulário
  FE->>BE: POST /accounts_payable
  BE->>DB: Cria registro (AccountsPayable)
  DB-->>BE: Confirmação
  BE-->>FE: Sucesso
  FE-->>U: Feedback visual
```

### Pagamento Parcial

```mermaid
sequenceDiagram
  participant U as Usuário
  participant FE as Frontend
  participant BE as Backend
  participant DB as Banco
  U->>FE: Inicia pagamento parcial
  FE->>BE: POST /accounts_payable/:id/pay (parcial)
  BE->>DB: Atualiza valor pago, status, gera Transaction
  DB-->>BE: Confirmação
  BE-->>FE: Sucesso
  FE-->>U: Feedback visual
```

### Geração de Relatórios

```mermaid
sequenceDiagram
  participant U as Usuário
  participant FE as Frontend
  participant BE as Backend
  participant DB as Banco
  U->>FE: Seleciona filtros
  FE->>BE: GET /reports?filtros
  BE->>DB: Query otimizada
  DB-->>BE: Dados agregados
  BE-->>FE: JSON
  FE-->>U: Exibe gráficos/tabelas
```

---

## Padrões de Desenvolvimento e Stack

- **Frontend**: React, Vite, Tailwind, shadcn-ui, React Query, Zod, Recharts, Context API, hooks customizados
- **Backend**: NestJS, Prisma, JWT, RBAC, validação, Swagger, testes
- **Banco**: PostgreSQL, RLS, índices, multi-tenant, migrações Prisma
- **DevOps**: Docker Compose, scripts de automação, ambientes dev/prod, hot-reload
- **Testes**: Unitários e integração (Jest, Supertest, React Testing Library)
- **CI/CD**: Pipeline sugerido: lint, test, build, deploy

---

## Diretrizes de UX/UI

- **Clareza e Consistência**: Layout limpo, grid 12 colunas, tipografia legível
- **Acessibilidade**: WCAG 2.1, navegação por teclado, contraste adequado
- **Feedback**: Toasts, loaders, mensagens de erro/sucesso
- **Temas**: Claro/escuro
- **Componentização**: shadcn-ui, design system próprio
- **Onboarding**: Tooltips, tour guiado
- **Responsividade**: Mobile-first, breakpoints otimizados

---

## Roadmap e Visão de Produto

- **MVP**: Multi-tenant, contas a pagar/receber, transações, projetos, entidades, relatórios, RBAC, autenticação JWT
- **Próximos passos**:
  - Integração bancária automática (Open Finance)
  - Dashboard customizável
  - Notificações push/mobile
  - API pública para integrações
  - Módulo de conciliação bancária
  - Expansão de relatórios e BI

---

## Conclusão

O FluxoMax é uma solução robusta, escalável e segura para gestão financeira multi-empresa, com arquitetura moderna, UX refinada e potencial de expansão para integrações e automações financeiras avançadas. 