# Análise Abrangente do Projeto FluxoMax

Este documento fornece uma análise detalhada do projeto FluxoMax sob múltiplas perspectivas: Arquiteto de Software, Desenvolvedor de Software e Gerente de Produto.

## 1. Perspectiva do Arquiteto de Software

### 1.1. Visão Geral da Arquitetura

O FluxoMax é um sistema de gestão financeira multi-tenant projetado para pequenas e médias empresas. A arquitetura é dividida em três componentes principais: Frontend, Backend e Banco de Dados, todos orquestrados utilizando Docker para ambientes de desenvolvimento e produção.

```mermaid
graph TD
    A[Usuário (Navegador)] --> B{Frontend (React/Vite)};
    B --> C{Backend (NestJS)};
    C --> D[Banco de Dados (PostgreSQL)];
    C --> E[Prisma ORM];
    D -- Interação via --> E;

    subgraph "Ambiente Dockerizado"
        direction LR
        B
        C
        D
    end

    F[Docker Compose] -- Gerencia --> B;
    F -- Gerencia --> C;
    F -- Gerencia --> D;
```

### 1.2. Tecnologias Chave

*   **Frontend:**
    *   Linguagem: TypeScript
    *   Framework/Biblioteca: React
    *   Build Tool: Vite
    *   Estilização: Tailwind CSS
    *   Componentes UI: shadcn/ui
    *   Porta: 3001 (desenvolvimento)
*   **Backend:**
    *   Linguagem: TypeScript
    *   Framework: NestJS
    *   ORM: Prisma
    *   Banco de Dados: PostgreSQL
    *   Autenticação: JWT com Passport
    *   API: RESTful
    *   Documentação API: Swagger (`/api/docs`)
    *   Porta: 3000 (desenvolvimento)
*   **Banco de Dados:**
    *   SGBD: PostgreSQL
    *   Porta: 5432 (desenvolvimento)
*   **Containerização:**
    *   Docker, Docker Compose
    *   Script de desenvolvimento: `dev.sh`
    *   Arquivo de composição: `compose.yaml`, `docker-compose.dev.yml`

### 1.3. Multi-Tenancy

A arquitetura suporta multi-tenancy através de uma abordagem de **discriminador de coluna**. A entidade central `Company` atua como o tenant, e a maioria das outras entidades de negócios (como `Project`, `Transaction`, `Entity`, `Role`, `Permission`) possui uma referência direta (`companyId`) a ela. Isso garante o isolamento dos dados por empresa dentro das mesmas tabelas do banco de dados.

Um `User` pode estar associado a múltiplas `Company`s através da tabela de junção `UserCompanyRole`, que também define o `Role` do usuário naquela empresa específica.

```mermaid
erDiagram
    USER ||--o{ USER_COMPANY_ROLE : "tem acesso a"
    COMPANY ||--o{ USER_COMPANY_ROLE : "tem usuário com papel"
    COMPANY ||--o{ PROJECT : "possui"
    COMPANY ||--o{ TRANSACTION : "registra"
    COMPANY ||--o{ ACCOUNT_PAYABLE : "tem"
    COMPANY ||--o{ ACCOUNT_RECEIVABLE : "tem"
    COMPANY ||--o{ ENTITY : "cadastra"
    COMPANY ||--o{ BANK_ACCOUNT : "mantém"
    COMPANY ||--o{ ROLE : "define"

    USER_COMPANY_ROLE }|--|| ROLE : "assume papel"

    USER {
        string id PK
        string email
    }
    COMPANY {
        string id PK
        string name
        string cnpj
    }
    USER_COMPANY_ROLE {
        string userId FK
        string companyId FK
        string roleId FK
    }
    PROJECT {
        string id PK
        string companyId FK
        string name
    }
    TRANSACTION {
        string id PK
        string companyId FK
        string description
    }
    ACCOUNT_PAYABLE {
        string id PK
        string companyId FK
    }
    ACCOUNT_RECEIVABLE {
        string id PK
        string companyId FK
    }
    ENTITY {
        string id PK
        string companyId FK
    }
    BANK_ACCOUNT {
        string id PK
        string companyId FK
    }
    ROLE {
        string id PK
        string companyId FK
        string name
    }
```

### 1.4. Autenticação e Autorização

A autenticação é gerenciada via JWT (JSON Web Tokens) com o auxílio da biblioteca Passport.js no backend. O sistema inclui funcionalidades de registro, login, renovação de token, logout e alteração de senha. O `schema.prisma` inclui um modelo `RefreshToken` para suportar sessões de usuário mais longas e permitir a revogação de tokens.

**Fluxo de Login (`POST /auth/login`):**

1.  O cliente envia `email` e `password` para o `AuthController`.
2.  O `AuthController` chama `authService.login()`.
3.  `authService.validateUser()`:
    *   Busca o usuário pelo `email` no banco de dados (incluindo o hash da senha).
    *   Compara a senha fornecida com o hash armazenado usando `bcrypt.compare()`.
    *   Se a validação for bem-sucedida, retorna o `userId`.
4.  `authService.generateTokens()`:
    *   Busca a `companyId` padrão do usuário através de `usersService.getUserDefaultCompany(userId)`. Esta `companyId` é crucial para o contexto multi-tenant.
    *   Cria o payload do `accessToken` JWT, incluindo `sub` (userId), `email`, e o `companyId` padrão.
    *   Assina o `accessToken`.
    *   Gera um `refreshToken` (UUID) e define sua data de expiração.
    *   Armazena o `refreshToken` no banco de dados, associado ao `userId`.
    *   Retorna o `accessToken` e o `refreshToken` para o cliente.

```mermaid
sequenceDiagram
    participant Client
    participant AuthController
    participant AuthService
    participant UsersService
    participant PrismaService as DB

    Client->>AuthController: POST /auth/login (email, password)
    AuthController->>AuthService: login(loginDto)
    AuthService->>AuthService: validateUser(email, password)
    AuthService->>DB: Busca usuário por email (com senha)
    DB-->>AuthService: Retorna dados do usuário (ou nulo)
    AuthService->>AuthService: Compara senha (bcrypt)
    alt Credenciais Inválidas
        AuthService-->>AuthController: UnauthorizedException
        AuthController-->>Client: 401 Unauthorized
    else Credenciais Válidas
        AuthService->>UsersService: getUserDefaultCompany(userId)
        UsersService->>DB: Busca companyId padrão do usuário
        DB-->>UsersService: Retorna companyId
        UsersService-->>AuthService: Retorna companyId
        AuthService->>AuthService: generateTokens(userId, email, companyId)
        Note over AuthService,DB: Cria payload JWT (sub, email, companyId)
        Note over AuthService,DB: Assina accessToken
        Note over AuthService,DB: Gera refreshToken (UUID)
        AuthService->>DB: Salva refreshToken
        DB-->>AuthService: Confirmação
        AuthService-->>AuthController: Retorna {accessToken, refreshToken}
        AuthController-->>Client: 200 OK {accessToken, refreshToken}
    end
```

**Fluxo de Renovação de Token (`POST /auth/refresh-token`):**

1.  O cliente envia o `refreshToken` para o `AuthController`.
2.  O `AuthController` chama `authService.refreshToken()`.
3.  `authService`:
    *   Verifica se o `refreshToken` existe no banco de dados e se não está expirado.
    *   Se inválido ou expirado, lança `UnauthorizedException`.
    *   Se válido, remove o `refreshToken` antigo do banco.
    *   Chama `generateTokens()` (conforme descrito acima) para criar um novo par de `accessToken` e `refreshToken`.
    *   Retorna o novo par de tokens para o cliente.

```mermaid
sequenceDiagram
    participant Client
    participant AuthController
    participant AuthService
    participant UsersService
    participant PrismaService as DB

    Client->>AuthController: POST /auth/refresh-token (refreshToken)
    AuthController->>AuthService: refreshToken(refreshTokenDto)
    AuthService->>DB: Busca refreshToken e dados do usuário associado
    DB-->>AuthService: Retorna refreshTokenRecord (ou nulo)
    alt Refresh Token Inválido ou Expirado
        AuthService-->>AuthController: UnauthorizedException
        AuthController-->>Client: 401 Unauthorized
    else Refresh Token Válido
        AuthService->>DB: Remove refreshToken antigo
        DB-->>AuthService: Confirmação
        AuthService->>UsersService: getUserDefaultCompany(userId)
        UsersService->>DB: Busca companyId padrão do usuário
        DB-->>UsersService: Retorna companyId
        UsersService-->>AuthService: Retorna companyId
        AuthService->>AuthService: generateTokens(userId, email, companyId)
        Note over AuthService,DB: Cria payload JWT (sub, email, companyId)
        Note over AuthService,DB: Assina accessToken
        Note over AuthService,DB: Gera novo refreshToken (UUID)
        AuthService->>DB: Salva novo refreshToken
        DB-->>AuthService: Confirmação
        AuthService-->>AuthController: Retorna {accessToken, refreshToken}
        AuthController-->>Client: 200 OK {accessToken, refreshToken}
    end
```

**Payload do JWT e Multi-Tenancy:**
A inclusão do `companyId` padrão do usuário no payload do `accessToken` é uma peça chave para a arquitetura multi-tenant. Nas requisições subsequentes às rotas protegidas, o `JwtStrategy` (não visualizado em detalhes ainda, mas presente no `AuthModule`) irá validar o token e extrair seu payload. Os serviços do backend podem então usar o `companyId` do token para filtrar dados e garantir que as operações sejam realizadas estritamente dentro do contexto da empresa à qual o usuário está atualmente associado (conforme seu token). Isso elimina a necessidade de passar o `companyId` explicitamente em cada requisição após o login.

A relação entre `Project` e `Transaction` é definida no `schema.prisma`. O modelo `Transaction` possui um campo opcional `projectId` que se relaciona com o `id` do modelo `Project` (`project Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)`). Inversamente, o modelo `Project` possui um campo `transactions Transaction[]`. Esta relação permite vincular despesas e receitas diretamente a projetos, o que é essencial para o acompanhamento de custos e orçamentos. A política `onDelete: SetNull` garante que, se um projeto for excluído, as transações associadas não sejam perdidas, apenas desvinculadas.

**Controle de Acesso Baseado em Papéis (RBAC):**
A autorização é implementada através de um robusto sistema de RBAC:

*   **`SystemPermission`**: Define as permissões granulares disponíveis em todo o sistema (e.g., `create_project`, `view_financial_reports`). Cada permissão é identificada por um `code` e associada a um `module`.
*   **`Permission`**: Representa a concessão de uma `SystemPermission` (ou uma permissão customizada) dentro do contexto de uma `Company` específica. Ela é vinculada à `companyId`.
*   **`Role`**: Um papel (e.g., Administrador, Financeiro, Vendedor) é definido por `Company` e agrupa um conjunto de `Permission`s através da tabela de junção `RolePermission`.
*   **`UserCompanyRole`**: Associa um `User` a um `Role` específico dentro de uma `Company`.

Este design permite um controle de acesso flexível e granular, adaptado às necessidades de cada empresa (tenant).

```mermaid
erDiagram
    USER ||--o{ USER_COMPANY_ROLE : "pode ter múltiplos"
    USER_COMPANY_ROLE }o--|| COMPANY : "empresa"
    USER_COMPANY_ROLE }o--|| ROLE : "papel"

    COMPANY ||--o{ ROLE : "define"
    COMPANY ||--o{ PERMISSION : "configura (instância)"

    ROLE ||--o{ ROLE_PERMISSION : "tem permissões"
    ROLE_PERMISSION }o--|| PERMISSION : "permissão"
    PERMISSION }o--|| SYSTEM_PERMISSION : "baseada em (global)"
```

## 2. Perspectiva do Desenvolvedor de Software

### 2.1. Estrutura do Código Fonte

#### 2.1.1. Frontend (`frontend/src`)

A estrutura do frontend segue um padrão comum em aplicações React modernas:
*   `app/`: Provavelmente contém a configuração de rotas e layouts principais da aplicação.
*   `components/`: Componentes React reutilizáveis, organizados por funcionalidade (e.g., `accounts`, `auth`, `dashboard`).
    *   `ui/`: Componentes de UI básicos, provavelmente provenientes ou customizações de `shadcn/ui`.
*   `contexts/`: Contextos React para gerenciamento de estado global ou compartilhado entre componentes.
*   `hooks/`: Hooks personalizados para encapsular lógica reutilizável.
*   `lib/`: Utilitários gerais e configurações (e.g., `utils.ts`, instâncias de clientes HTTP).
*   `pages/`: Componentes que representam as diferentes telas/rotas da aplicação.
*   `services/`: Módulos responsáveis pela comunicação com a API backend (chamadas HTTP).
*   `types/`: Definições de tipos e interfaces TypeScript para o frontend.
*   `App.tsx`: Componente React raiz da aplicação.
*   `main.tsx`: Ponto de entrada da aplicação, onde o React é montado no DOM.

#### 2.1.2. Backend (`backend/src`)

O backend NestJS é altamente modular e bem estruturado, conforme evidenciado pelo `app.module.ts`. Cada funcionalidade principal é encapsulada em seu próprio módulo, promovendo separação de responsabilidades e manutenibilidade.

*   **Módulos Principais (`app.module.ts`):** O `AppModule` importa uma série de módulos de funcionalidade, como `AuthModule`, `UsersModule`, `CompaniesModule`, `ProjectsModule`, `TransactionsModule`, `RolesModule`, `RbacModule`, `ReportsModule`, entre outros. Também importa módulos de suporte como `ConfigModule`, `PrismaModule`, e `UtilsModule`.

    ```mermaid
    graph TD
        AppModule --> ConfigModule[ConfigModule: Variáveis Ambiente]
        AppModule --> PrismaModule[PrismaModule: ORM]
        AppModule --> UtilsModule[UtilsModule: Utilitários]
        AppModule --> AuthModule[AuthModule: Autenticação]
        AppModule --> UsersModule[UsersModule: Usuários]
        AppModule --> CompaniesModule[CompaniesModule: Empresas/Tenants]
        AppModule --> ProjectsModule[ProjectsModule: Projetos]
        AppModule --> TransactionsModule[TransactionsModule: Transações]
        AppModule --> RolesModule[RolesModule: Papéis]
        AppModule --> RbacModule[RbacModule: Lógica RBAC]
        AppModule --> ReportsModule[ReportsModule: Relatórios]
        AppModule --> TasksModule[TasksModule: Tarefas Agendadas]
        %% Outros módulos importantes...

        subgraph "Módulos de Funcionalidade Principal"
            AuthModule
            UsersModule
            CompaniesModule
            ProjectsModule
            TransactionsModule
            RolesModule
            RbacModule
            ReportsModule
        end

        subgraph "Módulos de Suporte"
            PrismaModule
            UtilsModule
            ConfigModule
            TasksModule
        end
    ```

*   **Estrutura de Diretórios Típica (inferida):**
    *   `routes/`: Contém a maioria dos módulos de funcionalidades, cada um provavelmente com seus `controllers`, `services`, e `providers`.
    *   `prisma/`: Contém o `schema.prisma` e o `PrismaModule`.
    *   `guards/`, `decorators/`: Para lógica de autenticação/autorização e metadados.
    *   `main.ts`: Ponto de entrada da aplicação NestJS.

#### 2.1.3. Modelo de Dados (Backend - `prisma/schema.prisma`)

O `schema.prisma` define a estrutura do banco de dados e os modelos de dados da aplicação. As principais entidades e seus relacionamentos incluem:

*   **Usuário e Acesso:** `User`, `Profile`, `RefreshToken`, `UserCompanyRole`, `Role`, `Permission`, `SystemPermission`.
*   **Tenant:** `Company` (entidade central do tenant).
*   **Dados Financeiros e Operacionais (por `Company`):**
    *   `AccountsPayable`, `AccountsReceivable`
    *   `Transaction`
    *   `BankAccount` (associada a `Bank` global)
    *   `Category`
    *   `Project`
    *   `Entity` (Clientes/Fornecedores)
    *   `RecurringSchedule` (associada a `RecurrenceType` global)
*   **Dados de Suporte (alguns globais, outros por `Company`):**
    *   `Address`, `AddressType` (global)
    *   `Bank` (global)
    *   `PaymentMethod` (global)
    *   `RecurrenceType` (global)
    *   `CustomPeriod` (por `Company`)

Um diagrama ER simplificado das principais entidades e seus relacionamentos:

```mermaid
erDiagram
    USER ||--o{ PROFILE : "tem"
    USER ||--o{ USER_COMPANY_ROLE : "participa em"

    USER_COMPANY_ROLE }o--|| COMPANY : "empresa"
    USER_COMPANY_ROLE }o--|| ROLE : "papel"

    COMPANY ||--o{ PROJECT : "gerencia"
    COMPANY ||--o{ TRANSACTION : "realiza"
    COMPANY ||--o{ ENTITY : "gerencia"
    COMPANY ||--o{ BANK_ACCOUNT : "possui"
    COMPANY ||--o{ ACCOUNTS_PAYABLE : "registra"
    COMPANY ||--o{ ACCOUNTS_RECEIVABLE : "registra"

    ACCOUNTS_PAYABLE ||--|| ENTITY : "para"
    ACCOUNTS_RECEIVABLE ||--|| ENTITY : "de"
    TRANSACTION }o--|| CATEGORY : "pertence a"
    TRANSACTION }o--|| BANK_ACCOUNT : "conta"
    PROJECT ||--o{ TRANSACTION : "tem transações"
    BANK_ACCOUNT }o--|| BANK : "pertence ao (global)"

    ROLE ||--o{ ROLE_PERMISSION : "tem permissões"
    ROLE_PERMISSION }o--|| PERMISSION : "permissão"
    PERMISSION }o--|| SYSTEM_PERMISSION : "baseada em (global)"
*   (Potencialmente) **Integração Bancária:** Mencionado como uma feature, mas o nível de implementação precisa ser verificado.
*   (Potencialmente) **Analytics e Relatórios:** Essencial para um sistema financeiro, com um `ReportsModule` já existente no backend, mas a profundidade atual precisa ser avaliada.

### 3.3. Público Alvo

*   Pequenas e Médias Empresas (PMEs) no Brasil (devido à formatação de moeda e idioma pt-BR mencionados nas memórias).
*   Empresas que necessitam de uma ferramenta centralizada para gerenciar suas finanças, projetos e entidades relacionadas, com controle de acesso para diferentes usuários dentro da empresa.

### 3.4. Proposta de Valor

*   **Centralização:** Unificar a gestão financeira, de projetos e de relacionamento com entidades em uma única plataforma.
*   **Eficiência:** Automatizar e simplificar processos financeiros, incluindo transações recorrentes.
*   **Visibilidade e Controle:** Fornecer insights sobre a saúde financeira da empresa através de dados organizados e (potencialmente) relatórios, com controle de acesso granular.
*   **Segurança e Isolamento:** Garantir que os dados de cada empresa (tenant) sejam seguros e isolados, com permissões específicas por usuário.

### 3.5. Roadmap Potencial e Oportunidades

Com base na análise inicial, algumas direções futuras e oportunidades incluem:
*   **Aprofundar Analytics e Relatórios:** Criação de dashboards customizáveis, relatórios financeiros padrão (DRE, Fluxo de Caixa Projetado), e exportação de dados. Expandir as capacidades do `ReportsModule`.
*   **Integrações Estratégicas:**
    *   Gateways de pagamento para facilitar o recebimento de faturas.
    *   Sistemas de contabilidade para exportação de dados fiscais.
    *   Ferramentas de CRM.
*   **Funcionalidades Avançadas de Faturamento:** Emissão de boletos, notas fiscais (requer análise de complexidade e APIs).
*   **Aplicativo Móvel:** Para acesso rápido a informações chave e aprovações.
*   **Automações:** Lembretes de pagamento/recebimento, conciliação bancária automática (se a integração bancária for robusta).
*   **Customização por Tenant:** Níveis maiores de personalização de campos, relatórios e workflows.
*   **Notificações:** Sistema de notificações para eventos importantes (vencimentos, pagamentos, etc.).

---
*Este documento é um esboço inicial e será enriquecido com mais detalhes e diagramas conforme a exploração do código avança.*
