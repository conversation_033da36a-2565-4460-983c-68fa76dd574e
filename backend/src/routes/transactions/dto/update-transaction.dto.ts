import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsDateString, IsOptional } from 'class-validator';

export class UpdateTransactionDto {
  @ApiPropertyOptional({ 
    description: 'Transaction description', 
    example: 'Updated payment for invoice #123' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Transaction date', 
    example: '2025-12-31T14:30:00Z' 
  })
  @IsOptional()
  @IsDateString()
  transactionDate?: string;

  @ApiPropertyOptional({ 
    description: 'Additional notes', 
    example: 'Payment made via bank transfer' 
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
