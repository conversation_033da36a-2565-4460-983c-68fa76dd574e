import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsDateString, IsString } from 'class-validator';

export class TransactionsSummaryDto {
  @ApiPropertyOptional({ 
    description: 'Filter by start date (inclusive)', 
    example: '2025-01-01T00:00:00Z' 
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by end date (inclusive)', 
    example: '2025-12-31T23:59:59Z' 
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by bank account ID (optional UUID)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsString()
  bankAccountId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by company ID (optional UUID)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsString()
  companyId?: string;
}