import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsUUID, 
  IsNumber, 
  IsDateString, 
  IsOptional, 
  IsEnum, 
  ValidateIf,
  IsPositive
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TransactionType {
  INCOME = 'income',
  EXPENSE = 'expense',
  TRANSFER = 'transfer',
}

export class CreateTransactionDto {
  @ApiPropertyOptional({
    description: 'Company ID (empresa alvo para multi-tenant, opcional - se não enviado, será usada a empresa do usuário autenticado)',
    example: 'bc61a6ae-1a4a-40d9-95e7-3f9e5d47812b'
  })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ 
    description: 'Transaction type', 
    enum: TransactionType,
    example: TransactionType.INCOME 
  })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({ 
    description: 'Transaction description', 
    example: 'Payment for invoice #123' 
  })
  @IsString()
  description: string;

  @ApiProperty({ 
    description: 'Transaction amount', 
    example: 1500.50 
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Type(() => Number)
  amount: number;

  @ApiProperty({ 
    description: 'Transaction date', 
    example: '2025-12-31T14:30:00Z' 
  })
  @IsDateString()
  transactionDate: string;

  @ApiProperty({ 
    description: 'Bank account ID (source account for transfers)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsUUID()
  bankAccountId: string;

  @ApiPropertyOptional({ 
    description: 'Destination bank account ID (required for transfers)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ValidateIf(o => o.type === TransactionType.TRANSFER)
  @IsUUID()
  destinationAccountId?: string;

  @ApiPropertyOptional({ 
    description: 'Accounts payable ID (required for expense transactions)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @ValidateIf(o => o.type === TransactionType.EXPENSE && o.accountsPayableId)
  @IsUUID()
  accountsPayableId?: string;

  @ApiPropertyOptional({
    description: 'Accounts receivable ID (optional for income transactions)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ValidateIf(o => o.type === TransactionType.INCOME && o.accountsReceivableId)
  @IsUUID()
  accountsReceivableId?: string;

  @ApiPropertyOptional({ 
    description: 'Category ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({ 
    description: 'Entity ID (customer or supplier)', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;

  @ApiPropertyOptional({ 
    description: 'Project ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  projectId?: string;

  @ApiPropertyOptional({ 
    description: 'Payment method ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @ApiPropertyOptional({ 
    description: 'Additional notes', 
    example: 'Payment made via bank transfer' 
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
