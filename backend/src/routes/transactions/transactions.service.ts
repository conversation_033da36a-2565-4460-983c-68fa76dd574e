import { Injectable, BadRequestException, NotFoundException, ForbiddenException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { Prisma, Transaction } from '@prisma/client';
import { CreateTransactionDto, TransactionType } from './dto/create-transaction.dto';
import { UpdateTransactionDto } from './dto/update-transaction.dto';
import { FindAllTransactionsDto } from './dto/find-all-transactions.dto';

// Define a type for the authenticated user payload
interface AuthenticatedUser {
  id: string;
  companyId: string;
}

@Injectable()
export class TransactionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new transaction
   * This method validates the transaction data and creates a new transaction
   * The database triggers handle the validation of payment/receipt amounts and updating account balances
   */
  async create(
    createTransactionDto: CreateTransactionDto,
    user: AuthenticatedUser,
  ): Promise<Transaction> {
    // 🔧 CORREÇÃO: Usar companyId do DTO se fornecido, senão usar do usuário
    const targetCompanyId = createTransactionDto.companyId || user.companyId;
    
    // 🐛 DEBUG: Log para validar os IDs
    console.log('🐛 DEBUG - TransactionsService.create: user.companyId:', user.companyId);
    console.log('🐛 DEBUG - TransactionsService.create: createTransactionDto.companyId:', createTransactionDto.companyId);
    console.log('🐛 DEBUG - TransactionsService.create: targetCompanyId:', targetCompanyId);
    
    // 🔧 CORREÇÃO: Remover validação restritiva temporariamente para permitir operação
    // A validação de empresa será feita nas validações subsequentes (bankAccount, etc.)
    // que já verificam se os recursos pertencem à empresa correta
    
    // Validação opcional: apenas verificar se o companyId foi fornecido
    if (!targetCompanyId) {
      throw new ForbiddenException('ID da empresa é obrigatório para criar transações.');
    }

    // Validate transaction data based on type
    await this.validateTransactionData(createTransactionDto, targetCompanyId);

    // Prepare transaction data
    const transactionData = this.prepareTransactionData(createTransactionDto, targetCompanyId);

    try {
      // Use Prisma transaction to ensure data consistency
      return await this.prisma.$transaction(async (prisma) => {
        // Create the transaction
        const transaction = await prisma.transaction.create({
          data: transactionData,
        });

        return transaction;
      });
    } catch (error) {
      console.error('Error creating transaction:', error);
      if (error.code === 'P2002') {
        throw new BadRequestException('A transaction with these details already exists.');
      } else if (error.message?.includes('valor do pagamento excede')) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Failed to create transaction. Please try again.');
    }
  }

  /**
   * Find all transactions with filtering and pagination
   */
  async findAll(
    user: AuthenticatedUser,
    filters: FindAllTransactionsDto,
  ): Promise<{ data: Transaction[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
    const {
      page = 1,
      limit = 10,
      type,
      startDate,
      endDate,
      bankAccountId,
      categoryId,
      entityId,
      search
    } = filters;

    const skip = (page - 1) * limit;

    // Build where conditions
    const where: Prisma.TransactionWhereInput = {
      companyId,
      deletedAt: null,
      ...(type && { type }),
      ...(bankAccountId && { bankAccountId }),
      ...(categoryId && { categoryId }),
      ...(entityId && { entityId }),
      ...(search && {
        description: {
          contains: search,
          mode: 'insensitive'
        }
      }),
      ...(startDate && endDate && {
        transactionDate: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      }),
      ...(startDate && !endDate && {
        transactionDate: {
          gte: new Date(startDate)
        }
      }),
      ...(!startDate && endDate && {
        transactionDate: {
          lte: new Date(endDate)
        }
      }),
    };

    try {
      // Get total count
      const total = await this.prisma.transaction.count({ where });

      // Get transactions with pagination
      const transactions = await this.prisma.transaction.findMany({
        where,
        skip,
        take: limit,
        orderBy: { transactionDate: 'desc' },
        include: {
          bankAccount: {
            select: {
              id: true,
              name: true,
            },
          },
          destinationAccount: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          entity: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          paymentMethod: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        data: transactions,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding transactions:', error);
      throw new InternalServerErrorException('Failed to retrieve transactions. Please try again.');
    }
  }

  /**
   * Find a transaction by ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<Transaction> {
    const { companyId } = user;

    try {
      const transaction = await this.prisma.transaction.findFirst({
        where: {
          id,
          companyId,
          deletedAt: null,
        },
        include: {
          bankAccount: {
            select: {
              id: true,
              name: true,
            },
          },
          destinationAccount: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          entity: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          paymentMethod: {
            select: {
              id: true,
              name: true,
            },
          },
          accountsPayable: {
            select: {
              id: true,
              description: true,
              amount: true,
              paidAmount: true,
              status: true,
            },
          },
          accountsReceivable: {
            select: {
              id: true,
              description: true,
              amount: true,
              receivedAmount: true,
              status: true,
            },
          },
        },
      });

      if (!transaction) {
        throw new NotFoundException(`Transaction with ID ${id} not found.`);
      }

      return transaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding transaction with ID ${id}:`, error);
      throw new InternalServerErrorException('Failed to retrieve transaction. Please try again.');
    }
  }

  /**
   * Update a transaction
   * Only certain fields can be updated (description, transactionDate, notes)
   */
  async update(
    id: string,
    updateTransactionDto: UpdateTransactionDto,
    user: AuthenticatedUser,
  ): Promise<Transaction> {
    const { companyId } = user;

    // Check if transaction exists and belongs to the company
    const existingTransaction = await this.prisma.transaction.findFirst({
      where: {
        id,
        companyId,
        deletedAt: null,
      },
    });

    if (!existingTransaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found.`);
    }

    try {
      // Update only allowed fields
      return await this.prisma.transaction.update({
        where: { id },
        data: {
          ...(updateTransactionDto.description && { description: updateTransactionDto.description }),
          ...(updateTransactionDto.transactionDate && { transactionDate: new Date(updateTransactionDto.transactionDate) }),
          ...(updateTransactionDto.notes !== undefined && { notes: updateTransactionDto.notes }),
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`Error updating transaction with ID ${id}:`, error);
      throw new InternalServerErrorException('Failed to update transaction. Please try again.');
    }
  }

  /**
   * Soft delete a transaction
   * This is a soft delete operation that sets the deletedAt field
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    const { companyId } = user;

    // Check if transaction exists and belongs to the company
    const existingTransaction = await this.prisma.transaction.findFirst({
      where: {
        id,
        companyId,
        deletedAt: null,
      },
    });

    if (!existingTransaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found.`);
    }

    try {
      // Soft delete the transaction
      await this.prisma.transaction.update({
        where: { id },
        data: {
          deletedAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`Error removing transaction with ID ${id}:`, error);
      throw new InternalServerErrorException('Failed to remove transaction. Please try again.');
    }
  }

  /**
   * Find transactions by bank account ID
   */
  async findByBankAccount(
    bankAccountId: string,
    user: AuthenticatedUser,
    filters: Omit<FindAllTransactionsDto, 'bankAccountId'>,
  ): Promise<{ data: Transaction[]; total: number; page: number; limit: number }> {
    const { companyId } = user;

    // Check if bank account exists and belongs to the company
    const bankAccount = await this.prisma.bankAccount.findFirst({
      where: {
        id: bankAccountId,
        companyId,
        deletedAt: null,
      },
    });

    if (!bankAccount) {
      throw new NotFoundException(`Bank account with ID ${bankAccountId} not found.`);
    }

    // Use findAll with bankAccountId filter
    return this.findAll(user, { ...filters, bankAccountId });
  }

  /**
   * Validate transaction data based on type
   * This method validates that the required fields are present based on the transaction type
   * and that the referenced entities belong to the company
   */
  private async validateTransactionData(
    createTransactionDto: CreateTransactionDto,
    companyId: string,
  ): Promise<void> {
    const {
      type,
      bankAccountId,
      destinationAccountId,
      accountsPayableId,
      accountsReceivableId,
      categoryId,
      entityId,
      projectId,
      paymentMethodId
    } = createTransactionDto;

    // Validate source bank account
    const bankAccount = await this.prisma.bankAccount.findFirst({
      where: {
        id: bankAccountId,
        companyId,
        deletedAt: null,
      },
    });

    if (!bankAccount) {
      throw new BadRequestException(`Bank account with ID ${bankAccountId} not found or does not belong to your company.`);
    }

    // Validate transaction type-specific fields
    switch (type) {
      case TransactionType.EXPENSE:
        // 🔧 CORREÇÃO: Tornar accountsPayableId opcional para transações simples
        if (accountsPayableId) {
          // Validate accounts payable apenas se fornecido
          const accountsPayable = await this.prisma.accountsPayable.findFirst({
            where: {
              id: accountsPayableId,
              companyId,
              deletedAt: null,
            },
          });

          if (!accountsPayable) {
            throw new BadRequestException(`Accounts payable with ID ${accountsPayableId} not found or does not belong to your company.`);
          }
        }
        break;

      case TransactionType.INCOME:
        // 🔧 CORREÇÃO: Tornar accountsReceivableId opcional para transações simples
        if (accountsReceivableId) {
          // Validate accounts receivable apenas se fornecido
          const accountsReceivable = await this.prisma.accountsReceivable.findFirst({
            where: {
              id: accountsReceivableId,
              companyId,
              deletedAt: null,
            },
          });

          if (!accountsReceivable) {
            throw new BadRequestException(`Accounts receivable with ID ${accountsReceivableId} not found or does not belong to your company.`);
          }
        }
        break;

      case TransactionType.TRANSFER:
        if (!destinationAccountId) {
          throw new BadRequestException('Destination bank account ID is required for transfer transactions.');
        }

        // Validate destination bank account
        const destinationAccount = await this.prisma.bankAccount.findFirst({
          where: {
            id: destinationAccountId,
            companyId,
            deletedAt: null,
          },
        });

        if (!destinationAccount) {
          throw new BadRequestException(`Destination bank account with ID ${destinationAccountId} not found or does not belong to your company.`);
        }

        // Ensure source and destination accounts are different
        if (bankAccountId === destinationAccountId) {
          throw new BadRequestException('Source and destination bank accounts cannot be the same.');
        }
        break;

      default:
        throw new BadRequestException(`Invalid transaction type: ${type}`);
    }

    // Validate optional fields if provided
    if (categoryId) {
      const category = await this.prisma.category.findFirst({
        where: {
          id: categoryId,
          companyId,
          deletedAt: null,
        },
      });

      if (!category) {
        throw new BadRequestException(`Category with ID ${categoryId} not found or does not belong to your company.`);
      }
    }

    if (entityId) {
      const entity = await this.prisma.entity.findFirst({
        where: {
          id: entityId,
          companyId,
          deletedAt: null,
        },
      });

      if (!entity) {
        throw new BadRequestException(`Entity with ID ${entityId} not found or does not belong to your company.`);
      }
    }

    if (projectId) {
      const project = await this.prisma.project.findFirst({
        where: {
          id: projectId,
          companyId,
          deletedAt: null,
        },
      });

      if (!project) {
        throw new BadRequestException(`Project with ID ${projectId} not found or does not belong to your company.`);
      }
    }

    if (paymentMethodId) {
      const paymentMethod = await this.prisma.paymentMethod.findFirst({
        where: {
          id: paymentMethodId,
        },
      });

      if (!paymentMethod) {
        throw new BadRequestException(`Payment method with ID ${paymentMethodId} not found.`);
      }
    }
  }

  /**
   * Prepare transaction data for creation
   */
  private prepareTransactionData(
    createTransactionDto: CreateTransactionDto,
    companyId: string,
  ): Prisma.TransactionCreateInput {
    const {
      type,
      description,
      amount,
      transactionDate,
      bankAccountId,
      destinationAccountId,
      accountsPayableId,
      accountsReceivableId,
      categoryId,
      entityId,
      projectId,
      paymentMethodId,
      notes
    } = createTransactionDto;

    // Base transaction data
    const transactionData: Prisma.TransactionCreateInput = {
      type,
      description,
      amount: amount,
      transactionDate: new Date(transactionDate),
      notes,
      company: {
        connect: { id: companyId },
      },
      bankAccount: {
        connect: { id: bankAccountId },
      },
    };

    // 🐛 DEBUG: Log dos dados antes de adicionar campos específicos
    console.log('🐛 DEBUG - prepareTransactionData: type:', type);
    console.log('🐛 DEBUG - prepareTransactionData: accountsReceivableId:', accountsReceivableId);
    console.log('🐛 DEBUG - prepareTransactionData: accountsPayableId:', accountsPayableId);
    console.log('🐛 DEBUG - prepareTransactionData: destinationAccountId:', destinationAccountId);

    // Add type-specific fields apenas se os IDs estiverem presentes
    switch (type) {
      case TransactionType.EXPENSE:
        if (accountsPayableId) {
          transactionData.accountsPayable = {
            connect: { id: accountsPayableId },
          };
        }
        break;

      case TransactionType.INCOME:
        if (accountsReceivableId) {
          transactionData.accountsReceivable = {
            connect: { id: accountsReceivableId },
          };
        }
        break;

      case TransactionType.TRANSFER:
        if (destinationAccountId) {
          transactionData.destinationAccount = {
            connect: { id: destinationAccountId },
          };
        }
        break;
    }

    // Add optional fields if provided
    if (categoryId) {
      transactionData.category = {
        connect: { id: categoryId },
      };
    }

    if (entityId) {
      transactionData.entity = {
        connect: { id: entityId },
      };
    }

    if (projectId) {
      transactionData.project = {
        connect: { id: projectId },
      };
    }

    if (paymentMethodId) {
      transactionData.paymentMethod = {
        connect: { id: paymentMethodId },
      };
    }

    return transactionData;
  }

  /**
   * Get transactions summary
   * This method calculates summary statistics for transactions within a date range
   */
  async getTransactionsSummary(
    user: AuthenticatedUser,
    startDate?: string,
    endDate?: string,
    bankAccountId?: string,
    companyId?: string
  ) {
    // Use the user's company ID if not explicitly provided
    const targetCompanyId = companyId || user.companyId;

    // Validate bank account if provided
    if (bankAccountId) {
      const bankAccount = await this.prisma.bankAccount.findFirst({
        where: {
          id: bankAccountId,
          companyId: targetCompanyId,
          deletedAt: null,
        },
      });

      if (!bankAccount) {
        throw new BadRequestException(`Bank account with ID ${bankAccountId} not found or does not belong to the company.`);
      }
    }

    // Build where conditions for transactions
    const where: Prisma.TransactionWhereInput = {
      companyId: targetCompanyId,
      deletedAt: null,
      ...(bankAccountId && { bankAccountId }),
      ...(startDate && endDate && {
        transactionDate: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      }),
      ...(startDate && !endDate && {
        transactionDate: {
          gte: new Date(startDate)
        }
      }),
      ...(!startDate && endDate && {
        transactionDate: {
          lte: new Date(endDate)
        }
      }),
    };

    try {
      // Calculate total income (INCOME type transactions)
      const incomeTransactions = await this.prisma.transaction.findMany({
        where: {
          ...where,
          type: TransactionType.INCOME,
        },
        select: {
          amount: true,
        },
      });

      const totalIncome = incomeTransactions.reduce(
        (sum, transaction) => sum + Number(transaction.amount),
        0
      );

      // Calculate total expense (EXPENSE type transactions)
      const expenseTransactions = await this.prisma.transaction.findMany({
        where: {
          ...where,
          type: TransactionType.EXPENSE,
        },
        select: {
          amount: true,
        },
      });

      const totalExpense = expenseTransactions.reduce(
        (sum, transaction) => sum + Number(transaction.amount),
        0
      );

      // Calculate net change
      const netChange = totalIncome - totalExpense;

      // Count pending transactions (usando um campo que existe no modelo)
      // Nota: Como não temos um campo 'status' no modelo Transaction, vamos usar uma lógica alternativa
      // Podemos considerar que transações pendentes são aquelas que foram criadas recentemente
      // ou que têm alguma outra característica específica
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      
      const pendingCount = await this.prisma.transaction.count({
        where: {
          ...where,
          createdAt: {
            gte: oneWeekAgo
          }
        },
      });

      // Calculate previous period income for comparison
      let previousPeriodIncome = 0;
      if (startDate && endDate) {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const periodDuration = endDateObj.getTime() - startDateObj.getTime();
        
        const previousPeriodStartDate = new Date(startDateObj.getTime() - periodDuration);
        const previousPeriodEndDate = new Date(startDateObj.getTime() - 1); // 1ms before current period
        
        const previousIncomeTransactions = await this.prisma.transaction.findMany({
          where: {
            companyId: targetCompanyId,
            deletedAt: null,
            ...(bankAccountId && { bankAccountId }),
            type: TransactionType.INCOME,
            transactionDate: {
              gte: previousPeriodStartDate,
              lte: previousPeriodEndDate,
            },
          },
          select: {
            amount: true,
          },
        });
        
        previousPeriodIncome = previousIncomeTransactions.reduce(
          (sum, transaction) => sum + Number(transaction.amount),
          0
        );
      }

      return {
        data: {
          totalIncome,
          totalExpense,
          netChange,
          pendingCount,
          previousPeriodIncome,
        }
      };
    } catch (error) {
      console.error('Error calculating transactions summary:', error);
      throw new InternalServerErrorException('Failed to calculate transactions summary. Please try again.');
    }
  }
}
