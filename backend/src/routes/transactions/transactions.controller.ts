import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
  ParseUUIDPipe,
  Req
} from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { CreateTransactionDto, UpdateTransactionDto, FindAllTransactionsDto, TransactionsSummaryDto, TransactionType } from './dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiQuery, ApiParam } from '@nestjs/swagger';
import { Transaction } from '@prisma/client';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';

@ApiTags('transactions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new transaction',
    description: `Creates a new financial transaction. Transactions can be of three types:
    - INCOME: Records money received from an accounts receivable
    - EXPENSE: Records money paid for an accounts payable
    - TRANSFER: Records money transferred between bank accounts

    Each type requires different fields to be provided.`
  })
  @ApiBody({
    type: CreateTransactionDto,
    examples: {
      income: {
        summary: 'Income transaction',
        description: 'Example of creating an income transaction from an accounts receivable',
        value: {
          type: TransactionType.INCOME,
          description: 'Client payment for invoice #123',
          amount: 1500.50,
          transactionDate: '2025-12-31T14:30:00Z',
          bankAccountId: '123e4567-e89b-12d3-a456-************',
          accountsReceivableId: '123e4567-e89b-12d3-a456-************',
          categoryId: '123e4567-e89b-12d3-a456-************',
          paymentMethodId: '123e4567-e89b-12d3-a456-************',
          notes: 'Payment received via bank transfer'
        }
      },
      expense: {
        summary: 'Expense transaction',
        description: 'Example of creating an expense transaction for an accounts payable',
        value: {
          type: TransactionType.EXPENSE,
          description: 'Payment for office rent',
          amount: 2500.00,
          transactionDate: '2025-12-31T14:30:00Z',
          bankAccountId: '123e4567-e89b-12d3-a456-************',
          accountsPayableId: '123e4567-e89b-12d3-a456-************',
          categoryId: '123e4567-e89b-12d3-a456-************',
          paymentMethodId: '123e4567-e89b-12d3-a456-************',
          notes: 'Monthly rent payment'
        }
      },
      transfer: {
        summary: 'Transfer transaction',
        description: 'Example of creating a transfer between bank accounts',
        value: {
          type: TransactionType.TRANSFER,
          description: 'Transfer from checking to savings account',
          amount: 1000.00,
          transactionDate: '2025-12-31T14:30:00Z',
          bankAccountId: '123e4567-e89b-12d3-a456-************',
          destinationAccountId: '123e4567-e89b-12d3-a456-************',
          notes: 'Monthly savings transfer'
        }
      }
    }
  })
  @ApiResponse({ status: 201, description: 'Transaction created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data or validation failed.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  create(
    @Body() createTransactionDto: CreateTransactionDto,
    @Req() req: RequestWithUser,
  ): Promise<Transaction> {
    return this.transactionsService.create(createTransactionDto, req.user);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all transactions with filtering and pagination',
    description: 'Retrieves a paginated list of transactions with optional filtering by type, date range, bank account, category, and entity.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'type', required: false, enum: TransactionType, description: 'Filter by transaction type' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Filter by start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Filter by end date (ISO format)' })
  @ApiQuery({ name: 'bankAccountId', required: false, type: String, description: 'Filter by bank account ID' })
  @ApiQuery({ name: 'categoryId', required: false, type: String, description: 'Filter by category ID' })
  @ApiQuery({ name: 'entityId', required: false, type: String, description: 'Filter by entity ID' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term in description' })
  @ApiResponse({ status: 200, description: 'Transactions retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  findAll(
    @Query() filters: FindAllTransactionsDto,
    @Req() req: RequestWithUser,
  ): Promise<{ data: Transaction[]; total: number; page: number; limit: number }> {
    return this.transactionsService.findAll(req.user, filters);
  }

  @Get('by-account/:bankAccountId')
  @ApiOperation({
    summary: 'Get transactions by bank account ID',
    description: 'Retrieves all transactions associated with a specific bank account.'
  })
  @ApiParam({ name: 'bankAccountId', type: String, description: 'Bank Account ID (UUID format)' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'type', required: false, enum: TransactionType, description: 'Filter by transaction type' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Filter by start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Filter by end date (ISO format)' })
  @ApiQuery({ name: 'categoryId', required: false, type: String, description: 'Filter by category ID' })
  @ApiQuery({ name: 'entityId', required: false, type: String, description: 'Filter by entity ID' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term in description' })
  @ApiResponse({ status: 200, description: 'Transactions retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  @ApiResponse({ status: 404, description: 'Bank account not found.' })
  findByBankAccount(
    @Param('bankAccountId', ParseUUIDPipe) bankAccountId: string,
    @Query() filters: Omit<FindAllTransactionsDto, 'bankAccountId'>,
    @Req() req: RequestWithUser,
  ): Promise<{ data: Transaction[]; total: number; page: number; limit: number }> {
    return this.transactionsService.findByBankAccount(bankAccountId, req.user, filters);
  }

  @Get('summary')
  @ApiOperation({
    summary: 'Get transactions summary',
    description: 'Retrieves a summary of transactions including total income, total expense, net change, and pending count.'
  })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Filter by start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'Filter by end date (ISO format)' })
  @ApiQuery({ name: 'bankAccountId', required: false, type: String, description: 'Filter by bank account ID' })
  @ApiQuery({ name: 'companyId', required: false, type: String, description: 'Filter by company ID' })
  @ApiResponse({ status: 200, description: 'Transactions summary retrieved successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid parameters.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  async getTransactionsSummary(
    @Req() req: RequestWithUser,
    @Query() query: TransactionsSummaryDto,
  ) {
    return this.transactionsService.getTransactionsSummary(
      req.user,
      query.startDate,
      query.endDate,
      query.bankAccountId,
      query.companyId
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a transaction by ID',
    description: 'Retrieves detailed information about a specific transaction by its ID.'
  })
  @ApiParam({ name: 'id', type: String, description: 'Transaction ID (UUID format)' })
  @ApiResponse({ status: 200, description: 'Transaction retrieved successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ): Promise<Transaction> {
    return this.transactionsService.findOne(id, req.user);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update a transaction',
    description: 'Updates specific fields of an existing transaction. Only description, transactionDate, and notes can be updated.'
  })
  @ApiParam({ name: 'id', type: String, description: 'Transaction ID (UUID format)' })
  @ApiBody({
    type: UpdateTransactionDto,
    description: 'Fields that can be updated',
    examples: {
      update: {
        value: {
          description: 'Updated transaction description',
          transactionDate: '2025-12-31T14:30:00Z',
          notes: 'Updated notes for this transaction'
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Transaction updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTransactionDto: UpdateTransactionDto,
    @Req() req: RequestWithUser,
  ): Promise<Transaction> {
    return this.transactionsService.update(id, updateTransactionDto, req.user);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a transaction (soft delete)',
    description: 'Performs a soft delete on a transaction by setting the deletedAt field.'
  })
  @ApiParam({ name: 'id', type: String, description: 'Transaction ID (UUID format)' })
  @ApiResponse({ status: 200, description: 'Transaction deleted successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized - User not authenticated.' })
  @ApiResponse({ status: 403, description: 'Forbidden - User lacks necessary permissions.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ): Promise<void> {
    return this.transactionsService.remove(id, req.user);
  }
}
