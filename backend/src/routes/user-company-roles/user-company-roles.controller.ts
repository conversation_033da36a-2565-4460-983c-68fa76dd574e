import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { UserCompanyRolesService } from './user-company-roles.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateUserCompanyRoleDto,
  UserCompanyRoleDto,
  UserCompanyRoleListDto,
  AssignRolesDto,
  RemoveRolesDto,
} from './dto/user-company-role.dto';
import { RoleDto } from '../roles/dto/role.dto';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('user-company-roles')
@Controller('user-company-roles')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UserCompanyRolesController {
  constructor(
    private readonly userCompanyRolesService: UserCompanyRolesService,
  ) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Associar um usuário a uma empresa com um papel' })
  @ApiBody({ type: CreateUserCompanyRoleDto })
  @ApiResponse({
    status: 201,
    description: 'Associação criada com sucesso',
    type: UserCompanyRoleDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({
    status: 404,
    description: 'Usuário, empresa ou papel não encontrado',
  })
  @ApiResponse({ status: 409, description: 'Associação já existe' })
  async create(
    @Body() createUserCompanyRoleDto: CreateUserCompanyRoleDto,
  ): Promise<UserCompanyRoleDto> {
    return this.userCompanyRolesService.create(createUserCompanyRoleDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Listar associações entre usuários, empresas e papéis',
  })
  @ApiQuery({
    name: 'companyId',
    required: false,
    description: 'ID da empresa (opcional)',
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'ID do usuário (opcional)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de associações',
    type: UserCompanyRoleListDto,
  })
  async findAll(
    @Query('companyId') companyId?: string,
    @Query('userId') userId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<UserCompanyRoleListDto> {
    return this.userCompanyRolesService.findAll(companyId, userId, page, limit);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma associação pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da associação' })
  @ApiResponse({
    status: 200,
    description: 'Associação encontrada',
    type: UserCompanyRoleDto,
  })
  @ApiResponse({ status: 404, description: 'Associação não encontrada' })
  async findOne(@Param('id') id: string): Promise<UserCompanyRoleDto> {
    return this.userCompanyRolesService.findOne(id);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma associação' })
  @ApiParam({ name: 'id', description: 'ID da associação' })
  @ApiResponse({ status: 204, description: 'Associação removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Associação não encontrada' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.userCompanyRolesService.remove(id);
  }

  @Get('check/user-company')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Verificar se um usuário está associado a uma empresa',
  })
  @ApiQuery({ name: 'userId', required: true, description: 'ID do usuário' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiResponse({ status: 200, description: 'Resultado da verificação' })
  async checkUserCompanyAssociation(
    @Query('userId') userId: string,
    @Query('companyId') companyId: string,
  ): Promise<{ associated: boolean }> {
    const associated =
      await this.userCompanyRolesService.isUserAssociatedWithCompany(
        userId,
        companyId,
      );
    return { associated };
  }
}

@ApiTags('user-roles')
@Controller('companies/:companyId/users/:userId/roles')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UserRolesController {
  constructor(
    private readonly userCompanyRolesService: UserCompanyRolesService,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar papéis de um usuário em uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'userId', description: 'ID do usuário' })
  @ApiResponse({
    status: 200,
    description: 'Lista de papéis do usuário',
    type: [RoleDto],
  })
  @ApiResponse({ status: 404, description: 'Usuário ou empresa não encontrado' })
  async getUserRoles(
    @Param('companyId') companyId: string,
    @Param('userId') userId: string,
  ): Promise<RoleDto[]> {
    return this.userCompanyRolesService.getUserRoles(companyId, userId);
  }

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atribuir papéis a um usuário em uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'userId', description: 'ID do usuário' })
  @ApiBody({ type: AssignRolesDto })
  @ApiResponse({
    status: 201,
    description: 'Papéis atribuídos com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Usuário, empresa ou papéis não encontrados' })
  @HttpCode(HttpStatus.CREATED)
  async assignRoles(
    @Param('companyId') companyId: string,
    @Param('userId') userId: string,
    @Body() assignRolesDto: AssignRolesDto,
  ): Promise<{ message: string }> {
    await this.userCompanyRolesService.assignRoles(
      companyId,
      userId,
      assignRolesDto,
    );
    return { message: 'Papéis atribuídos com sucesso' };
  }

  @Delete()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover papéis de um usuário em uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'userId', description: 'ID do usuário' })
  @ApiBody({ type: RemoveRolesDto })
  @ApiResponse({
    status: 204,
    description: 'Papéis removidos com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Usuário ou empresa não encontrado' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeRoles(
    @Param('companyId') companyId: string,
    @Param('userId') userId: string,
    @Body() removeRolesDto: RemoveRolesDto,
  ): Promise<void> {
    await this.userCompanyRolesService.removeRoles(
      companyId,
      userId,
      removeRolesDto,
    );
  }
}
