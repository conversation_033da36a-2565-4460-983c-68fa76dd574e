import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import {
  CashFlowQueryDto,
  CashFlowResponseDto,
  CashFlowItemDto,
  BalanceSheetQueryDto,
  BalanceSheetResponseDto,
  BalanceSheetItemDto,
  IncomeStatementQueryDto,
  IncomeStatementResponseDto,
  IncomeStatementCategoryDto
} from './dto';
import { Prisma } from '@prisma/client';

// Interface para usuário autenticado
interface AuthenticatedUser {
  userId: string;
  companyId: string;
}

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gera um relatório de fluxo de caixa para o período especificado
   * @param user Usuário autenticado
   * @param params Parâmetros de consulta (startDate, endDate, currencyId)
   * @returns Relatório de fluxo de caixa
   */
  async getCashFlow(
    user: AuthenticatedUser,
    params: CashFlowQueryDto,
  ): Promise<CashFlowResponseDto> {
    try {
      const { companyId } = user;
      const { startDate, endDate, currencyId } = params;

      // Validar datas
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        throw new BadRequestException('Datas inválidas');
      }

      if (startDateObj > endDateObj) {
        throw new BadRequestException('A data inicial deve ser anterior à data final');
      }

      // Buscar moeda (padrão se não especificada)
      const currency = await this.getCurrency(currencyId);

      // Buscar transações no período
      const transactions = await this.prisma.transaction.findMany({
        where: {
          companyId,
          transactionDate: {
            gte: startDateObj,
            lte: endDateObj,
          },
          deletedAt: null,
          type: {
            in: ['income', 'expense'],
          },
        },
        orderBy: {
          transactionDate: 'asc',
        },
        select: {
          id: true,
          type: true,
          amount: true,
          transactionDate: true,
        },
      });

      // Agrupar transações por dia
      const dailyTransactions = new Map<string, CashFlowItemDto>();

      // Inicializar totais
      let totalIncome = 0;
      let totalExpense = 0;

      // Processar transações
      transactions.forEach((transaction) => {
        const dateKey = transaction.transactionDate.toISOString().split('T')[0];

        if (!dailyTransactions.has(dateKey)) {
          dailyTransactions.set(dateKey, {
            date: new Date(dateKey),
            income: 0,
            expense: 0,
            balance: 0,
          });
        }

        const dailyData = dailyTransactions.get(dateKey);

        if (dailyData) {
          if (transaction.type === 'income') {
            dailyData.income += Number(transaction.amount);
            totalIncome += Number(transaction.amount);
          } else if (transaction.type === 'expense') {
            dailyData.expense += Number(transaction.amount);
            totalExpense += Number(transaction.amount);
          }

          dailyData.balance = dailyData.income - dailyData.expense;
        }
      });

      // Converter para array e ordenar por data
      const items = Array.from(dailyTransactions.values()).sort(
        (a, b) => a.date.getTime() - b.date.getTime(),
      );

      return {
        items,
        totalIncome,
        totalExpense,
        totalBalance: totalIncome - totalExpense,
        startDate: startDateObj,
        endDate: endDateObj,
        currencyCode: currency.code,
        currencySymbol: currency.symbol,
      };
    } catch (error) {
      this.logger.error(`Erro ao gerar relatório de fluxo de caixa: ${error.message}`, error.stack);

      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Erro ao gerar relatório de fluxo de caixa');
    }
  }

  /**
   * Gera um balanço patrimonial para a data especificada
   * @param user Usuário autenticado
   * @param params Parâmetros de consulta (date, currencyId)
   * @returns Balanço patrimonial
   */
  async getBalanceSheet(
    user: AuthenticatedUser,
    params: BalanceSheetQueryDto,
  ): Promise<BalanceSheetResponseDto> {
    try {
      const { companyId } = user;
      const { date, currencyId } = params;

      // Validar data
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        throw new BadRequestException('Data inválida');
      }

      // Buscar moeda (padrão se não especificada)
      const currency = await this.getCurrency(currencyId);

      // 1. Buscar saldos das contas bancárias (ativos)
      const bankAccounts = await this.prisma.bankAccount.findMany({
        where: {
          companyId,
          deletedAt: null,
        },
        select: {
          id: true,
          name: true,
          currentBalance: true,
          accountType: true,
        },
      });

      // 2. Buscar contas a receber em aberto (ativos)
      const accountsReceivable = await this.prisma.accountsReceivable.findMany({
        where: {
          companyId,
          dueDate: {
            lte: dateObj,
          },
          status: {
            in: ['pending', 'partially_received', 'overdue'],
          },
          deletedAt: null,
        },
        select: {
          id: true,
          description: true,
          amount: true,
          receivedAmount: true,
          entity: {
            select: {
              name: true,
            },
          },
        },
      });

      // 3. Buscar contas a pagar em aberto (passivos)
      const accountsPayable = await this.prisma.accountsPayable.findMany({
        where: {
          companyId,
          dueDate: {
            lte: dateObj,
          },
          status: {
            in: ['pending', 'partially_paid', 'overdue'],
          },
          deletedAt: null,
        },
        select: {
          id: true,
          description: true,
          amount: true,
          paidAmount: true,
          entity: {
            select: {
              name: true,
            },
          },
        },
      });

      // Calcular totais
          const totalBankBalance = bankAccounts.reduce(
      (sum, account) => sum + Number(account.currentBalance),
      0,
    );

      const totalReceivable = accountsReceivable.reduce(
        (sum, account) => sum + Number(account.amount) - Number(account.receivedAmount),
        0,
      );

      const totalPayable = accountsPayable.reduce(
        (sum, account) => sum + Number(account.amount) - Number(account.paidAmount),
        0,
      );

      const totalAssets = totalBankBalance + totalReceivable;
      const totalLiabilities = totalPayable;
      const totalEquity = totalAssets - totalLiabilities;

      // Construir estrutura do balanço
      const assets: BalanceSheetItemDto = {
        name: 'Ativos',
        value: totalAssets,
        items: [
          {
            name: 'Caixa e Equivalentes',
            value: totalBankBalance,
            items: bankAccounts.map((account) => ({
                          name: account.name,
            value: Number(account.currentBalance),
            })),
          },
          {
            name: 'Contas a Receber',
            value: totalReceivable,
            items: accountsReceivable.map((account) => ({
              name: `${account.entity.name} - ${account.description}`,
              value: Number(account.amount) - Number(account.receivedAmount),
            })),
          },
        ],
      };

      const liabilities: BalanceSheetItemDto = {
        name: 'Passivos',
        value: totalLiabilities,
        items: [
          {
            name: 'Contas a Pagar',
            value: totalPayable,
            items: accountsPayable.map((account) => ({
              name: `${account.entity.name} - ${account.description}`,
              value: Number(account.amount) - Number(account.paidAmount),
            })),
          },
        ],
      };

      const equity: BalanceSheetItemDto = {
        name: 'Patrimônio Líquido',
        value: totalEquity,
        items: [
          {
            name: 'Capital Acumulado',
            value: totalEquity,
          },
        ],
      };

      return {
        date: dateObj,
        assets,
        liabilities,
        equity,
        totalAssets,
        totalLiabilities,
        totalEquity,
        currencyCode: currency.code,
        currencySymbol: currency.symbol,
      };
    } catch (error) {
      this.logger.error(`Erro ao gerar balanço patrimonial: ${error.message}`, error.stack);

      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Erro ao gerar balanço patrimonial');
    }
  }

  /**
   * Gera uma demonstração de resultados para o período especificado
   * @param user Usuário autenticado
   * @param params Parâmetros de consulta (startDate, endDate, currencyId)
   * @returns Demonstração de resultados
   */
  async getIncomeStatement(
    user: AuthenticatedUser,
    params: IncomeStatementQueryDto,
  ): Promise<IncomeStatementResponseDto> {
    try {
      const { companyId } = user;
      const { startDate, endDate, currencyId } = params;

      // Validar datas
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        throw new BadRequestException('Datas inválidas');
      }

      if (startDateObj > endDateObj) {
        throw new BadRequestException('A data inicial deve ser anterior à data final');
      }

      // Buscar moeda (padrão se não especificada)
      const currency = await this.getCurrency(currencyId);

      // Buscar transações no período agrupadas por categoria
      const transactions = await this.prisma.transaction.findMany({
        where: {
          companyId,
          transactionDate: {
            gte: startDateObj,
            lte: endDateObj,
          },
          deletedAt: null,
          type: {
            in: ['income', 'expense'],
          },
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              parentId: true,
              transactionType: true,
            },
          },
        },
      });

      // Estruturas para armazenar categorias e valores
      const revenueCategories = new Map<string, { id: string; name: string; value: number; parentId: string | null }>();
      const expenseCategories = new Map<string, { id: string; name: string; value: number; parentId: string | null }>();

      // Processar transações
      transactions.forEach((transaction) => {
        if (!transaction.category) return;

        const { id, name, parentId, transactionType } = transaction.category;
        const amount = Number(transaction.amount);

        if (transaction.type === 'income') {
          if (!revenueCategories.has(id)) {
            revenueCategories.set(id, { id, name, value: 0, parentId });
          }
          const category = revenueCategories.get(id);
          if (category) {
            category.value += amount;
          }
        } else if (transaction.type === 'expense') {
          if (!expenseCategories.has(id)) {
            expenseCategories.set(id, { id, name, value: 0, parentId });
          }
          const category = expenseCategories.get(id);
          if (category) {
            category.value += amount;
          }
        }
      });

      // Função para construir a hierarquia de categorias
      const buildCategoryHierarchy = (
        categories: Map<string, { id: string; name: string; value: number; parentId: string | null }>,
      ): IncomeStatementCategoryDto[] => {
        const rootCategories: IncomeStatementCategoryDto[] = [];
        const categoryMap = new Map<string, IncomeStatementCategoryDto>();

        // Primeiro passo: criar objetos para todas as categorias
        categories.forEach((category) => {
          categoryMap.set(category.id, {
            id: category.id,
            name: category.name,
            value: category.value,
            subcategories: [],
          });
        });

        // Segundo passo: construir a hierarquia
        categories.forEach((category) => {
          const categoryDto = categoryMap.get(category.id);

          if (category.parentId && categoryMap.has(category.parentId) && categoryDto) {
            // Esta é uma subcategoria
            const parentCategory = categoryMap.get(category.parentId);
            if (parentCategory && parentCategory.subcategories) {
              parentCategory.subcategories.push(categoryDto);
            }
          } else if (categoryDto) {
            // Esta é uma categoria raiz
            rootCategories.push(categoryDto);
          }
        });

        return rootCategories;
      };

      // Construir hierarquias
      const revenues = buildCategoryHierarchy(revenueCategories);
      const expenses = buildCategoryHierarchy(expenseCategories);

      // Calcular totais
      const totalRevenues = Array.from(revenueCategories.values()).reduce(
        (sum, category) => sum + category.value,
        0,
      );

      const totalExpenses = Array.from(expenseCategories.values()).reduce(
        (sum, category) => sum + category.value,
        0,
      );

      const netIncome = totalRevenues - totalExpenses;

      return {
        startDate: startDateObj,
        endDate: endDateObj,
        revenues,
        expenses,
        totalRevenues,
        totalExpenses,
        netIncome,
        currencyCode: currency.code,
        currencySymbol: currency.symbol,
      };
    } catch (error) {
      this.logger.error(`Erro ao gerar demonstração de resultados: ${error.message}`, error.stack);

      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Erro ao gerar demonstração de resultados');
    }
  }

  /**
   * Busca a moeda pelo ID ou retorna a moeda padrão
   * @param currencyId ID da moeda (opcional)
   * @returns Moeda
   */
  private async getCurrency(currencyId?: string) {
    try {
      let currency;

      if (currencyId) {
        // Buscar moeda específica
        currency = await this.prisma.currency.findUnique({
          where: { id: currencyId },
        });

        if (!currency) {
          throw new NotFoundException('Moeda não encontrada');
        }
      } else {
        // Buscar moeda padrão
        currency = await this.prisma.currency.findFirst({
          where: { isDefault: true },
        });

        if (!currency) {
          // Se não houver moeda padrão, pegar a primeira
          currency = await this.prisma.currency.findFirst();

          if (!currency) {
            throw new NotFoundException('Nenhuma moeda cadastrada no sistema');
          }
        }
      }

      return currency;
    } catch (error) {
      this.logger.error(`Erro ao buscar moeda: ${error.message}`, error.stack);
      throw error;
    }
  }
}
