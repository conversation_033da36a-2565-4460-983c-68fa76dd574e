import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { SystemPermissionsService } from './system-permissions.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateSystemPermissionDto,
  SystemPermissionDto,
  UpdateSystemPermissionDto,
  SystemPermissionListDto,
} from './dto/system-permission.dto';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('system-permissions')
@Controller('system-permissions')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SystemPermissionsController {
  constructor(
    private readonly systemPermissionsService: SystemPermissionsService,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova permissão do sistema (Admin Global)' })
  @ApiBody({ type: CreateSystemPermissionDto })
  @ApiResponse({
    status: 201,
    description: 'Permissão do sistema criada com sucesso',
    type: SystemPermissionDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'Permissão já existe' })
  async create(
    @Body() createSystemPermissionDto: CreateSystemPermissionDto,
  ): Promise<SystemPermissionDto> {
    return this.systemPermissionsService.create(createSystemPermissionDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as permissões do sistema' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiQuery({
    name: 'module',
    required: false,
    description: 'Filtrar por módulo',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de permissões do sistema',
    type: SystemPermissionListDto,
  })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('module') module?: string,
  ): Promise<SystemPermissionListDto> {
    return this.systemPermissionsService.findAll(page, limit, module);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma permissão do sistema pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da permissão do sistema' })
  @ApiResponse({
    status: 200,
    description: 'Permissão do sistema encontrada',
    type: SystemPermissionDto,
  })
  @ApiResponse({ status: 404, description: 'Permissão do sistema não encontrada' })
  async findOne(@Param('id') id: string): Promise<SystemPermissionDto> {
    return this.systemPermissionsService.findOne(id);
  }

  @Get('code/:code')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma permissão do sistema pelo código' })
  @ApiParam({ name: 'code', description: 'Código da permissão do sistema' })
  @ApiResponse({
    status: 200,
    description: 'Permissão do sistema encontrada',
    type: SystemPermissionDto,
  })
  @ApiResponse({ status: 404, description: 'Permissão do sistema não encontrada' })
  async findByCode(@Param('code') code: string): Promise<SystemPermissionDto> {
    return this.systemPermissionsService.findByCode(code);
  }

  @Put(':id')
  @Roles(Role.ADMIN)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma permissão do sistema (Admin Global)' })
  @ApiParam({ name: 'id', description: 'ID da permissão do sistema' })
  @ApiBody({ type: UpdateSystemPermissionDto })
  @ApiResponse({
    status: 200,
    description: 'Permissão do sistema atualizada com sucesso',
    type: SystemPermissionDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Permissão do sistema não encontrada' })
  async update(
    @Param('id') id: string,
    @Body() updateSystemPermissionDto: UpdateSystemPermissionDto,
  ): Promise<SystemPermissionDto> {
    return this.systemPermissionsService.update(id, updateSystemPermissionDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma permissão do sistema (Admin Global)' })
  @ApiParam({ name: 'id', description: 'ID da permissão do sistema' })
  @ApiResponse({ status: 204, description: 'Permissão do sistema removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Permissão do sistema não encontrada' })
  @ApiResponse({ status: 409, description: 'Permissão do sistema está em uso' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.systemPermissionsService.remove(id);
  }

  @Post('setup')
  @Roles(Role.ADMIN)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configurar permissões padrão do sistema (Admin Global)' })
  @ApiResponse({
    status: 201,
    description: 'Permissões padrão do sistema configuradas com sucesso',
  })
  @HttpCode(HttpStatus.CREATED)
  async setup(): Promise<{ message: string }> {
    await this.systemPermissionsService.createDefaultSystemPermissions();
    return { message: 'Permissões padrão do sistema configuradas com sucesso' };
  }
}
