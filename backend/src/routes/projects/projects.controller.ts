// backend/src/routes/projects/projects.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  Put,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  BadRequestException,
} from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto } from './dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('projects')
@Controller('projects')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo projeto' })
  @ApiBody({ type: CreateProjectDto })
  @ApiResponse({
    status: 201,
    description: 'Projeto criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  create(
    @Body() createProjectDto: CreateProjectDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.projectsService.create(createProjectDto, user);
  }

  @Get()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os projetos' })
  @ApiQuery({ name: 'page', required: false, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página' })
  @ApiQuery({ name: 'status', required: false, description: 'Status do projeto (planned, in_progress, completed, on_hold, canceled)' })
  @ApiQuery({ name: 'name', required: false, description: 'Filtrar por nome do projeto' })
  @ApiQuery({ name: 'startDateFrom', required: false, description: 'Data de início a partir de (YYYY-MM-DD)' })
  @ApiQuery({ name: 'startDateTo', required: false, description: 'Data de início até (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDateFrom', required: false, description: 'Data de término a partir de (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDateTo', required: false, description: 'Data de término até (YYYY-MM-DD)' })
  @ApiQuery({ name: 'budgetMin', required: false, description: 'Orçamento mínimo' })
  @ApiQuery({ name: 'budgetMax', required: false, description: 'Orçamento máximo' })
  @ApiResponse({ status: 200, description: 'Lista de projetos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string,
    @Query('name') name?: string,
    @Query('startDateFrom') startDateFrom?: string,
    @Query('startDateTo') startDateTo?: string,
    @Query('endDateFrom') endDateFrom?: string,
    @Query('endDateTo') endDateTo?: string,
    @Query('budgetMin') budgetMin?: string,
    @Query('budgetMax') budgetMax?: string,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    const budgetMinNumber = budgetMin ? parseFloat(budgetMin) : undefined;
    const budgetMaxNumber = budgetMax ? parseFloat(budgetMax) : undefined;

    return this.projectsService.findAll(user, {
      page: pageNumber,
      limit: limitNumber,
      status,
      name,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      budgetMin: budgetMinNumber,
      budgetMax: budgetMaxNumber
    });
  }

  @Get(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um projeto pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do projeto' })
  @ApiResponse({ status: 200, description: 'Projeto encontrado' })
  @ApiResponse({ status: 404, description: 'Projeto não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.projectsService.findOne(id, user);
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um projeto' })
  @ApiParam({ name: 'id', description: 'ID do projeto' })
  @ApiBody({ type: UpdateProjectDto })
  @ApiResponse({ status: 200, description: 'Projeto atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Projeto não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.projectsService.update(id, updateProjectDto, user);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um projeto' })
  @ApiParam({ name: 'id', description: 'ID do projeto' })
  @ApiResponse({ status: 204, description: 'Projeto removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Projeto não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.projectsService.remove(id, user);
  }

  @Get('search/name/:name')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar projetos por nome' })
  @ApiParam({ name: 'name', description: 'Nome do projeto para busca' })
  @ApiQuery({ name: 'page', required: false, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página' })
  @ApiResponse({ status: 200, description: 'Lista de projetos encontrados' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findByName(
    @Param('name') name: string,
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    return this.projectsService.findByName(name, user, { page: pageNumber, limit: limitNumber });
  }

  @Get('search/period')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar projetos por período' })
  @ApiQuery({ name: 'startDateFrom', required: true, description: 'Data de início a partir de (YYYY-MM-DD)' })
  @ApiQuery({ name: 'startDateTo', required: true, description: 'Data de início até (YYYY-MM-DD)' })
  @ApiQuery({ name: 'page', required: false, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página' })
  @ApiResponse({ status: 200, description: 'Lista de projetos encontrados' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos' })
  findByPeriod(
    @Query('startDateFrom') startDateFrom: string,
    @Query('startDateTo') startDateTo: string,
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    if (!startDateFrom || !startDateTo) {
      throw new BadRequestException('startDateFrom e startDateTo são obrigatórios');
    }

    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    return this.projectsService.findByPeriod(startDateFrom, startDateTo, user, { page: pageNumber, limit: limitNumber });
  }
}
