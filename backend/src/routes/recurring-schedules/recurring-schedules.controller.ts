import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  Query,
  ParseUUI<PERSON>ipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { RecurringSchedulesService } from './recurring-schedules.service';
import { CreateRecurringScheduleDto, ReferenceTableEnum } from './dto/create-recurring-schedule.dto';
import { UpdateRecurringScheduleDto } from './dto/update-recurring-schedule.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../../guards/permissions.guard';
import { Permissions } from '../../decorators/permissions.decorator';

@ApiTags('recurring-schedules')
@Controller('recurring-schedules')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class RecurringSchedulesController {
  constructor(private readonly recurringSchedulesService: RecurringSchedulesService) {}

  @Post()
  @Permissions('recurring-schedules.create')
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo agendamento recorrente' })
  @ApiBody({ type: CreateRecurringScheduleDto })
  @ApiResponse({
    status: 201,
    description: 'Agendamento recorrente criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createRecurringScheduleDto: CreateRecurringScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    return this.recurringSchedulesService.create(createRecurringScheduleDto, req.user);
  }

  @Get()
  @Permissions('recurring-schedules.view')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os agendamentos recorrentes' })
  @ApiQuery({ name: 'page', required: false, description: 'Página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página', type: Number })
  @ApiQuery({ name: 'active', required: false, description: 'Filtrar por status ativo', type: Boolean })
  @ApiQuery({
    name: 'referenceTable',
    required: false,
    description: 'Filtrar por tabela de referência',
    enum: ReferenceTableEnum
  })
  @ApiQuery({ name: 'entityId', required: false, description: 'Filtrar por entidade', type: String })
  @ApiResponse({ status: 200, description: 'Lista de agendamentos recorrentes' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('active') active?: string,
    @Query('referenceTable') referenceTable?: ReferenceTableEnum,
    @Query('entityId') entityId?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;
    const activeBoolean = active ? active.toLowerCase() === 'true' : undefined;

    // Validação básica
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite por página inválido');
    }

    return this.recurringSchedulesService.findAll(req.user, {
      page: pageNumber,
      limit: limitNumber,
      active: activeBoolean,
      referenceTable,
      entityId,
    });
  }

  @Get(':id')
  @Permissions('recurring-schedules.view')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um agendamento recorrente pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do agendamento recorrente' })
  @ApiResponse({ status: 200, description: 'Agendamento recorrente encontrado' })
  @ApiResponse({ status: 404, description: 'Agendamento recorrente não encontrado' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.recurringSchedulesService.findOne(id, req.user);
  }

  @Put(':id')
  @Permissions('recurring-schedules.update')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um agendamento recorrente' })
  @ApiParam({ name: 'id', description: 'ID do agendamento recorrente' })
  @ApiBody({ type: UpdateRecurringScheduleDto })
  @ApiResponse({ status: 200, description: 'Agendamento recorrente atualizado com sucesso' })
  @ApiResponse({ status: 404, description: 'Agendamento recorrente não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRecurringScheduleDto: UpdateRecurringScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    return this.recurringSchedulesService.update(id, updateRecurringScheduleDto, req.user);
  }

  @Delete(':id')
  @Permissions('recurring-schedules.delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um agendamento recorrente' })
  @ApiParam({ name: 'id', description: 'ID do agendamento recorrente' })
  @ApiResponse({ status: 204, description: 'Agendamento recorrente removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Agendamento recorrente não encontrado' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.recurringSchedulesService.remove(id, req.user);
  }
}
