import { Module } from '@nestjs/common';
import { AddressesController } from '../controllers/addresses.controller';
import { AddressesService } from '../services/addresses.service';
import { PrismaService } from '../services/prisma.service';

@Module({
  controllers: [AddressesController],
  providers: [AddressesService, PrismaService],
  exports: [AddressesService],
})
export class AddressesModule {}
