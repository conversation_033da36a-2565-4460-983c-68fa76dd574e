import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDateString, MaxLength, IsUUID, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCustomPeriodDto {
  @ApiProperty({
    description: 'Nome do período personalizado',
    example: 'Primeiro Trimestre 2025',
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Data de início do período (formato YYYY-MM-DD)',
    example: '2025-01-01',
  })
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({
    description: 'Data de fim do período (formato YYYY-MM-DD)',
    example: '2025-03-31',
  })
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @ApiProperty({
    description: 'ID da empresa (opcional, será inferido do usuário autenticado)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsUUID('4')
  @IsOptional()
  companyId?: string;
}
