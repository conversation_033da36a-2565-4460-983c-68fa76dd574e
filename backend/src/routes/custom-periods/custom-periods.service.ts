import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CustomPeriod, Prisma } from '@prisma/client';
import { CreateCustomPeriodDto, UpdateCustomPeriodDto, CustomPeriodListDto } from './dto';
import { AuthenticatedUser } from '../../interfaces/authenticated-user.interface';

@Injectable()
export class CustomPeriodsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Criar um novo período personalizado
   */
  async create(
    createDto: CreateCustomPeriodDto,
    user: AuthenticatedUser,
  ): Promise<CustomPeriod> {
    const { companyId } = user;

    // Validar datas
    const startDate = new Date(createDto.startDate);
    const endDate = new Date(createDto.endDate);

    if (endDate < startDate) {
      throw new BadRequestException('A data de fim deve ser posterior à data de início');
    }

    try {
      // Criar o período personalizado
      return await this.prisma.customPeriod.create({
        data: {
          name: createDto.name,
          startDate,
          endDate,
          company: { connect: { id: companyId } },
        },
      });
    } catch (error) {
      console.error('Erro ao criar período personalizado:', error);
      throw new InternalServerErrorException('Não foi possível criar o período personalizado');
    }
  }

  /**
   * Buscar todos os períodos personalizados com filtros e paginação
   */
  async findAll(
    user: AuthenticatedUser,
    {
      page = 1,
      limit = 10,
      search,
    }: {
      page?: number;
      limit?: number;
      search?: string;
    } = {},
  ): Promise<CustomPeriodListDto> {
    const { companyId } = user;
    const skip = (page - 1) * limit;

    // Construir filtros
    const where: Prisma.CustomPeriodWhereInput = {
      companyId,
    };

    // Adicionar filtro de busca por nome se fornecido
    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive',
      };
    }

    try {
      // Contar total de registros
      const total = await this.prisma.customPeriod.count({ where });

      // Buscar períodos paginados
      const customPeriods = await this.prisma.customPeriod.findMany({
        where,
        skip,
        take: limit,
        orderBy: { startDate: 'desc' },
      });

      return {
        items: customPeriods,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Erro ao buscar períodos personalizados:', error);
      throw new InternalServerErrorException('Não foi possível buscar os períodos personalizados');
    }
  }

  /**
   * Buscar um período personalizado pelo ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<CustomPeriod> {
    const { companyId } = user;

    try {
      const customPeriod = await this.prisma.customPeriod.findFirst({
        where: { id, companyId },
      });

      if (!customPeriod) {
        throw new NotFoundException(`Período personalizado com ID ${id} não encontrado`);
      }

      return customPeriod;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Erro ao buscar período personalizado com ID ${id}:`, error);
      throw new InternalServerErrorException('Não foi possível buscar o período personalizado');
    }
  }

  /**
   * Atualizar um período personalizado
   */
  async update(
    id: string,
    updateDto: UpdateCustomPeriodDto,
    user: AuthenticatedUser,
  ): Promise<CustomPeriod> {
    // Verificar se o período existe e pertence à empresa do usuário
    await this.findOne(id, user);

    // Validar datas se ambas forem fornecidas
    if (updateDto.startDate && updateDto.endDate) {
      const startDate = new Date(updateDto.startDate);
      const endDate = new Date(updateDto.endDate);

      if (endDate < startDate) {
        throw new BadRequestException('A data de fim deve ser posterior à data de início');
      }
    } else if (updateDto.startDate) {
      // Se apenas a data de início for fornecida, verificar com a data de fim existente
      const existingPeriod = await this.prisma.customPeriod.findUnique({
        where: { id },
        select: { endDate: true },
      });

      const startDate = new Date(updateDto.startDate);
      if (existingPeriod && existingPeriod.endDate < startDate) {
        throw new BadRequestException('A data de início não pode ser posterior à data de fim existente');
      }
    } else if (updateDto.endDate) {
      // Se apenas a data de fim for fornecida, verificar com a data de início existente
      const existingPeriod = await this.prisma.customPeriod.findUnique({
        where: { id },
        select: { startDate: true },
      });

      const endDate = new Date(updateDto.endDate);
      if (existingPeriod && endDate < existingPeriod.startDate) {
        throw new BadRequestException('A data de fim não pode ser anterior à data de início existente');
      }
    }

    try {
      // Preparar dados para atualização
      const data: Prisma.CustomPeriodUpdateInput = {};

      if (updateDto.name) data.name = updateDto.name;
      if (updateDto.startDate) data.startDate = new Date(updateDto.startDate);
      if (updateDto.endDate) data.endDate = new Date(updateDto.endDate);

      // Atualizar o período personalizado
      return await this.prisma.customPeriod.update({
        where: { id },
        data,
      });
    } catch (error) {
      console.error(`Erro ao atualizar período personalizado com ID ${id}:`, error);
      throw new InternalServerErrorException('Não foi possível atualizar o período personalizado');
    }
  }

  /**
   * Remover um período personalizado
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // Verificar se o período existe e pertence à empresa do usuário
    await this.findOne(id, user);

    try {
      await this.prisma.customPeriod.delete({
        where: { id },
      });
    } catch (error) {
      console.error(`Erro ao remover período personalizado com ID ${id}:`, error);
      throw new InternalServerErrorException('Não foi possível remover o período personalizado');
    }
  }
}
