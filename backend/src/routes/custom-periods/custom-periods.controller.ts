import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  Query,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { CustomPeriodsService } from './custom-periods.service';
import { CreateCustomPeriodDto, UpdateCustomPeriodDto, CustomPeriodDto, CustomPeriodListDto } from './dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('custom-periods')
@Controller('custom-periods')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CustomPeriodsController {
  constructor(private readonly customPeriodsService: CustomPeriodsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo período personalizado' })
  @ApiBody({ type: CreateCustomPeriodDto })
  @ApiResponse({
    status: 201,
    description: 'Período personalizado criado com sucesso',
    type: CustomPeriodDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  create(
    @Body() createCustomPeriodDto: CreateCustomPeriodDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.customPeriodsService.create(createCustomPeriodDto, user);
  }

  @Get()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os períodos personalizados' })
  @ApiQuery({ name: 'page', required: false, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página' })
  @ApiQuery({ name: 'search', required: false, description: 'Busca por nome' })
  @ApiResponse({
    status: 200,
    description: 'Lista de períodos personalizados',
    type: CustomPeriodListDto,
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    return this.customPeriodsService.findAll(user, {
      page: pageNumber,
      limit: limitNumber,
      search,
    });
  }

  @Get(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.FINANCE_ANALYST)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um período personalizado pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do período personalizado' })
  @ApiResponse({
    status: 200,
    description: 'Período personalizado encontrado',
    type: CustomPeriodDto,
  })
  @ApiResponse({ status: 404, description: 'Período personalizado não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.customPeriodsService.findOne(id, user);
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um período personalizado' })
  @ApiParam({ name: 'id', description: 'ID do período personalizado' })
  @ApiBody({ type: UpdateCustomPeriodDto })
  @ApiResponse({
    status: 200,
    description: 'Período personalizado atualizado com sucesso',
    type: CustomPeriodDto,
  })
  @ApiResponse({ status: 404, description: 'Período personalizado não encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCustomPeriodDto: UpdateCustomPeriodDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.customPeriodsService.update(id, updateCustomPeriodDto, user);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um período personalizado' })
  @ApiParam({ name: 'id', description: 'ID do período personalizado' })
  @ApiResponse({ status: 204, description: 'Período personalizado removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Período personalizado não encontrado' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.customPeriodsService.remove(id, user);
  }
}
