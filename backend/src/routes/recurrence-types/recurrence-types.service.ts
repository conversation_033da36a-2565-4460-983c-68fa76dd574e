// backend/src/routes/recurrence-types/recurrence-types.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateRecurrenceTypeDto } from './dto/create-recurrence-type.dto';
import { UpdateRecurrenceTypeDto } from './dto/update-recurrence-type.dto';
import { RecurrenceType } from '@prisma/client';

@Injectable()
export class RecurrenceTypesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new recurrence type
   */
  async create(createRecurrenceTypeDto: CreateRecurrenceTypeDto): Promise<RecurrenceType> {
    try {
      // Check if a recurrence type with the same name already exists
      const existingRecurrenceType = await this.prisma.recurrenceType.findUnique({
        where: { name: createRecurrenceTypeDto.name },
      });

      if (existingRecurrenceType) {
        throw new ConflictException(`Recurrence type with name ${createRecurrenceTypeDto.name} already exists`);
      }

      // Create the recurrence type
      return await this.prisma.recurrenceType.create({
        data: createRecurrenceTypeDto,
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      console.error('Error creating recurrence type:', error);
      throw new InternalServerErrorException('Could not create recurrence type');
    }
  }

  /**
   * Find all recurrence types with pagination
   */
  async findAll(
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: RecurrenceType[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    try {
      // Get total count
      const total = await this.prisma.recurrenceType.count();

      // Get recurrence types with pagination
      const recurrenceTypes = await this.prisma.recurrenceType.findMany({
        skip,
        take: limit,
        orderBy: { name: 'asc' },
      });

      return {
        data: recurrenceTypes,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding recurrence types:', error);
      throw new InternalServerErrorException('Could not retrieve recurrence types');
    }
  }

  /**
   * Find all active recurrence types (all recurrence types are considered active)
   */
  async findAllActive(): Promise<RecurrenceType[]> {
    try {
      const recurrenceTypes = await this.prisma.recurrenceType.findMany({
        orderBy: { name: 'asc' },
      });

      return recurrenceTypes;
    } catch (error) {
      console.error('Error finding active recurrence types:', error);
      throw new InternalServerErrorException('Could not retrieve active recurrence types');
    }
  }

  /**
   * Find one recurrence type by id
   */
  async findOne(id: string): Promise<RecurrenceType> {
    try {
      const recurrenceType = await this.prisma.recurrenceType.findUnique({
        where: { id },
      });

      if (!recurrenceType) {
        throw new NotFoundException(`Recurrence type with ID ${id} not found`);
      }

      return recurrenceType;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding recurrence type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve recurrence type');
    }
  }

  /**
   * Update a recurrence type
   */
  async update(id: string, updateRecurrenceTypeDto: UpdateRecurrenceTypeDto): Promise<RecurrenceType> {
    try {
      // Check if recurrence type exists
      await this.findOne(id);

      // If updating name, check if the new name already exists for another recurrence type
      if (updateRecurrenceTypeDto.name) {
        const existingRecurrenceType = await this.prisma.recurrenceType.findUnique({
          where: { name: updateRecurrenceTypeDto.name },
        });

        if (existingRecurrenceType && existingRecurrenceType.id !== id) {
          throw new ConflictException(`Recurrence type with name ${updateRecurrenceTypeDto.name} already exists`);
        }
      }

      // Update the recurrence type
      return await this.prisma.recurrenceType.update({
        where: { id },
        data: updateRecurrenceTypeDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.error(`Error updating recurrence type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update recurrence type');
    }
  }

  /**
   * Remove a recurrence type
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if recurrence type exists
      await this.findOne(id);

      // Check if recurrence type is being used by any account payable
      const accountsPayable = await this.prisma.accountsPayable.findFirst({
        where: { recurrenceTypeId: id },
      });

      if (accountsPayable) {
        throw new BadRequestException(
          'Cannot delete recurrence type because it is being used by one or more accounts payable'
        );
      }

      // Check if recurrence type is being used by any account receivable
      const accountsReceivable = await this.prisma.accountsReceivable.findFirst({
        where: { recurrenceTypeId: id },
      });

      if (accountsReceivable) {
        throw new BadRequestException(
          'Cannot delete recurrence type because it is being used by one or more accounts receivable'
        );
      }

      // Delete the recurrence type
      await this.prisma.recurrenceType.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error removing recurrence type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete recurrence type');
    }
  }
}
