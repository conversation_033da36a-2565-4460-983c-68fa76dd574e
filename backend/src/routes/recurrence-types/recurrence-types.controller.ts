// backend/src/routes/recurrence-types/recurrence-types.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { RecurrenceTypesService } from './recurrence-types.service';
import { CreateRecurrenceTypeDto } from './dto/create-recurrence-type.dto';
import { UpdateRecurrenceTypeDto } from './dto/update-recurrence-type.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('recurrence-types')
@Controller('recurrence-types')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
export class RecurrenceTypesController {
  constructor(private readonly recurrenceTypesService: RecurrenceTypesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo tipo de recorrência' })
  @ApiBody({ type: CreateRecurrenceTypeDto })
  @ApiResponse({
    status: 201,
    description: 'Tipo de recorrência criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Tipo de recorrência já existe' })
  create(@Body() createRecurrenceTypeDto: CreateRecurrenceTypeDto) {
    return this.recurrenceTypesService.create(createRecurrenceTypeDto);
  }

  @Get()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os tipos de recorrência' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number })
  @ApiResponse({ status: 200, description: 'Lista de tipos de recorrência' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite de itens por página inválido');
    }

    return this.recurrenceTypesService.findAll({
      page: pageNumber,
      limit: limitNumber,
    });
  }

  @Get('active')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os tipos de recorrência ativos' })
  @ApiResponse({ status: 200, description: 'Lista de tipos de recorrência ativos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAllActive() {
    return this.recurrenceTypesService.findAllActive();
  }

  @Get(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter um tipo de recorrência pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do tipo de recorrência', type: String })
  @ApiResponse({ status: 200, description: 'Tipo de recorrência encontrado' })
  @ApiResponse({ status: 404, description: 'Tipo de recorrência não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.recurrenceTypesService.findOne(id);
  }

  @Put(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um tipo de recorrência' })
  @ApiParam({ name: 'id', description: 'ID do tipo de recorrência', type: String })
  @ApiBody({ type: UpdateRecurrenceTypeDto })
  @ApiResponse({ status: 200, description: 'Tipo de recorrência atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Tipo de recorrência não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Nome já existe' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRecurrenceTypeDto: UpdateRecurrenceTypeDto,
  ) {
    return this.recurrenceTypesService.update(id, updateRecurrenceTypeDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um tipo de recorrência' })
  @ApiParam({ name: 'id', description: 'ID do tipo de recorrência', type: String })
  @ApiResponse({ status: 204, description: 'Tipo de recorrência removido com sucesso' })
  @ApiResponse({ status: 400, description: 'Não é possível remover o tipo de recorrência' })
  @ApiResponse({ status: 404, description: 'Tipo de recorrência não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.recurrenceTypesService.remove(id);
  }
}
