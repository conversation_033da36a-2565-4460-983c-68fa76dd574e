// backend/src/routes/recurrence-types/dto/create-recurrence-type.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateRecurrenceTypeDto {
  @ApiProperty({
    description: 'Nome do tipo de recorrência',
    example: 'mensal',
    maxLength: 50
  })
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição do tipo de recorrência',
    example: 'Recorrência mensal',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
