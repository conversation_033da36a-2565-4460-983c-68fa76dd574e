// backend/src/routes/accounts-receivable/accounts-receivable.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { AccountsReceivableService } from './accounts-receivable.service';
import { CreateAccountReceivableDto } from './dto/create-account-receivable.dto';
import { UpdateAccountReceivableDto } from './dto/update-account-receivable.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

import { RequestWithUser } from '../../interfaces/request-with-user.interface';

@ApiTags('accounts-receivable')
@Controller('accounts-receivable')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
// Apply RBAC guards globally or per route if needed
// @UseGuards(RolesGuard)
export class AccountsReceivableController {
  constructor(private readonly accountsReceivableService: AccountsReceivableService) {}

  @Post()
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova conta a receber' })
  @ApiBody({ type: CreateAccountReceivableDto })
  @ApiResponse({
    status: 201,
    description: 'Conta a receber criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createAccountReceivableDto: CreateAccountReceivableDto,
    @Req() req: RequestWithUser,
  ) {
    // Pass the authenticated user (including companyId) to the service
    return this.accountsReceivableService.create(createAccountReceivableDto, req.user);
  }

  @Get()
  // @Roles(Role.Admin, Role.FinanceManager, Role.FinanceViewer) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as contas a receber' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página (padrão: 10)' })
  @ApiQuery({ name: 'status', required: false, description: 'Filtrar por status (pending, received, partially_received, overdue, cancelled)' })
  @ApiQuery({ name: 'dueDateFrom', required: false, description: 'Data de vencimento inicial (formato: YYYY-MM-DD)' })
  @ApiQuery({ name: 'dueDateTo', required: false, description: 'Data de vencimento final (formato: YYYY-MM-DD)' })
  @ApiQuery({ name: 'entityId', required: false, description: 'ID da entidade (cliente)' })
  @ApiResponse({ status: 200, description: 'Lista de contas a receber' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string,
    @Query('dueDateFrom') dueDateFrom?: string,
    @Query('dueDateTo') dueDateTo?: string,
    @Query('entityId') entityId?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
        throw new BadRequestException('Invalid page number');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
        throw new BadRequestException('Invalid limit number');
    }

    return this.accountsReceivableService.findAll(req.user, {
        page: pageNumber,
        limit: limitNumber,
        status,
        dueDateFrom,
        dueDateTo,
        entityId
    });
  }

  @Get(':id')
  // @Roles(Role.Admin, Role.FinanceManager, Role.FinanceViewer) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma conta a receber pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da conta a receber' })
  @ApiResponse({ status: 200, description: 'Conta a receber encontrada' })
  @ApiResponse({ status: 404, description: 'Conta a receber não encontrada' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsReceivableService.findOne(id, req.user);
  }

  @Put(':id')
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma conta a receber' })
  @ApiParam({ name: 'id', description: 'ID da conta a receber' })
  @ApiBody({ type: UpdateAccountReceivableDto })
  @ApiResponse({ status: 200, description: 'Conta a receber atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Conta a receber não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAccountReceivableDto: UpdateAccountReceivableDto,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsReceivableService.update(id, updateAccountReceivableDto, req.user);
  }

  @Delete(':id')
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma conta a receber' })
  @ApiParam({ name: 'id', description: 'ID da conta a receber' })
  @ApiResponse({ status: 204, description: 'Conta a receber removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Conta a receber não encontrada' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsReceivableService.remove(id, req.user);
  }

  // Endpoint /receive removido
  // A ação de receber agora deve ser feita criando uma Transaction do tipo "income" vinculada à conta a receber
  // Use transactionService.createTransaction para esta finalidade
}
