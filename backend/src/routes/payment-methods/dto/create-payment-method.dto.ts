// backend/src/routes/payment-methods/dto/create-payment-method.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreatePaymentMethodDto {
  @ApiProperty({
    description: 'Nome do método de pagamento',
    example: 'pix',
    maxLength: 50
  })
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição do método de pagamento',
    example: 'Transferência via PIX',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
