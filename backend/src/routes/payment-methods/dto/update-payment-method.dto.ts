// backend/src/routes/payment-methods/dto/update-payment-method.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdatePaymentMethodDto {
  @ApiPropertyOptional({
    description: 'Nome do método de pagamento',
    example: 'pix',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: 'Descrição do método de pagamento',
    example: 'Transferência via PIX',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
