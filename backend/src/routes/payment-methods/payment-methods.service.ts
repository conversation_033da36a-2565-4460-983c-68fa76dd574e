// backend/src/routes/payment-methods/payment-methods.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { PaymentMethod } from '@prisma/client';

@Injectable()
export class PaymentMethodsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new payment method
   */
  async create(createPaymentMethodDto: CreatePaymentMethodDto): Promise<PaymentMethod> {
    try {
      // Check if a payment method with the same name already exists
      const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
        where: { name: createPaymentMethodDto.name },
      });

      if (existingPaymentMethod) {
        throw new ConflictException(`Payment method with name ${createPaymentMethodDto.name} already exists`);
      }

      // Create the payment method
      return await this.prisma.paymentMethod.create({
        data: createPaymentMethodDto,
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      console.error('Error creating payment method:', error);
      throw new InternalServerErrorException('Could not create payment method');
    }
  }

  /**
   * Find all payment methods with pagination
   */
  async findAll(
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: PaymentMethod[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    try {
      // Get total count
      const total = await this.prisma.paymentMethod.count();

      // Get payment methods with pagination
      const paymentMethods = await this.prisma.paymentMethod.findMany({
        skip,
        take: limit,
        orderBy: { name: 'asc' },
      });

      return {
        data: paymentMethods,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding payment methods:', error);
      throw new InternalServerErrorException('Could not retrieve payment methods');
    }
  }

  /**
   * Find all active payment methods (all payment methods are considered active)
   */
  async findAllActive(): Promise<PaymentMethod[]> {
    try {
      const paymentMethods = await this.prisma.paymentMethod.findMany({
        orderBy: { name: 'asc' },
      });

      return paymentMethods;
    } catch (error) {
      console.error('Error finding active payment methods:', error);
      throw new InternalServerErrorException('Could not retrieve active payment methods');
    }
  }

  /**
   * Find one payment method by id
   */
  async findOne(id: string): Promise<PaymentMethod> {
    try {
      const paymentMethod = await this.prisma.paymentMethod.findUnique({
        where: { id },
      });

      if (!paymentMethod) {
        throw new NotFoundException(`Payment method with ID ${id} not found`);
      }

      return paymentMethod;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding payment method with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve payment method');
    }
  }

  /**
   * Update a payment method
   */
  async update(id: string, updatePaymentMethodDto: UpdatePaymentMethodDto): Promise<PaymentMethod> {
    try {
      // Check if payment method exists
      await this.findOne(id);

      // If updating name, check if the new name already exists for another payment method
      if (updatePaymentMethodDto.name) {
        const existingPaymentMethod = await this.prisma.paymentMethod.findUnique({
          where: { name: updatePaymentMethodDto.name },
        });

        if (existingPaymentMethod && existingPaymentMethod.id !== id) {
          throw new ConflictException(`Payment method with name ${updatePaymentMethodDto.name} already exists`);
        }
      }

      // Update the payment method
      return await this.prisma.paymentMethod.update({
        where: { id },
        data: updatePaymentMethodDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.error(`Error updating payment method with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update payment method');
    }
  }

  /**
   * Remove a payment method
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if payment method exists
      await this.findOne(id);

      // Check if payment method is being used by any account payable
      const accountsPayable = await this.prisma.accountsPayable.findFirst({
        where: { paymentMethodId: id },
      });

      if (accountsPayable) {
        throw new BadRequestException(
          'Cannot delete payment method because it is being used by one or more accounts payable'
        );
      }

      // Check if payment method is being used by any account receivable
      const accountsReceivable = await this.prisma.accountsReceivable.findFirst({
        where: { paymentMethodId: id },
      });

      if (accountsReceivable) {
        throw new BadRequestException(
          'Cannot delete payment method because it is being used by one or more accounts receivable'
        );
      }

      // Check if payment method is being used by any transaction
      const transactions = await this.prisma.transaction.findFirst({
        where: { paymentMethodId: id },
      });

      if (transactions) {
        throw new BadRequestException(
          'Cannot delete payment method because it is being used by one or more transactions'
        );
      }

      // Delete the payment method
      await this.prisma.paymentMethod.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error removing payment method with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete payment method');
    }
  }
}
