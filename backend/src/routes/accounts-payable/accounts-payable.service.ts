// backend/src/routes/accounts-payable/accounts-payable.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service'; // Correct path
import { CreateAccountPayableDto } from './dto/create-account-payable.dto';
import { UpdateAccountPayableDto } from './dto/update-account-payable.dto';
import { AccountsPayableSummaryDto } from './dto/accounts-payable-summary.dto'; // Import the summary DTO

import { Prisma, AccountsPayable } from '@prisma/client'; // Import Prisma types
import { Decimal } from '@prisma/client/runtime/library'; // Import Decimal

// Define a type for the authenticated user payload (adjust as needed)
interface AuthenticatedUser {
  id: string;
  companyId: string; // Assuming companyId is available in the JWT payload or request context
  // Add other relevant user properties like roles/permissions if needed for RBAC
}

// Define status constants (optional but good practice)
enum AccountPayableStatus {
  PENDING = 'pending',
  PAID = 'paid',
  PARTIALLY_PAID = 'partially_paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
}

@Injectable()
export class AccountsPayableService {
  constructor(private prisma: PrismaService) {}

  // --- Helper: Validate related entities belong to the same company ---
  private async validateRelatedEntities(
    companyId: string,
    dto: CreateAccountPayableDto | UpdateAccountPayableDto,
  ): Promise<void> {
    const checks: Promise<any>[] = []; // Explicitly type the array

    if (dto.entityId) {
      checks.push(
        this.prisma.entity.findFirst({
          where: { id: dto.entityId, companyId, deletedAt: null },
        }),
      );
    }
    if (dto.currencyId) {
      // Currencies might be global, or company-specific. Adjust if global.
      // Assuming global for now, no companyId check needed unless they are company-specific.
      checks.push(this.prisma.currency.findUnique({ where: { id: dto.currencyId } }));
    }
    if (dto.categoryId) {
      checks.push(
        this.prisma.category.findFirst({
          where: { id: dto.categoryId, companyId, deletedAt: null },
        }),
      );
    }
    if (dto.projectId) {
      checks.push(
        this.prisma.project.findFirst({
          where: { id: dto.projectId, companyId, deletedAt: null },
        }),
      );
    }
    if (dto.paymentMethodId) {
      // PaymentMethods are global now, only check for existence by ID
      checks.push(
        this.prisma.paymentMethod.findUnique({
          where: { id: dto.paymentMethodId },
        }),
      );
    }
    if (dto.bankAccountId) {
      checks.push(
        this.prisma.bankAccount.findFirst({
          where: { id: dto.bankAccountId, companyId, deletedAt: null },
        }),
      );
    }
    if (dto.recurrenceTypeId) {
      // RecurrenceTypes are global now, only check for existence by ID
      checks.push(
        this.prisma.recurrenceType.findUnique({
          where: { id: dto.recurrenceTypeId },
        }),
      );
    }
     if (dto.parentId) {
      checks.push(
        this.prisma.accountsPayable.findFirst({
          where: { id: dto.parentId, companyId, deletedAt: null },
        }),
      );
    }

    try {
      const results = await Promise.all(checks);
      const invalidEntity = results.some((result, index) => {
        // Skip currency check if it's global
        if (index === 1 && dto.currencyId) return !result;
        // Check others
        return !result;
      });

      if (invalidEntity) {
        throw new BadRequestException(
          'One or more related entities not found or do not belong to the company.',
        );
      }
    } catch (error) {
       if (error instanceof BadRequestException) {
         throw error;
       }
      console.error('Error validating related entities:', error);
      throw new InternalServerErrorException('Error validating related entities.');
    }
  }

  // --- CRUD Operations ---

  async create(
    createDto: CreateAccountPayableDto,
    user: AuthenticatedUser,
  ): Promise<AccountsPayable> {
    const { companyId } = user;

    // 1. Validate related entities
    await this.validateRelatedEntities(companyId, createDto);

    // 2. Prepare data
    const data: Prisma.AccountsPayableCreateInput = {
      ...createDto,
      dueDate: new Date(createDto.dueDate), // Convert string date to Date object
      amount: new Decimal(createDto.amount), // Convert number to Decimal
      interestAmount: createDto.interestAmount ? new Decimal(createDto.interestAmount) : undefined,
      discountAmount: createDto.discountAmount ? new Decimal(createDto.discountAmount) : undefined,
      paidAmount: new Decimal(0), // Initial paid amount is 0
      status: AccountPayableStatus.PENDING, // Initial status
      company: { connect: { id: companyId } },
      entity: { connect: { id: createDto.entityId } },
      currency: { connect: { id: createDto.currencyId } },
      // Connect optional relations only if they exist in the DTO
      ...(createDto.categoryId && { category: { connect: { id: createDto.categoryId } } }),
      ...(createDto.projectId && { project: { connect: { id: createDto.projectId } } }),
      ...(createDto.paymentMethodId && { paymentMethod: { connect: { id: createDto.paymentMethodId } } }),
      ...(createDto.bankAccountId && { bankAccount: { connect: { id: createDto.bankAccountId } } }),
      ...(createDto.recurrenceTypeId && { recurrenceType: { connect: { id: createDto.recurrenceTypeId } } }),
      ...(createDto.parentId && { parent: { connect: { id: createDto.parentId } } }),
    };

    // 3. Create record
    try {
      return await this.prisma.accountsPayable.create({ data });
    } catch (error) {
      console.error('Error creating account payable:', error);
      // Add more specific error handling (e.g., unique constraint violation) if needed
      throw new InternalServerErrorException('Could not create account payable.');
    }
  }

  async findAll(
    user: AuthenticatedUser,
    // Add filter and pagination params here
    // e.g., status?: string, dueDateFrom?: string, dueDateTo?: string, entityId?: string, page: number = 1, limit: number = 10
    { page = 1, limit = 10, status, dueDateFrom, dueDateTo, entityId }: {
        page?: number;
        limit?: number;
        status?: string;
        dueDateFrom?: string;
        dueDateTo?: string;
        entityId?: string;
    } = {}
  ): Promise<{ data: AccountsPayable[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
    const skip = (page - 1) * limit;

    const where: Prisma.AccountsPayableWhereInput = {
      companyId,
      deletedAt: null,
      ...(status && { status }),
      ...(entityId && { entityId }),
      ...(dueDateFrom || dueDateTo
        ? {
            dueDate: {
              ...(dueDateFrom && { gte: new Date(dueDateFrom) }),
              ...(dueDateTo && { lte: new Date(dueDateTo) }),
            },
          }
        : {}),
    };

    try {
      const [data, total] = await this.prisma.$transaction([
        this.prisma.accountsPayable.findMany({
          where,
          skip,
          take: limit,
          orderBy: { dueDate: 'asc' }, // Default sort order
          // Include relations if needed for the list view
          // include: { entity: true, category: true }
        }),
        this.prisma.accountsPayable.count({ where }),
      ]);
      return { data, total, page, limit };
    } catch (error) {
      console.error('Error finding accounts payable:', error);
      throw new InternalServerErrorException('Could not retrieve accounts payable.');
    }
  }

  async findOne(id: string, user: AuthenticatedUser): Promise<AccountsPayable> {
    const { companyId } = user;
    try {
      const account = await this.prisma.accountsPayable.findFirst({
        where: { id, companyId, deletedAt: null },
        // Include relations if needed for detail view
        // include: { entity: true, category: true, project: true, ... }
      });

      if (!account) {
        throw new NotFoundException(`Account Payable with ID ${id} not found.`);
      }
      return account;
    } catch (error) {
       if (error instanceof NotFoundException) {
         throw error;
       }
      console.error(`Error finding account payable with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve account payable.');
    }
  }

  async update(
    id: string,
    updateDto: UpdateAccountPayableDto,
    user: AuthenticatedUser,
  ): Promise<AccountsPayable> {
    const { companyId } = user;

    // 1. Ensure the record exists and belongs to the company
    const existingAccount = await this.findOne(id, user); // findOne already checks companyId and deletedAt

    // 2. Validate related entities if they are being changed
    await this.validateRelatedEntities(companyId, updateDto);

    // 3. Prepare update data (only include fields present in the DTO)
    const data: Prisma.AccountsPayableUpdateInput = {
      ...(updateDto.description && { description: updateDto.description }),
      ...(updateDto.entityId && { entity: { connect: { id: updateDto.entityId } } }),
      ...(updateDto.dueDate && { dueDate: new Date(updateDto.dueDate) }),
      ...(updateDto.amount && { amount: new Decimal(updateDto.amount) }),
      ...(updateDto.currencyId && { currency: { connect: { id: updateDto.currencyId } } }),
      ...(updateDto.categoryId && { category: { connect: { id: updateDto.categoryId } } }),
      ...(updateDto.projectId && { project: { connect: { id: updateDto.projectId } } }),
      ...(updateDto.paymentMethodId && { paymentMethod: { connect: { id: updateDto.paymentMethodId } } }),
      ...(updateDto.bankAccountId && { bankAccount: { connect: { id: updateDto.bankAccountId } } }),
      ...(updateDto.recurrenceTypeId && { recurrenceType: { connect: { id: updateDto.recurrenceTypeId } } }),
      ...(updateDto.invoiceNumber && { invoiceNumber: updateDto.invoiceNumber }),
      ...(updateDto.notes && { notes: updateDto.notes }),
      ...(updateDto.interestAmount && { interestAmount: new Decimal(updateDto.interestAmount) }),
      ...(updateDto.discountAmount && { discountAmount: new Decimal(updateDto.discountAmount) }),
      ...(updateDto.installments && { installments: updateDto.installments }),
      ...(updateDto.installmentNumber && { installmentNumber: updateDto.installmentNumber }),
       // Handle parentId update carefully - might need disconnect logic if changing parent
      ...(updateDto.parentId && { parent: { connect: { id: updateDto.parentId } } }),
       // If parentId is explicitly set to null or undefined in DTO, disconnect it
      ...(updateDto.hasOwnProperty('parentId') && !updateDto.parentId && { parent: { disconnect: true } }),
      // Note: Status and paidAmount are typically updated via separate actions (e.g., payment transaction)
    };

    // Prevent updating if no data is provided
     if (Object.keys(data).length === 0) {
       return existingAccount; // Or throw BadRequestException
     }


    // 4. Update record
    try {
      return await this.prisma.accountsPayable.update({
        where: { id }, // No need for companyId here as findOne already verified ownership
        data,
      });
    } catch (error) {
      console.error(`Error updating account payable with ID ${id}:`, error);
      // Add specific error handling (e.g., relation not found if validation missed something)
      throw new InternalServerErrorException('Could not update account payable.');
    }
  }

  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    const { companyId } = user;

    // 1. Ensure the record exists and belongs to the company
    const account = await this.findOne(id, user); // Verifies ownership and existence

    // 2. Perform soft delete
    try {
      await this.prisma.accountsPayable.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error soft-deleting account payable with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete account payable.');
    }
  }

  // --- Summary Calculation --- New Method

  async getSummary(user: AuthenticatedUser): Promise<AccountsPayableSummaryDto> {
    const { companyId } = user;
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today

    const thirtyDaysLater = new Date(today);
    thirtyDaysLater.setDate(today.getDate() + 30);
    thirtyDaysLater.setHours(23, 59, 59, 999); // End of 30th day

    const baseWhere: Prisma.AccountsPayableWhereInput = {
      companyId,
      deletedAt: null,
    };

    const dueStatuses = [
      AccountPayableStatus.PENDING,
      AccountPayableStatus.PARTIALLY_PAID,
      AccountPayableStatus.OVERDUE,
    ];
    const upcomingStatuses = [
      AccountPayableStatus.PENDING,
      AccountPayableStatus.PARTIALLY_PAID,
    ];

    try {
      const [totalDueResult, totalOverdueResult, totalUpcomingResult] =
        await this.prisma.$transaction([
          // Total Due Calculation
          this.prisma.accountsPayable.aggregate({
            _sum: {
              amount: true,
              paidAmount: true,
            },
            where: {
              ...baseWhere,
              status: { in: dueStatuses },
            },
          }),
          // Total Overdue Calculation
          this.prisma.accountsPayable.aggregate({
            _sum: {
              amount: true,
              paidAmount: true,
            },
            where: {
              ...baseWhere,
              status: AccountPayableStatus.OVERDUE,
            },
          }),
          // Total Upcoming Calculation (next 30 days)
          this.prisma.accountsPayable.aggregate({
            _sum: {
              amount: true,
              paidAmount: true,
            },
            where: {
              ...baseWhere,
              status: { in: upcomingStatuses },
              dueDate: {
                gte: today,
                lte: thirtyDaysLater,
              },
            },
          }),
        ]);

      const calculateBalance = (sum: { amount: Decimal | null; paidAmount: Decimal | null } | null): number => {
        if (!sum || !sum.amount) return 0;
        const amount = sum.amount || new Decimal(0);
        const paidAmount = sum.paidAmount || new Decimal(0);
        return amount.sub(paidAmount).toNumber();
      };

      const totalDue = calculateBalance(totalDueResult._sum);
      const totalOverdue = calculateBalance(totalOverdueResult._sum);
      const totalUpcoming = calculateBalance(totalUpcomingResult._sum);

      return {
        totalDue,
        totalOverdue,
        totalUpcoming,
      };
    } catch (error) {
      console.error('Error calculating accounts payable summary:', error);
      throw new InternalServerErrorException(
        'Could not calculate accounts payable summary.',
      );
    }
  }
}