import { ApiProperty } from '@nestjs/swagger';

export class AccountsPayableSummaryDto {
  @ApiProperty({
    description: 'Valor total de todas as contas a pagar em aberto (pending, partial, overdue)',
    example: 15500.75,
  })
  totalDue: number;

  @ApiProperty({
    description: 'Valor total das contas a pagar vencidas (overdue)',
    example: 2500.50,
  })
  totalOverdue: number;

  @ApiProperty({
    description: 'Valor total das contas a pagar a vencer nos próximos 30 dias (pending, partial)',
    example: 8340.25, // Exemplo para os próximos 30 dias
  })
  totalUpcoming: number;
}
