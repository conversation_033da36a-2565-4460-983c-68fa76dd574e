// backend/src/routes/accounts-payable/dto/create-account-payable.dto.ts
import {
  IsString,
  IsUUID,
  IsDateString,
  IsNumber,
  IsOptional,
  IsInt,
  Min,
  MaxLength,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer'; // Required for IsNumber with decimal

export class CreateAccountPayableDto {
  @IsString()
  @MaxLength(255)
  description: string;

  @IsUUID()
  entityId: string;

  @IsDateString()
  dueDate: string; // Use string for input, transform/validate as Date in service if needed

  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Type(() => Number) // Ensure transformation from string if needed
  amount: number;

  @IsUUID()
  currencyId: string;

  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @IsOptional()
  @IsUUID()
  projectId?: string;

  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @IsOptional()
  @IsUUID()
  bankAccountId?: string;

  @IsOptional()
  @IsUUID()
  recurrenceTypeId?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  invoiceNumber?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  interestAmount?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  discountAmount?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  installments?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  installmentNumber?: number;

  @IsOptional()
  @IsUUID()
  parentId?: string;

  // companyId is added in the service based on the authenticated user
  // status is managed internally
  // paidAmount is managed internally (likely via transactions)
}