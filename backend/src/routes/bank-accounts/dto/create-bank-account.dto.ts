// backend/src/routes/bank-accounts/dto/create-bank-account.dto.ts
import {
  IsString,
  IsUUID,
  IsNumber,
  IsOptional,
  IsBoolean,
  MaxLength,
  IsDateString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBankAccountDto {
  @ApiProperty({
    description: 'ID do banco',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid'
  })
  @IsUUID()
  bankId: string;

  @ApiProperty({
    description: 'Número da conta',
    example: '12345-6',
    maxLength: 20
  })
  @IsString()
  @MaxLength(20)
  accountNumber: string;

  @ApiProperty({
    description: 'Tipo de conta',
    example: 'corrente',
    enum: ['corrente', 'poupanca', 'investimento', 'dinheiro', 'outro']
  })
  @IsString()
  @MaxLength(50)
  accountType: string; // e.g., 'corrente', 'poupanca', 'investimento', 'dinheiro', 'outro'

  @ApiProperty({
    description: 'Saldo inicial',
    example: 1000.00,
    type: 'number',
    format: 'double'
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Type(() => Number)
  initialBalance: number; // Initial balance

  @ApiProperty({
    description: 'Data do saldo inicial',
    example: '2025-04-04T00:00:00.000Z',
    type: 'string',
    format: 'date-time'
  })
  @IsDateString()
  balanceDate: string; // Date for the initial balance

  @ApiProperty({
    description: 'Limite de crédito',
    example: 500.00,
    type: 'number',
    format: 'double',
    required: false,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  creditLimit?: number;

  @ApiProperty({
    description: 'ID da moeda',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid'
  })
  @IsUUID()
  currencyId: string;

  @ApiProperty({
    description: 'Nome da conta',
    example: 'Conta Corrente Principal',
    maxLength: 100
  })
  @IsString()
  @MaxLength(100)
  name: string; // User-friendly name for the account

  @ApiProperty({
    description: 'Indica se a conta está ativa',
    example: true,
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isEnabled?: boolean;

  @ApiProperty({
    description: 'ID da empresa (opcional, se não fornecido usa o ID da empresa do usuário)',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
    required: false
  })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  // companyId is added in the service
}