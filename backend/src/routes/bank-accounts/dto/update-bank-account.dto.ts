// backend/src/routes/bank-accounts/dto/update-bank-account.dto.ts
import {
  IsString,
  IsUUID,
  IsNumber,
  IsOptional,
  IsBoolean,
  MaxLength,
  IsDateString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Using PartialType might be simpler here, but explicit definition for clarity
export class UpdateBankAccountDto {
  @ApiPropertyOptional({
    description: 'ID do banco',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  bankId?: string;

  @ApiPropertyOptional({
    description: 'Número da conta',
    example: '12345-6',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Tipo de conta',
    example: 'corrente',
    enum: ['corrente', 'poupanca', 'investimento', 'dinheiro', 'outro', 'checking', 'savings', 'investment', 'cash', 'other']
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  accountType?: string;

  @ApiPropertyOptional({
    description: 'Saldo inicial (só pode ser alterado se não estiver bloqueado)',
    example: 1000.00,
    type: 'number',
    format: 'double'
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Type(() => Number)
  initialBalance?: number;

  @ApiPropertyOptional({
    description: 'Limite de crédito',
    example: 1000.00,
    type: 'number',
    format: 'double',
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  creditLimit?: number;

  @ApiPropertyOptional({
    description: 'ID da moeda',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid'
  })
  @IsOptional()
  @IsUUID()
  currencyId?: string;

  @ApiPropertyOptional({
    description: 'Nome da conta',
    example: 'Conta Corrente Atualizada',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Indica se a conta está ativa',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isEnabled?: boolean;
}