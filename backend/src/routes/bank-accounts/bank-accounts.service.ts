// backend/src/routes/bank-accounts/bank-accounts.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateBankAccountDto } from './dto/create-bank-account.dto';
import { UpdateBankAccountDto } from './dto/update-bank-account.dto';
import { Prisma, BankAccount } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

// Define a type for the authenticated user payload (adjust as needed)
interface AuthenticatedUser {
  id: string;
  companyId: string;
}

@Injectable()
export class BankAccountsService {
  constructor(private prisma: PrismaService) {}

  // --- Helper: Validate related entities ---
  private async validateRelatedEntities(
    companyId: string, // Company ID for context, though Bank/Currency might be global
    dto: CreateBankAccountDto | UpdateBankAccountDto,
  ): Promise<void> {
    const checks: Promise<any>[] = [];

    if (dto.bankId) {
      // Banks are global based on docs/database.sql, no companyId check needed
      checks.push(this.prisma.bank.findUnique({ where: { id: dto.bankId } }));
    }
    if (dto.currencyId) {
      // Currencies are global based on docs/database.sql, no companyId check needed
      checks.push(this.prisma.currency.findUnique({ where: { id: dto.currencyId } }));
    }

    try {
      const results = await Promise.all(checks);
      const invalidEntity = results.some((result) => !result);

      if (invalidEntity) {
        throw new BadRequestException('Bank or Currency not found.');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error validating related entities for Bank Account:', error);
      throw new InternalServerErrorException('Error validating related entities.');
    }
  }

  // --- CRUD Operations ---

  async create(
    createDto: CreateBankAccountDto,
    user: AuthenticatedUser,
  ): Promise<BankAccount> {
    // Usar o companyId do DTO se estiver presente, caso contrário usar o do usuário autenticado
    const effectiveCompanyId = createDto.companyId || user.companyId;
    
    console.log(`[BankAccountsService] Creating bank account with companyId: ${effectiveCompanyId} (from DTO: ${createDto.companyId}, from user: ${user.companyId})`);

    // 1. Validate related Bank and Currency
    await this.validateRelatedEntities(effectiveCompanyId, createDto);

    // 2. Prepare data
    const data: Prisma.BankAccountCreateInput = {
      accountNumber: createDto.accountNumber,
      accountType: createDto.accountType,
      initialBalance: new Decimal(createDto.initialBalance),
      currentBalance: new Decimal(createDto.initialBalance), // Current balance starts as initial balance
      isInitialBalanceLocked: false,
      balanceDate: new Date(createDto.balanceDate),
      creditLimit: createDto.creditLimit ? new Decimal(createDto.creditLimit) : new Decimal(0),
      name: createDto.name,
      isEnabled: createDto.isEnabled ?? true,
      company: { connect: { id: effectiveCompanyId } },
      bank: { connect: { id: createDto.bankId } },
      currency: { connect: { id: createDto.currencyId } },
    };

    // 3. Create record
    try {
      return await this.prisma.bankAccount.create({ data });
    } catch (error) {
      console.error('Error creating bank account:', error);
      // Add more specific error handling if needed
      throw new InternalServerErrorException('Could not create bank account.');
    }
  }

  async findAll(
    user: AuthenticatedUser,
     { page = 1, limit = 10, isEnabled }: {
        page?: number;
        limit?: number;
        isEnabled?: boolean;
    } = {}
  ): Promise<{ data: BankAccount[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
     const skip = (page - 1) * limit;

    // Log para depuração
    console.log(`[BankAccountsService] Finding bank accounts for companyId: ${companyId}`);

    const where: Prisma.BankAccountWhereInput = {
      companyId, // Usar o companyId que foi passado pelo controlador
      deletedAt: null,
      ...(isEnabled !== undefined && { isEnabled }), // Filter by isEnabled if provided
    };

    // Log para depuração
    console.log(`[BankAccountsService] Query where clause:`, JSON.stringify(where));

    try {
       const [data, total] = await this.prisma.$transaction([
        this.prisma.bankAccount.findMany({
          where,
          skip,
          take: limit,
          orderBy: { name: 'asc' }, // Default sort order
          include: { bank: true, currency: true } // Include related bank and currency
        }),
        this.prisma.bankAccount.count({ where }),
      ]);

      // Log para depuração
      console.log(`[BankAccountsService] Found ${data.length} bank accounts out of ${total} total`);
      
      return { data, total, page, limit };
    } catch (error) {
      console.error('Error finding bank accounts:', error);
      throw new InternalServerErrorException('Could not retrieve bank accounts.');
    }
  }

  async findOne(id: string, user: AuthenticatedUser): Promise<BankAccount> {
    const { companyId } = user;
    console.log(`[BankAccountsService] Finding bank account with ID: ${id} for companyId: ${companyId}`);
    console.log(`[BankAccountsService] User object:`, JSON.stringify(user, null, 2));

    try {
      const account = await this.prisma.bankAccount.findFirst({
        where: { id, companyId, deletedAt: null },
        include: { bank: true, currency: true } // Include related bank and currency
      });

      console.log(`[BankAccountsService] Query result:`, account ? 'Found' : 'Not found');
      if (account) {
        console.log(`[BankAccountsService] Found account:`, JSON.stringify({
          id: account.id,
          name: account.name,
          companyId: account.companyId
        }, null, 2));
      }

      if (!account) {
        throw new NotFoundException(`Bank Account with ID ${id} not found for company ${companyId}.`);
      }
      return account;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding bank account with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve bank account.');
    }
  }

  async update(
    id: string,
    updateDto: UpdateBankAccountDto,
    user: AuthenticatedUser,
  ): Promise<BankAccount> {
    const { companyId } = user;

    console.log(`[BankAccountsService] Starting update for bank account ID: ${id}`);
    console.log(`[BankAccountsService] Update DTO received:`, JSON.stringify(updateDto, null, 2));
    console.log(`[BankAccountsService] User companyId: ${companyId}`);

    // 1. Ensure the record exists and belongs to the company
    const existingAccount = await this.findOne(id, user);
    console.log(`[BankAccountsService] Existing account found:`, JSON.stringify({
      id: existingAccount.id,
      name: existingAccount.name,
      accountType: existingAccount.accountType,
      accountNumber: existingAccount.accountNumber,
      bankId: existingAccount.bankId
    }, null, 2));

    // 2. Validate related entities if they are being changed
    await this.validateRelatedEntities(companyId, updateDto);

    // 3. Prepare update data with more flexible handling
    const data: Prisma.BankAccountUpdateInput = {};

    // Handle each field individually with proper validation
    if (updateDto.bankId !== undefined) {
      if (updateDto.bankId === null || updateDto.bankId === '') {
        // Para campos opcionais, simplesmente não incluir no update
        console.log(`[BankAccountsService] Skipping bank update (empty value)`);
      } else {
        data.bank = { connect: { id: updateDto.bankId } };
        console.log(`[BankAccountsService] Connecting to bank: ${updateDto.bankId}`);
      }
    }

    if (updateDto.accountNumber !== undefined) {
      data.accountNumber = updateDto.accountNumber || undefined;
      console.log(`[BankAccountsService] Setting accountNumber: ${updateDto.accountNumber}`);
    }

    if (updateDto.accountType !== undefined) {
      // Mapear tipos do frontend para o formato do backend se necessário
      let mappedAccountType = updateDto.accountType;

      // Converter tipos em inglês para português se necessário
      if (updateDto.accountType === 'checking') mappedAccountType = 'corrente';
      else if (updateDto.accountType === 'savings') mappedAccountType = 'poupanca';
      else if (updateDto.accountType === 'investment') mappedAccountType = 'investimento';
      else if (updateDto.accountType === 'cash') mappedAccountType = 'dinheiro';
      else if (updateDto.accountType === 'other') mappedAccountType = 'outro';

      data.accountType = mappedAccountType;
      console.log(`[BankAccountsService] Setting accountType: ${updateDto.accountType} -> ${mappedAccountType}`);
    }

    if (updateDto.creditLimit !== undefined) {
      data.creditLimit = updateDto.creditLimit ? new Decimal(updateDto.creditLimit) : null;
      console.log(`[BankAccountsService] Setting creditLimit: ${updateDto.creditLimit}`);
    }

    if (updateDto.currencyId !== undefined) {
      if (updateDto.currencyId === null || updateDto.currencyId === '') {
        // Para campos opcionais, simplesmente não incluir no update
        console.log(`[BankAccountsService] Skipping currency update (empty value)`);
      } else {
        data.currency = { connect: { id: updateDto.currencyId } };
        console.log(`[BankAccountsService] Connecting to currency: ${updateDto.currencyId}`);
      }
    }

    if (updateDto.name !== undefined) {
      data.name = updateDto.name;
      console.log(`[BankAccountsService] Setting name: ${updateDto.name}`);
    }

    if (updateDto.isEnabled !== undefined) {
      data.isEnabled = updateDto.isEnabled;
      console.log(`[BankAccountsService] Setting isEnabled: ${updateDto.isEnabled}`);
    }

    // Handle initial balance update (only if not locked)
    if (updateDto.initialBalance !== undefined) {
      if (existingAccount.isInitialBalanceLocked) {
        console.log(`[BankAccountsService] Cannot update initialBalance: account is locked`);
        throw new BadRequestException('Cannot update initial balance: account has transactions and is locked');
      } else {
        const newInitialBalance = new Decimal(updateDto.initialBalance);
        data.initialBalance = newInitialBalance;
        // Also update current balance to maintain consistency if no transactions exist
        data.currentBalance = newInitialBalance;
        console.log(`[BankAccountsService] Setting initialBalance: ${updateDto.initialBalance}`);
      }
    }

    console.log(`[BankAccountsService] Final update data:`, JSON.stringify(data, null, 2));

     // Prevent updating if no data is provided
     if (Object.keys(data).length === 0) {
       console.log(`[BankAccountsService] No data to update, returning existing account`);
       return existingAccount;
     }

    // 4. Update record
    try {
      console.log(`[BankAccountsService] Executing Prisma update...`);
      const updatedAccount = await this.prisma.bankAccount.update({
        where: { id },
        data,
        include: { bank: true, currency: true } // Return updated data with relations
      });

      console.log(`[BankAccountsService] Update successful:`, JSON.stringify({
        id: updatedAccount.id,
        name: updatedAccount.name,
        accountType: updatedAccount.accountType,
        accountNumber: updatedAccount.accountNumber,
        bankId: updatedAccount.bankId
      }, null, 2));

      return updatedAccount;
    } catch (error) {
      console.error(`[BankAccountsService] Error updating bank account with ID ${id}:`, error);
      console.error(`[BankAccountsService] Error details:`, {
        message: error.message,
        code: error.code,
        meta: error.meta
      });
      throw new InternalServerErrorException('Could not update bank account.');
    }
  }

  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // 1. Ensure the record exists and belongs to the company
    await this.findOne(id, user);

    // 2. Perform soft delete
    try {
      await this.prisma.bankAccount.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error soft-deleting bank account with ID ${id}:`, error);
      // Consider checking for related active transactions before deleting
      throw new InternalServerErrorException('Could not delete bank account.');
    }
  }

  /**
   * Get the current balance of a bank account
   * This method returns the current balance which is managed by the database trigger
   * recalculate_bank_account_balance
   */
  async getBalance(id: string, user: AuthenticatedUser): Promise<{ balance: Decimal; balanceDate: Date }> {
    const { companyId } = user;

    try {
      // Find the account and verify ownership
      const account = await this.prisma.bankAccount.findFirst({
        where: { id, companyId, deletedAt: null },
        select: { currentBalance: true, balanceDate: true }
      });

      if (!account) {
        throw new NotFoundException(`Bank Account with ID ${id} not found.`);
      }

      return {
        balance: account.currentBalance,
        balanceDate: account.balanceDate
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error getting balance for bank account with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve bank account balance.');
    }
  }
}