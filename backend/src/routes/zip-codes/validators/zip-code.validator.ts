import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';

@ValidatorConstraint({ name: 'zipCodeFormat', async: false })
export class ZipCodeFormatValidator implements ValidatorConstraintInterface {
  validate(text: string, args: ValidationArguments) {
    // Verifica se o CEP está no formato XXXXX-XXX ou se contém apenas dígitos
    const cepRegex = /^(\d{5}-\d{3}|\d{8})$/;
    return cepRegex.test(text);
  }

  defaultMessage(args: ValidationArguments) {
    return 'CEP inválido. Use o formato XXXXX-XXX ou 8 dígitos sem hífen.';
  }
}

@ValidatorConstraint({ name: 'validBrazilianState', async: false })
export class BrazilianStateValidator implements ValidatorConstraintInterface {
  validate(state: string, args: ValidationArguments) {
    const validStates = [
      'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 
      'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 
      'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
    ];
    
    return validStates.includes(state.toUpperCase());
  }

  defaultMessage(args: ValidationArguments) {
    return 'Estado inválido. Deve ser uma UF válida do Brasil.';
  }
}
