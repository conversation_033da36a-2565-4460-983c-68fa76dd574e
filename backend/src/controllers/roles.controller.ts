import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from '../services/roles.service';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CreateRoleDto, RoleDto, UpdateRoleDto } from '../models/role.model';

@ApiTags('roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo papel' })
  @ApiBody({ type: CreateRoleDto })
  @ApiResponse({
    status: 201,
    description: 'Papel criado com sucesso',
    type: RoleDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 409, description: 'Papel já existe' })
  async create(@Body() createRoleDto: CreateRoleDto): Promise<RoleDto> {
    return this.rolesService.create(createRoleDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os papéis de uma empresa' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({ status: 200, description: 'Lista de papéis' })
  async findAll(
    @Query('companyId') companyId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.rolesService.findAll(companyId, page, limit);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um papel pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiResponse({ status: 200, description: 'Papel encontrado', type: RoleDto })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  async findOne(@Param('id') id: string): Promise<RoleDto> {
    return this.rolesService.findOne(id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um papel' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiBody({ type: UpdateRoleDto })
  @ApiResponse({
    status: 200,
    description: 'Papel atualizado com sucesso',
    type: RoleDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  @ApiResponse({ status: 409, description: 'Nome já existe' })
  async update(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<RoleDto> {
    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um papel' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiResponse({ status: 204, description: 'Papel removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  @ApiResponse({ status: 409, description: 'Papel está em uso' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.rolesService.remove(id);
  }

  @Post('setup')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configurar tabela de papéis e criar papel padrão' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiResponse({
    status: 201,
    description: 'Configuração concluída com sucesso',
    type: RoleDto,
  })
  async setup(@Query('companyId') companyId: string): Promise<RoleDto> {
    await this.rolesService.ensureRolesTableExists();
    return this.rolesService.createDefaultRole(companyId);
  }
}
