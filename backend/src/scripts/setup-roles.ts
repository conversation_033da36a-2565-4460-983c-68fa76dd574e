import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

async function setupRoles() {
  const prisma = new PrismaClient();

  try {
    // Conectando ao banco de dados - log removido por segurança
    await prisma.$connect();

    // Ler e executar o script SQL para criar a tabela de papéis
    // Verificando se a tabela de papéis existe - log removido por segurança
    // Tentar vários caminhos possíveis para o arquivo SQL
    let sqlFilePath = path.join(__dirname, 'create-roles-table.sql');

    // Se o arquivo não existir no caminho padrão, tente outros caminhos
    if (!fs.existsSync(sqlFilePath)) {
      // Arquivo não encontrado - tentando caminho alternativo
      sqlFilePath = path.join(
        process.cwd(),
        'src/scripts/create-roles-table.sql',
      );
      // Tentando caminho alternativo - log removido por segurança
    }

    if (!fs.existsSync(sqlFilePath)) {
      // Arquivo não encontrado - tentando caminho absoluto
      sqlFilePath = '/app/src/scripts/create-roles-table.sql';
      // Tentando caminho absoluto - log removido por segurança
    }

    // Buscando arquivo SQL - log removido por segurança
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

    await prisma.$executeRawUnsafe(sqlScript);
    // Tabela de papéis verificada/criada - log removido por segurança

    // Buscar todas as empresas
    // Buscando empresas - log removido por segurança
    const companies = await prisma.$queryRaw<any[]>`
      SELECT id FROM companies WHERE deleted_at IS NULL
    `;

    if (companies.length === 0) {
      // Nenhuma empresa encontrada para criar papéis padrão - log removido por segurança
      return;
    }

    console.log(`Encontradas ${companies.length} empresas.`);

    // Papéis padrão a serem criados para cada empresa
    const defaultRoles = [
      { name: 'Administrador', description: 'Acesso completo ao sistema' },
      {
        name: 'Gerente',
        description: 'Gerencia usuários e recursos da empresa',
      },
      { name: 'Operador', description: 'Acesso operacional básico' },
      { name: 'Visualizador', description: 'Apenas visualização de dados' },
    ];

    // Criar papéis padrão para cada empresa
    for (const company of companies) {
      console.log(`Criando papéis padrão para a empresa ${company.id}...`);

      for (const role of defaultRoles) {
        // Verificar se o papel já existe para esta empresa
        const existingRole = await prisma.$queryRaw<any[]>`
          SELECT id FROM roles 
          WHERE company_id = ${company.id}::uuid 
          AND name = ${role.name}
        `;

        if (existingRole.length === 0) {
          // Criar o papel se não existir
          const roleId = uuidv4();
          await prisma.$executeRaw`
            INSERT INTO roles (
              id, 
              name, 
              description, 
              company_id, 
              created_at, 
              updated_at,
              deleted_at
            ) VALUES (
              ${roleId}::uuid, 
              ${role.name}, 
              ${role.description}, 
              ${company.id}::uuid, 
              NOW(), 
              NOW(),
              NULL
            )
          `;
          console.log(`  - Papel '${role.name}' criado com sucesso.`);
        } else {
          console.log(`  - Papel '${role.name}' já existe para esta empresa.`);
        }
      }
    }

    // Configuração de papéis concluída - log removido por segurança
  } catch (error) {
    // Erro ao configurar papéis - log removido por segurança
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a função
setupRoles()
  .then(() => {
    // Script finalizado - log removido por segurança
    process.exit(0);
  })
  .catch((error) => {
    // Erro ao executar o script - log removido por segurança
    process.exit(1);
  });
