import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger, LogLevel } from '@nestjs/common';
import { PrismaService } from './services/prisma.service';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

// Função principal de bootstrap - teste de hot-reloading
async function bootstrap() {
  // Configurar níveis de log para não incluir DEBUG
  const logger = new Logger('Bootstrap');
  const logLevels: LogLevel[] = ['error', 'warn'];

  const app = await NestFactory.create(AppModule, {
    logger: logLevels,
  });

  // Habilitar validação global
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Habilitar CORS com configuração detalhada
  app.enableCors({
    origin: [
      'http://localhost:5173', // Frontend em desenvolvimento Vite
      'http://localhost:3000', // Frontend em produção
      'http://localhost:3001', // Frontend em Docker
      'http://localhost', // Outros ambientes locais
      process.env.FRONTEND_URL || '', // URL do frontend em produção (da variável de ambiente)
    ].filter(Boolean),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID', 'X-Company-ID'],
    credentials: true,
    maxAge: 86400, // 24 horas em segundos
  });

  // Configurar prefixo global para a API
  app.setGlobalPrefix('api');

  // Configurar Swagger
  const config = new DocumentBuilder()
    .setTitle('FluxoMax API - Ambiente de Desenvolvimento')
    .setDescription(
      'API RESTful para o sistema FluxoMax - Ambiente com Hot-Reloading',
    )
    .setVersion('1.0')
    .addTag('auth', 'Endpoints de autenticação')
    .addTag('users', 'Endpoints de usuários')
    .addTag('roles', 'Endpoints de papéis')
    .addTag(
      'user-company-roles',
      'Endpoints de associações entre usuários, empresas e papéis',
    )
    .addTag('accounts-receivable', 'Endpoints de contas a receber. NOTA: Para receber um pagamento, crie uma Transaction do tipo "income" vinculada à conta a receber através do campo "accountsReceivableId".')
    .addTag('accounts-payable', 'Endpoints de contas a pagar. NOTA: Para efetuar um pagamento, crie uma Transaction do tipo "expense" vinculada à conta a pagar através do campo "accountsPayableId".')
    .addTag('bank-accounts', 'Endpoints de contas bancárias')
    .addTag('banks', 'Endpoints de bancos')
    .addTag('transactions', 'Endpoints de transações financeiras. Use para registrar pagamentos e recebimentos.')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth', // Nome da definição de segurança (pode ser qualquer string)
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Configurar shutdown hooks do Prisma
  const prismaService = app.get(PrismaService);
  await prismaService.enableShutdownHooks(app);

  await app.listen(3000);
  // Logs de inicialização removidos por segurança em produção
}
bootstrap();
