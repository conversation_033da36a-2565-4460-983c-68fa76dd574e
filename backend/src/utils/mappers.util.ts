import { UserStatus, UserDto, UserProfileDto } from '../models/user.model';
import { CompanyDto } from '../models/company.model';
import { nullToUndefined, UserRawResult, CompanyRawResult, RoleRawResult } from './sql-transforms.util';
import { RoleDto } from '../models/role.model';

/**
 * Funções de mapeamento para transformar dados brutos SQL em DTOs
 */

/**
 * Mapeia resultados brutos SQL de usuário para UserDto
 */
export function mapToUserDto(user: UserRawResult): UserDto {
  return {
    id: user.id,
    email: user.email,
    status: user.status as UserStatus,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
    profile: user.username
      ? {
          id: user.id,
          username: user.username ?? '',
          firstName: user.first_name ?? '',
          lastName: user.last_name ?? '',
          phone: user.phone ?? '',
          avatarUrl: user.avatar_url ?? '',
          preferences: user.preferences ?? {},
          isActive: user.is_active ?? false,
        }
      : undefined,
  };
}

/**
 * Mapeia resultados brutos SQL de usuário para UserProfileDto
 */
export function mapToUserProfileDto(user: UserRawResult): UserProfileDto {
  return {
    id: user.id,
    email: user.email,
    status: user.status as UserStatus,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
    profile: {
      id: user.id,
      username: user.username ?? '',
      firstName: user.first_name ?? '',
      lastName: user.last_name ?? '',
      phone: user.phone ?? '',
      avatarUrl: user.avatar_url ?? '',
      preferences: user.preferences ?? {},
      isActive: user.is_active ?? false,
    },
  };
}

/**
 * Mapeia resultados brutos SQL de empresa para CompanyDto
 */
export function mapToCompanyDto(company: CompanyRawResult): CompanyDto {
  return {
    id: company.id,
    name: company.name,
    cnpj: company.cnpj,
    phone: nullToUndefined(company.phone),
    email: nullToUndefined(company.email),
    addressId: nullToUndefined(company.address_id),
    logo: nullToUndefined(company.logo),
    active: company.active || false,
    calendarType: company.calendar_type || '',
    createdAt: company.created_at || new Date(),
    updatedAt: company.updated_at || new Date(),
    deletedAt: nullToUndefined(company.deleted_at),
  };
}

/**
 * Mapeia resultados brutos SQL de papel para RoleDto
 */
export function mapToRoleDto(role: RoleRawResult): RoleDto {
  return {
    id: role.id,
    companyId: role.company_id,
    name: role.name,
    description: role.description ?? undefined,
    isSystemRole: role.is_system_role,
    createdAt: role.created_at,
    updatedAt: role.updated_at,
  };
} 