# syntax=docker/dockerfile:1

# Stage 1: Build Stage
FROM node:22.13.1-slim AS builder

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY --link package.json package-lock.json ./
RUN --mount=type=cache,target=/root/.npm npm ci

# Copy application source code
COPY --link . .

# Install additional dependencies
RUN apt-get update && apt-get install -y python3 make g++ openssl
RUN npm rebuild bcrypt --build-from-source

# Regenerate Prisma Client for the current platform
RUN npx prisma generate

# Build the application
RUN npm run build

# Stage 2: Production Stage
FROM node:22.13.1-slim AS production

# Set working directory
WORKDIR /app

# Install OpenSSL for Prisma
RUN apt-get update && apt-get install -y openssl

# Copy built application and dependencies from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Expose application port
EXPOSE 3000

# Run the application
CMD ["node", "dist/main.js"]