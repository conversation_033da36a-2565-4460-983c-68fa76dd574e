import * as fs from 'fs';
import * as path from 'path';

/**
 * Script para copiar arquivos SQL para o diretório dist/scripts durante o processo de build
 */
function copySqlFiles() {
  const sourceDir = path.join(__dirname, '../src/scripts');
  const targetDir = path.join(__dirname, '../dist/scripts');

  // Criar diretório de destino se não existir
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
    // Diretório criado - log removido por segurança
  }

  try {
    // Ler todos os arquivos do diretório de origem
    const files = fs.readdirSync(sourceDir);
    
    // Filtrar apenas arquivos SQL
    const sqlFiles = files.filter(file => file.endsWith('.sql'));
    
    // Copiar cada arquivo SQL para o diretório de destino
    sqlFiles.forEach(file => {
      const sourcePath = path.join(sourceDir, file);
      const targetPath = path.join(targetDir, file);

      fs.copyFileSync(sourcePath, targetPath);
      // Arquivo copiado - log removido por segurança
    });

    // Arquivos SQL copiados - log removido por segurança
  } catch (error) {
    // Erro ao copiar arquivos SQL - log removido por segurança
    process.exit(1);
  }
}

// Executar a função
copySqlFiles();
