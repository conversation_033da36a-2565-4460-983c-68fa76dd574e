/**
 * Script para exportar os dados do banco de dados para um arquivo de seed SQL
 *
 * Uso: node export-seed.js [--output=caminho/para/arquivo.sql]
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configurações
const DEFAULT_OUTPUT_PATH = path.join(__dirname, '../prisma/seed_exported.sql');
const EXCLUDED_TABLES = [
  '_prisma_migrations',
  'refresh_tokens',
  'transactions',
];
const TABLE_ORDER = [
  'address_types',
  'currencies',
  'payment_methods',
  'recurrence_types',
  'banks',
  'companies',
  'addresses',
  'roles',
  'users',
  'profiles',
  'user_company_roles',
  'categories',
  'entities',
  'bank_accounts',
  'accounts_payable',
  'accounts_receivable',
  'projects',
];

// Processar argumentos da linha de comando
const args = process.argv.slice(2);
let outputPath = DEFAULT_OUTPUT_PATH;

args.forEach((arg) => {
  if (arg.startsWith('--output=')) {
    outputPath = arg.split('=')[1];
  }
});

// Função para executar comandos SQL e capturar a saída
function execSQL(sql) {
  try {
    const isDocker =
      process.env.DOCKER === '1' || process.argv.includes('--docker');
    const command = isDocker
      ? `psql $DATABASE_URL -c "${sql}"`
      : `docker compose exec -T database psql -U postgres -d fluxomax -c "${sql}"`;

    return execSync(command, { encoding: 'utf8' });
  } catch (error) {
    // Erro ao executar SQL - falha silenciosa
    process.exit(1);
  }
}

// Obter lista de tabelas no banco de dados
function getTables() {
  const sql = `SELECT table_name FROM information_schema.tables 
               WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
               ORDER BY table_name`;

  const result = execSQL(sql);

  // Extrair nomes das tabelas da saída do psql
  const tables = result
    .split('\n')
    .slice(2, -3) // Remover cabeçalho e rodapé
    .map((line) => line.trim())
    .filter((table) => !EXCLUDED_TABLES.includes(table));

  // Ordenar tabelas conforme a ordem definida
  return tables.sort((a, b) => {
    const indexA = TABLE_ORDER.indexOf(a);
    const indexB = TABLE_ORDER.indexOf(b);

    if (indexA === -1 && indexB === -1) return a.localeCompare(b);
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;

    return indexA - indexB;
  });
}

// Gerar script SQL para uma tabela
function generateTableScript(table) {
  console.log(`Processando tabela: ${table}`);

  // Obter colunas da tabela
  const columnsSQL = `SELECT column_name, data_type 
                     FROM information_schema.columns 
                     WHERE table_schema = 'public' AND table_name = '${table}'
                     ORDER BY ordinal_position`;

  const columnsResult = execSQL(columnsSQL);
  const columns = columnsResult
    .split('\n')
    .slice(2, -3) // Remover cabeçalho e rodapé
    .map((line) => {
      const [name, type] = line
        .trim()
        .split('|')
        .map((s) => s.trim());
      return { name, type };
    });

  // Obter dados da tabela
  const dataSQL = `SELECT * FROM "${table}" LIMIT 1000`;
  const dataResult = execSQL(dataSQL);

  // Se não houver dados, retornar comentário
  if (dataResult.includes('(0 rows)')) {
    return `-- Tabela ${table} não possui dados\n\n`;
  }

  // Extrair dados da saída do psql
  const lines = dataResult.split('\n');
  const headerLine = lines[0];
  const dataLines = lines.slice(2, -2); // Remover cabeçalho e rodapé

  if (dataLines.length === 0) {
    return `-- Tabela ${table} não possui dados\n\n`;
  }

  // Gerar script INSERT
  let script = `-- Inserir dados na tabela ${table}\n`;

  // Usar DO $$ BEGIN ... END $$ para tabelas com relações complexas
  if (
    [
      'companies',
      'addresses',
      'users',
      'profiles',
      'roles',
      'user_company_roles',
      'entities',
      'bank_accounts',
      'accounts_payable',
      'accounts_receivable',
    ].includes(table)
  ) {
    script += `DO $$\nBEGIN\n`;
  }

  // Gerar INSERT para cada linha
  dataLines.forEach((line) => {
    const values = line.split('|').map((v) => v.trim());

    // Formatar valores conforme o tipo de dados
    const formattedValues = columns.map((col, i) => {
      const value = values[i];

      if (value === null || value === '' || value === 'NULL') {
        return 'NULL';
      }

      if (col.type.includes('timestamp')) {
        if (value.toLowerCase() === 'now()') {
          return 'NOW()';
        }
        return `'${value}'::timestamp`;
      }

      if (col.type.includes('uuid')) {
        return `'${value}'::uuid`;
      }

      if (
        col.type.includes('int') ||
        col.type.includes('numeric') ||
        col.type.includes('decimal')
      ) {
        return value;
      }

      if (col.type.includes('boolean')) {
        return value.toLowerCase();
      }

      // String e outros tipos
      return `'${value.replace(/'/g, "''")}'`;
    });

    // Gerar INSERT statement
    script += `INSERT INTO "${table}" (${columns.map((c) => `"${c.name}"`).join(', ')})\n`;
    script += `VALUES (${formattedValues.join(', ')})`;

    // Adicionar ON CONFLICT para tabelas com chaves únicas
    if (
      [
        'users',
        'companies',
        'address_types',
        'currencies',
        'payment_methods',
        'recurrence_types',
        'banks',
      ].includes(table)
    ) {
      script += `\nON CONFLICT DO NOTHING`;
    }

    script += `;\n\n`;
  });

  // Fechar bloco DO para tabelas com relações complexas
  if (
    [
      'companies',
      'addresses',
      'users',
      'profiles',
      'roles',
      'user_company_roles',
      'entities',
      'bank_accounts',
      'accounts_payable',
      'accounts_receivable',
    ].includes(table)
  ) {
    script += `END;\n$$;\n\n`;
  }

  return script;
}

// Função principal
async function main() {
  // Exportando dados - logs removidos por segurança

  // Obter lista de tabelas
  const tables = getTables();
  // Tabelas encontradas - log removido por segurança

  // Gerar cabeçalho do script
  let script = `-- Script de seed gerado automaticamente em ${new Date().toISOString()}\n\n`;

  // Adicionar scripts para cada tabela
  for (const table of tables) {
    script += generateTableScript(table);
  }

  // Adicionar verificação final
  script += `-- Verificar se os dados foram inseridos corretamente\n`;
  script += `DO $$\n`;
  script += `DECLARE\n`;
  script += `    company_count INTEGER;\n`;
  script += `    user_count INTEGER;\n`;
  script += `    profile_count INTEGER;\n`;
  script += `BEGIN\n`;
  script += `    SELECT COUNT(*) INTO company_count FROM companies;\n`;
  script += `    SELECT COUNT(*) INTO user_count FROM users;\n`;
  script += `    SELECT COUNT(*) INTO profile_count FROM profiles;\n`;
  script += `    \n`;
  script += `    RAISE NOTICE 'Total de empresas: %', company_count;\n`;
  script += `    RAISE NOTICE 'Total de usuários: %', user_count;\n`;
  script += `    RAISE NOTICE 'Total de perfis: %', profile_count;\n`;
  script += `    \n`;
  script += `    IF user_count > profile_count THEN\n`;
  script += `        RAISE NOTICE 'Usuários sem perfil: %', user_count - profile_count;\n`;
  script += `    ELSE\n`;
  script += `        RAISE NOTICE 'Todos os usuários possuem perfil. Seed concluído com sucesso!';\n`;
  script += `    END IF;\n`;
  script += `END;\n`;
  script += `$$;\n`;

  // Salvar script no arquivo
  fs.writeFileSync(outputPath, script);

  // Script de seed gerado - log removido por segurança
}

main().catch((error) => {
  // Erro na execução - falha silenciosa
  process.exit(1);
});
