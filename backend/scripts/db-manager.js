#!/usr/bin/env node
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Configurações
const MIGRATIONS_DIR = path.join(__dirname, '../prisma/migrations');
const SCHEMA_PATH = path.join(__dirname, '../prisma/schema.prisma');
const BACKUP_DIR = path.join(__dirname, '../prisma/migrations_backup');

// Função para executar comandos
function executeCommand(command, options = {}) {
  console.log(`Executando: ${command}`);
  try {
    return execSync(command, { stdio: 'inherit', ...options });
  } catch (error) {
    if (options.ignoreError) {
      console.warn(`Comando falhou, mas continuando: ${error.message}`);
      return null;
    }
    console.error(`Erro ao executar comando: ${error.message}`);
    process.exit(1);
  }
}

// Função para executar comandos no Docker
function executeDockerCommand(command, options = {}) {
  console.log('Executando comando no Docker...');

  // Determinar o diretório raiz do projeto
  const fs = require('fs');
  const path = require('path');

  // Verificar se estamos no diretório backend ou no diretório raiz
  const isInBackendDir =
    process.cwd().endsWith('/backend') || process.cwd().endsWith('\\backend');

  // Se estamos no diretório backend, precisamos voltar um nível
  const rootDir = isInBackendDir ? '..' : '.';

  console.log(`Diretório atual: ${process.cwd()}`);
  console.log(
    `Diretório raiz do projeto: ${path.resolve(process.cwd(), rootDir)}`,
  );

  // Mudar para o diretório raiz do projeto para executar os comandos docker
  const originalDir = process.cwd();
  if (isInBackendDir) {
    try {
      process.chdir(rootDir);
      // Mudança de diretório - log removido por segurança
    } catch (e) {
      // Não foi possível mudar para o diretório raiz - falha silenciosa
    }
  }

  try {
    // Tenta primeiro com docker compose
    try {
      const result = executeCommand(
        `docker compose exec backend ${command}`,
        options,
      );
      // Voltar para o diretório original
      if (isInBackendDir) process.chdir(originalDir);
      return result;
    } catch (composeError) {
      // Falha ao usar docker compose, tentando com docker-compose - logs removidos

      // Tenta com docker-compose
      try {
        const result = executeCommand(
          `docker-compose exec backend ${command}`,
          options,
        );
        // Voltar para o diretório original
        if (isInBackendDir) process.chdir(originalDir);
        return result;
      } catch (dockerComposeError) {
        console.warn(
          'Falha ao usar docker-compose, tentando com docker exec...',
        );
        console.warn(`Erro: ${dockerComposeError.message}`);

        // Tenta com docker exec diretamente
        try {
          const result = executeCommand(
            `docker exec fluxo-max-backend-1 ${command}`,
            options,
          );
          // Voltar para o diretório original
          if (isInBackendDir) process.chdir(originalDir);
          return result;
        } catch (dockerExecError) {
          console.warn(
            'Falha ao usar docker exec, tentando com docker exec -it...',
          );
          console.warn(`Erro: ${dockerExecError.message}`);

          // Última tentativa com docker exec -it
          const result = executeCommand(
            `docker exec -it fluxo-max-backend-1 ${command}`,
            options,
          );
          // Voltar para o diretório original
          if (isInBackendDir) process.chdir(originalDir);
          return result;
        }
      }
    }
  } catch (error) {
    // Garantir que voltamos para o diretório original em caso de erro
    if (isInBackendDir) process.chdir(originalDir);
    throw error;
  }
}

// Função para confirmar ação
function confirmAction(message) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(`${message} (digite "SIM" para confirmar): `, (answer) => {
      rl.close();
      resolve(answer.trim() === 'SIM');
    });
  });
}

// Comandos disponíveis
const commands = {
  // Comando para aplicar triggers RBAC
  async applyRbacTriggers(useDocker = false) {
    // Aplicando triggers RBAC - logs removidos por segurança
    const triggerPath = path.join(
      __dirname,
      '../prisma/migrations/20250407_add_rbac_triggers.sql',
    );

    // Verificar se o arquivo existe
    if (!fs.existsSync(triggerPath)) {
      // Arquivo de triggers não encontrado - falha silenciosa
      process.exit(1);
    }

    // Executar o arquivo SQL
    const cmd = `psql $DATABASE_URL -f ${triggerPath}`;

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    // Triggers RBAC aplicados - log removido por segurança
  },
  // Comando para exportar o seed
  async exportSeed(useDocker = false) {
    console.log('🌱 Exportando dados atuais para script de seed...');
    const scriptPath = path.join(__dirname, 'export-seed.sh');

    // Garantir que o script é executável
    fs.chmodSync(scriptPath, '755');

    if (useDocker) {
      executeDockerCommand(`bash ${scriptPath} --docker`);
    } else {
      executeCommand(`bash ${scriptPath}`);
    }
  },

  // Comando para resetar o banco de dados
  async reset(useDocker = false) {
    console.log('🔄 Resetando o banco de dados...');
    const cmd = 'npx prisma migrate reset --force';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Banco de dados resetado com sucesso!');
  },

  // Comando para consolidar migrações
  async squash() {
    console.log('🔄 Consolidando migrações...');

    // Verificar se o diretório de migrações existe
    if (!fs.existsSync(MIGRATIONS_DIR)) {
      console.error('❌ Diretório de migrações não encontrado!');
      process.exit(1);
    }

    // Backup das migrações existentes
    console.log('📦 Criando backup das migrações existentes...');
    if (fs.existsSync(BACKUP_DIR)) {
      fs.rmSync(BACKUP_DIR, { recursive: true, force: true });
    }
    fs.mkdirSync(BACKUP_DIR, { recursive: true });

    // Copiar migrações existentes para o backup
    const migrations = fs
      .readdirSync(MIGRATIONS_DIR)
      .filter(
        (dir) =>
          !dir.startsWith('.') &&
          fs.statSync(path.join(MIGRATIONS_DIR, dir)).isDirectory(),
      );

    if (migrations.length === 0) {
      console.log('⚠️ Nenhuma migração encontrada para consolidar.');
      return;
    }

    migrations.forEach((migration) => {
      const srcDir = path.join(MIGRATIONS_DIR, migration);
      const destDir = path.join(BACKUP_DIR, migration);
      fs.mkdirSync(destDir, { recursive: true });

      fs.readdirSync(srcDir).forEach((file) => {
        fs.copyFileSync(path.join(srcDir, file), path.join(destDir, file));
      });
    });

    console.log(
      `✅ Backup de ${migrations.length} migrações criado em ${BACKUP_DIR}`,
    );

    // Remover migrações existentes
    console.log('🗑️ Removendo migrações existentes...');
    migrations.forEach((migration) => {
      fs.rmSync(path.join(MIGRATIONS_DIR, migration), {
        recursive: true,
        force: true,
      });
    });

    // Gerar SQL da migração consolidada - log removido por segurança
    try {
      // Usar prisma migrate para gerar o SQL
      const timestamp = new Date()
        .toISOString()
        .replace(/[-:]/g, '')
        .split('.')[0];
      const migrationName = `${timestamp}_initial_migration`;

      executeCommand(
        'npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > migration.sql',
      );

      // Criar diretório para a nova migração
      const newMigrationDir = path.join(MIGRATIONS_DIR, migrationName);
      fs.mkdirSync(newMigrationDir, { recursive: true });

      // Mover o arquivo SQL para o diretório da migração
      fs.renameSync(
        path.join(process.cwd(), 'migration.sql'),
        path.join(newMigrationDir, 'migration.sql'),
      );

      // Criar arquivo migration_lock.toml
      fs.writeFileSync(
        path.join(MIGRATIONS_DIR, 'migration_lock.toml'),
        'provider = "postgresql"\n',
      );

      console.log(`✅ Migração consolidada criada em ${newMigrationDir}`);
      console.log('🎉 Processo de consolidação concluído com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao gerar SQL da migração consolidada:', error);

      // Restaurar backup em caso de erro
      console.log('🔄 Restaurando backup das migrações...');
      migrations.forEach((migration) => {
        const srcDir = path.join(BACKUP_DIR, migration);
        const destDir = path.join(MIGRATIONS_DIR, migration);

        if (fs.existsSync(srcDir)) {
          fs.mkdirSync(destDir, { recursive: true });

          fs.readdirSync(srcDir).forEach((file) => {
            fs.copyFileSync(path.join(srcDir, file), path.join(destDir, file));
          });
        }
      });

      console.log('✅ Backup restaurado.');
      process.exit(1);
    }
  },

  // Comando para criar uma nova migração
  async create(name, useDocker = false) {
    if (!name) {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      name = await new Promise((resolve) => {
        rl.question('Digite o nome para a nova migração: ', (answer) => {
          rl.close();
          resolve(answer.trim().replace(/\s+/g, '_').toLowerCase());
        });
      });

      if (!name) {
        console.error('❌ Nome da migração é obrigatório!');
        process.exit(1);
      }
    }

    console.log(`🔄 Criando nova migração: ${name}...`);
    const cmd = `npx prisma migrate dev --create-only --name ${name}`;

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Migração criada com sucesso!');
    console.log(
      '⚠️ Lembre-se de revisar o arquivo SQL gerado antes de aplicar a migração.',
    );
  },

  // Comando para aplicar migrações pendentes
  async deploy(useDocker = false) {
    console.log('🔄 Aplicando migrações pendentes...');
    const cmd = 'npx prisma migrate deploy';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('🔄 Gerando cliente Prisma...');
    const generateCmd = 'npx prisma generate';

    if (useDocker) {
      executeDockerCommand(generateCmd);
    } else {
      executeCommand(generateCmd);
    }

    console.log('✅ Migrações aplicadas com sucesso!');
  },

  // Comando para executar o seeding do banco de dados
  async seed(useDocker = false) {
    console.log('🌱 Executando seeding do banco de dados...');
    const cmd = 'npx prisma db seed';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Seeding concluído com sucesso!');
  },

  // Comando para verificar o status das migrações
  async status(useDocker = false) {
    console.log('🔍 Verificando status das migrações...');
    const cmd = 'npx prisma migrate status';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Verificação concluída!');
  },

  // Comando para forçar a atualização do banco de dados
  async pushForce(useDocker = false) {
    const confirmed = await confirmAction(
      '⚠️ ATENÇÃO: Este comando irá forçar a atualização do banco de dados para corresponder ao schema.prisma.\n⚠️ Isso pode resultar em PERDA DE DADOS se houver alterações incompatíveis.',
    );

    if (!confirmed) {
      console.log('❌ Operação cancelada pelo usuário.');
      return;
    }

    console.log('🔄 Forçando atualização do banco de dados...');
    const cmd = 'npx prisma db push --accept-data-loss';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('🔄 Gerando cliente Prisma...');
    const generateCmd = 'npx prisma generate';

    if (useDocker) {
      executeDockerCommand(generateCmd);
    } else {
      executeCommand(generateCmd);
    }

    console.log('✅ Banco de dados atualizado com sucesso!');
  },

  // Comando para atualizar o schema.prisma com base no banco de dados
  async introspect(useDocker = false) {
    const confirmed = await confirmAction(
      '⚠️ ATENÇÃO: Este comando irá atualizar o schema.prisma com base na estrutura atual do banco de dados.\n⚠️ Isso pode sobrescrever alterações manuais feitas no schema.prisma.',
    );

    if (!confirmed) {
      console.log('❌ Operação cancelada pelo usuário.');
      return;
    }

    // Fazer backup do schema.prisma atual
    const backupPath = `${SCHEMA_PATH}.backup`;
    console.log('📦 Criando backup do schema.prisma atual...');
    fs.copyFileSync(SCHEMA_PATH, backupPath);

    console.log('🔄 Executando introspection do banco de dados...');
    const cmd = 'npx prisma db pull';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Schema atualizado com sucesso!');
    console.log(`📝 Backup do schema anterior salvo em: ${backupPath}`);
  },

  // Comando para gerar o cliente Prisma
  async generate(useDocker = false) {
    console.log('🔄 Gerando cliente Prisma...');
    const cmd = 'npx prisma generate';

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Cliente Prisma gerado com sucesso!');
  },

  // Comando para executar um comando Prisma personalizado
  async custom(args, useDocker = false) {
    if (args.length === 0) {
      console.error('❌ Nenhum comando especificado!');
      console.log('Uso: db-manager custom <comando-prisma>');
      console.log(
        'Exemplo: db-manager custom migrate dev --name add_user_field',
      );
      process.exit(1);
    }

    const cmd = `npx prisma ${args.join(' ')}`;
    console.log(`🔄 Executando comando personalizado: ${cmd}`);

    if (useDocker) {
      executeDockerCommand(cmd);
    } else {
      executeCommand(cmd);
    }

    console.log('✅ Comando executado com sucesso!');
  },

  // Comando para mostrar ajuda
  help() {
    console.log('Gerenciador de Banco de Dados com Prisma');
    console.log('');
    console.log('Uso: db-manager <comando> [opções]');
    console.log('');
    console.log('Comandos:');
    console.log(
      '  reset                  Resetar o banco de dados (apaga todos os dados)',
    );
    console.log(
      '  squash                 Consolidar todas as migrações em uma única',
    );
    console.log('  create <nome>          Criar uma nova migração');
    console.log('  deploy                 Aplicar migrações pendentes');
    console.log(
      '  seed                   Executar o seeding do banco de dados',
    );
    console.log('  status                 Verificar o status das migrações');
    console.log(
      '  push-force             Forçar atualização do banco de dados (aceita perda de dados)',
    );
    console.log(
      '  introspect             Atualizar schema.prisma com base no banco de dados',
    );
    console.log('  generate               Gerar cliente Prisma');
    console.log(
      '  apply-rbac-triggers    Aplicar triggers RBAC ao banco de dados',
    );
    console.log(
      '  custom <comando>       Executar um comando Prisma personalizado',
    );
    console.log('  help                   Mostrar esta ajuda');
    console.log('');
    console.log('Opções:');
    console.log('  --docker               Executar o comando no Docker');
    console.log('');
    console.log('Exemplos:');
    console.log('  db-manager reset');
    console.log('  db-manager create add_user_field');
    console.log('  db-manager deploy --docker');
    console.log('  db-manager custom migrate dev --name add_user_field');
  },
};

// Função principal
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    commands.help();
    return;
  }

  // Verificar se a opção --docker está presente em qualquer posição
  const useDocker = args.includes('--docker');

  // Remover a opção --docker dos argumentos
  const filteredArgs = args.filter((arg) => arg !== '--docker');

  const command = filteredArgs[0];
  const commandArgs = filteredArgs.slice(1);

  switch (command) {
    case 'reset':
      await commands.reset(useDocker);
      break;
    case 'squash':
      await commands.squash();
      break;
    case 'create':
      await commands.create(commandArgs[0], useDocker);
      break;
    case 'deploy':
      await commands.deploy(useDocker);
      break;
    case 'seed':
      await commands.seed(useDocker);
      break;
    case 'export-seed':
      await commands.exportSeed(useDocker);
      break;
    case 'status':
      await commands.status(useDocker);
      break;
    case 'push-force':
      await commands.pushForce(useDocker);
      break;
    case 'introspect':
      await commands.introspect(useDocker);
      break;
    case 'generate':
      await commands.generate(useDocker);
      break;
    case 'apply-rbac-triggers':
      await commands.applyRbacTriggers(useDocker);
      break;
    case 'custom':
      await commands.custom(commandArgs, useDocker);
      break;
    case 'help':
      commands.help();
      break;
    default:
      console.error(`❌ Comando desconhecido: ${command}`);
      commands.help();
      process.exit(1);
  }
}

// Executar função principal
main().catch((error) => {
  console.error('❌ Erro inesperado:', error);
  process.exit(1);
});
