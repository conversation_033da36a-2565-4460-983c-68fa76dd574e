#!/bin/bash
set -e

echo "🔄 Resetando o banco de dados..."

# Limpar todas as tabelas que dependem de bank_accounts primeiro
echo "Limpando dados das tabelas dependentes..."
psql $DATABASE_URL -c "TRUNCATE TABLE transactions CASCADE;"
psql $DATABASE_URL -c "TRUNCATE TABLE accounts_payable CASCADE;"
psql $DATABASE_URL -c "TRUNCATE TABLE accounts_receivable CASCADE;"
psql $DATABASE_URL -c "TRUNCATE TABLE recurring_schedules CASCADE;"

# Limpar tabela de contas bancárias
echo "Limpando contas bancárias..."
psql $DATABASE_URL -c "TRUNCATE TABLE bank_accounts CASCADE;"

echo "🌱 Executando seed corrigido..."
# Executar o seed corrigido
psql $DATABASE_URL -f ./prisma/seed.sql

echo "✅ Verificando se as contas bancárias foram criadas..."
BANK_ACCOUNTS_COUNT=$(psql $DATABASE_URL -t -c "SELECT COUNT(*) FROM bank_accounts;")
BANK_ACCOUNTS_COUNT=$(echo $BANK_ACCOUNTS_COUNT | xargs)

if [ "$BANK_ACCOUNTS_COUNT" -gt "0" ]; then
    echo "✅ Sucesso! $BANK_ACCOUNTS_COUNT contas bancárias foram criadas."
    echo "📊 Detalhes das contas bancárias criadas:"
    psql $DATABASE_URL -c "SELECT name, account_number, account_type, initial_balance, current_balance FROM bank_accounts ORDER BY created_at;"
else
    echo "❌ Erro: Nenhuma conta bancária foi criada."
    exit 1
fi

echo "🎉 Seed do banco de dados concluído com sucesso!" 