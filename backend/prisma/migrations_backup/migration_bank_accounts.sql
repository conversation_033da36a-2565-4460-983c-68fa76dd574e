-- Migração para nova estrutura de bank_accounts
-- Script para reestruturar a tabela de contas bancárias

-- Se a aplicação está em desenvolvimento, podemos recriar a tabela
DROP TABLE IF EXISTS public.bank_accounts CASCADE;

-- Recriar a tabela com a nova estrutura
CREATE TABLE IF NOT EXISTS public.bank_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    bank_id UUID NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('corrente', 'poupanca', 'investimento', 'dinheiro', 'outro')),
    
    -- Campos de saldo reformulados
    initial_balance NUMERIC NOT NULL DEFAULT 0,
    current_balance NUMERIC NOT NULL DEFAULT 0,
    is_initial_balance_locked BOOLEAN DEFAULT FALSE,
    
    balance_date TIMESTAMPTZ NOT NULL,
    credit_limit NUMERIC DEFAULT 0.00,
    first_transaction_at TIMESTAMPTZ NULL,
    currency_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_id) REFERENCES "banks"(id) ON DELETE RESTRICT,
    FOREIGN KEY (currency_id) REFERENCES "currencies"(id) ON DELETE RESTRICT
);

-- Recriar índices
CREATE INDEX IF NOT EXISTS idx_bank_accounts_company_id ON public.bank_accounts(company_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_deleted_at ON public.bank_accounts(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_bank_accounts_company_balance 
ON public.bank_accounts(company_id, current_balance) 
WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_bank_accounts_locked_status 
ON public.bank_accounts(is_initial_balance_locked, first_transaction_at) 
WHERE deleted_at IS NULL;

-- Recriar constraints
ALTER TABLE public.bank_accounts 
ADD CONSTRAINT check_positive_balances 
CHECK (
    (current_balance >= 0 AND initial_balance >= 0) 
    OR account_type = 'investimento'
);

ALTER TABLE public.bank_accounts 
ADD CONSTRAINT check_initial_balance_consistency 
CHECK (
    NOT is_initial_balance_locked 
    OR first_transaction_at IS NOT NULL
);

-- Recriar trigger
CREATE TRIGGER update_bank_accounts_modtime
BEFORE UPDATE ON public.bank_accounts
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- Função para prevenir alteração do saldo inicial após bloqueio
CREATE OR REPLACE FUNCTION prevent_initial_balance_modification()
RETURNS TRIGGER AS $$
BEGIN
    -- Impede alteração do saldo inicial se estiver bloqueado
    IF OLD.is_initial_balance_locked = TRUE 
       AND NEW.initial_balance != OLD.initial_balance THEN
        RAISE EXCEPTION 'Cannot modify initial_balance after first transaction. Account ID: %', NEW.id;
    END IF;
    
    -- Atualiza o timestamp de updated_at
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para aplicar a proteção
CREATE OR REPLACE TRIGGER trigger_protect_initial_balance
    BEFORE UPDATE ON public.bank_accounts
    FOR EACH ROW
    EXECUTE FUNCTION prevent_initial_balance_modification();

-- Função para marcar primeira transação e bloquear saldo inicial
CREATE OR REPLACE FUNCTION lock_initial_balance(account_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.bank_accounts 
    SET 
        is_initial_balance_locked = TRUE,
        first_transaction_at = COALESCE(first_transaction_at, NOW()),
        updated_at = NOW()
    WHERE id = account_uuid 
      AND is_initial_balance_locked = FALSE;
      
    IF NOT FOUND THEN
        RAISE NOTICE 'Account % not found or already locked', account_uuid;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Recriar política de RLS
CREATE POLICY bank_accounts_access ON public.bank_accounts
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) AND deleted_at IS NULL);
    
CREATE POLICY bank_accounts_select ON public.bank_accounts FOR SELECT
    USING (deleted_at IS NULL AND company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

-- Habilitar RLS
ALTER TABLE public.bank_accounts ENABLE ROW LEVEL SECURITY; 