-- Adicionar coluna entity_id à tabela addresses se ela não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'addresses'
        AND column_name = 'entity_id'
    ) THEN
        ALTER TABLE addresses ADD COLUMN entity_id UUID REFERENCES entities(id) ON DELETE CASCADE;
        
        -- Adicionar índice para melhorar performance de consultas
        CREATE INDEX idx_addresses_entity_id ON addresses(entity_id);
        
        -- Adicionar constraint para garantir que um endereço pertence a uma empresa OU a uma entidade
        ALTER TABLE addresses ADD CONSTRAINT check_owner 
            CHECK ((company_id IS NOT NULL AND entity_id IS NULL) OR 
                  (company_id IS NULL AND entity_id IS NOT NULL) OR
                  (company_id IS NULL AND entity_id IS NULL));
    END IF;
END $$;
