-- Script DML completo para popular as tabelas do FluxoMax
-- Criado em: 06/04/2025

-- Limpar dados existentes (opcional, remova se quiser preservar dados)
-- TRUNCATE TABLE user_company_roles CASCADE;
-- TRUNCATE TABLE refresh_tokens CASCADE;
-- TRUNCATE TABLE profiles CASCADE;
-- TRUNCATE TABLE users CASCADE;
-- TRUNCATE TABLE roles CASCADE;
-- TRUNCATE TABLE companies CASCADE;

-- =============================================
-- INSERÇÃO DE DADOS BÁSICOS (TABELAS DE REFERÊNCIA)
-- =============================================

-- Inserir Tipos de Endereço
INSERT INTO address_types (id, name, description)
VALUES
(gen_random_uuid(), 'Comercial', 'Endereço comercial'),
(gen_random_uuid(), 'Residencial', 'Endereço residencial'),
(gen_random_uuid(), 'Entrega', 'Endereço de entrega'),
(gen_random_uuid(), 'Faturamento', 'Endereço de faturamento'),
(gen_random_uuid(), 'Outro', 'Outro tipo de endereço')
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description;

-- Inserir Moedas Padrão
INSERT INTO currencies (id, code, name, symbol, decimal_places, is_default, created_at, updated_at)
VALUES
(gen_random_uuid(), 'BRL', 'Real Brasileiro', 'R$', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'USD', 'Dólar Americano', '$', 2, false, NOW(), NOW()),
(gen_random_uuid(), 'EUR', 'Euro', '€', 2, false, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    symbol = EXCLUDED.symbol,
    updated_at = NOW();

-- Inserir Métodos de Pagamento
INSERT INTO payment_methods (id, name, description, created_at, updated_at)
VALUES
(gen_random_uuid(), 'dinheiro', 'Pagamento em dinheiro', NOW(), NOW()),
(gen_random_uuid(), 'pix', 'Transferência via PIX', NOW(), NOW()),
(gen_random_uuid(), 'cartao_credito', 'Pagamento com cartão de crédito', NOW(), NOW()),
(gen_random_uuid(), 'cartao_debito', 'Pagamento com cartão de débito', NOW(), NOW()),
(gen_random_uuid(), 'boleto', 'Pagamento via boleto bancário', NOW(), NOW()),
(gen_random_uuid(), 'transferencia', 'Transferência bancária', NOW(), NOW()),
(gen_random_uuid(), 'cheque', 'Pagamento com cheque', NOW(), NOW()),
(gen_random_uuid(), 'debito_automatico', 'Débito automático em conta', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Tipos de Recorrência
INSERT INTO recurrence_types (id, name, description, created_at, updated_at)
VALUES
(gen_random_uuid(), 'unico', 'Pagamento/recebimento único', NOW(), NOW()),
(gen_random_uuid(), 'diario', 'Recorrência diária', NOW(), NOW()),
(gen_random_uuid(), 'semanal', 'Recorrência semanal', NOW(), NOW()),
(gen_random_uuid(), 'quinzenal', 'Recorrência quinzenal', NOW(), NOW()),
(gen_random_uuid(), 'mensal', 'Recorrência mensal', NOW(), NOW()),
(gen_random_uuid(), 'bimestral', 'Recorrência bimestral', NOW(), NOW()),
(gen_random_uuid(), 'trimestral', 'Recorrência trimestral', NOW(), NOW()),
(gen_random_uuid(), 'semestral', 'Recorrência semestral', NOW(), NOW()),
(gen_random_uuid(), 'anual', 'Recorrência anual', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Bancos
INSERT INTO banks (id, code, name, created_at, updated_at)
VALUES
(gen_random_uuid(), '001', 'Banco do Brasil S.A.', NOW(), NOW()),
(gen_random_uuid(), '237', 'Banco Bradesco S.A.', NOW(), NOW()),
(gen_random_uuid(), '341', 'Itaú Unibanco S.A.', NOW(), NOW()),
(gen_random_uuid(), '104', 'Caixa Econômica Federal', NOW(), NOW()),
(gen_random_uuid(), '033', 'Banco Santander (Brasil) S.A.', NOW(), NOW()),
(gen_random_uuid(), '260', 'Nu Pagamentos S.A. (Nubank)', NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    updated_at = NOW();

-- =============================================
-- INSERÇÃO DE DADOS PRINCIPAIS
-- =============================================

-- Inserir Empresas, Endereços, Usuários e Relacionamentos
DO $$
DECLARE
    -- Variáveis para IDs
    v_company_id1 UUID;
    v_company_id2 UUID;
    v_address_type_comercial UUID;
    v_address_id1 UUID;
    v_address_id2 UUID;
    v_user_id1 UUID;
    v_user_id2 UUID;
    v_role_admin_id1 UUID;
    v_role_member_id1 UUID;
    v_role_admin_id2 UUID;
    v_role_member_id2 UUID;
    v_currency_id_brl UUID;
    v_bank_id_itau UUID;
    v_bank_id_nubank UUID;
    v_bank_account_id_itau1 UUID;
    v_bank_account_id_nubank1 UUID;
    v_bank_account_id_itau2 UUID;
    v_payment_method_id_transfer UUID;
    v_entity_id_fornecedor1 UUID;
    v_entity_id_cliente1 UUID;
    v_entity_id_fornecedor2 UUID;
    v_entity_id_cliente2 UUID;
    v_category_id_despesas1 UUID;
    v_category_id_receitas1 UUID;
    v_category_id_despesas2 UUID;
    v_category_id_receitas2 UUID;
    v_project_id_website1 UUID;
    v_project_id_app1 UUID;
    v_project_id_erp2 UUID;
BEGIN
    -- Obter ID do tipo de endereço comercial
    SELECT id INTO v_address_type_comercial FROM address_types WHERE name = 'Comercial' LIMIT 1;

    -- Obter ID da moeda BRL
    SELECT id INTO v_currency_id_brl FROM currencies WHERE code = 'BRL' LIMIT 1;

    -- Obter ID do método de pagamento transferência
    SELECT id INTO v_payment_method_id_transfer FROM payment_methods WHERE name = 'transferencia' LIMIT 1;

    -- Obter IDs dos bancos
    SELECT id INTO v_bank_id_itau FROM banks WHERE code = '341' LIMIT 1;
    SELECT id INTO v_bank_id_nubank FROM banks WHERE code = '260' LIMIT 1;

    -- =============================================
    -- EMPRESA 1: FluxoMax Tecnologia
    -- =============================================

    -- Primeiro, criar um UUID para o endereço
    v_address_id1 := gen_random_uuid();

    -- Primeiro inserir o endereço
    INSERT INTO addresses (id, address_type_id, street, number, complement, district, city, state, zip_code, is_default, created_at, updated_at)
    VALUES (
        v_address_id1,
        v_address_type_comercial,
        'Av. Paulista',
        '1000',
        'Sala 1010',
        'Bela Vista',
        'São Paulo',
        'SP',
        '01310-100',
        true,
        NOW(),
        NOW()
    );

    -- Depois inserir a empresa com referência ao endereço já criado
    INSERT INTO companies (id, name, cnpj, phone, email, logo, active, calendar_type, address_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        'FluxoMax Tecnologia',
        '12.345.678/0001-90',
        '(11) 3456-7890',
        '<EMAIL>',
        '/logos/fluxomax.png',
        true,
        'standard',
        v_address_id1,
        NOW(),
        NOW()
    )
    ON CONFLICT (cnpj) DO UPDATE SET
        name = EXCLUDED.name,
        phone = EXCLUDED.phone,
        email = EXCLUDED.email,
        address_id = v_address_id1,
        updated_at = NOW()
    RETURNING id INTO v_company_id1;

    -- Atualizar o endereço com a referência à empresa
    UPDATE addresses
    SET company_id = v_company_id1
    WHERE id = v_address_id1;

    -- Inserir papéis para a primeira empresa
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Admin',
        'Administrador com acesso total',
        NOW(),
        NOW()
    )
    ON CONFLICT (company_id, name) DO UPDATE SET
        description = EXCLUDED.description,
        updated_at = NOW()
    RETURNING id INTO v_role_admin_id1;

    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Member',
        'Membro com acesso limitado',
        NOW(),
        NOW()
    )
    ON CONFLICT (company_id, name) DO UPDATE SET
        description = EXCLUDED.description,
        updated_at = NOW()
    RETURNING id INTO v_role_member_id1;

    -- Inserir usuário admin
    INSERT INTO users (id, email, password, status, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        '<EMAIL>',
        -- Senha: Admin123 (hash bcrypt)
        '$2b$10$oAA8dart1E8/RG.tzpmfueju/5As1woSxL/N4.NOTf36CKZhiZltu',
        'active',
        NOW(),
        NOW()
    )
    ON CONFLICT (email) DO UPDATE SET
        updated_at = NOW()
    RETURNING id INTO v_user_id1;

    -- Inserir perfil para o usuário admin
    INSERT INTO profiles (id, username, first_name, last_name, phone, avatar_url, is_active, created_at, updated_at)
    VALUES (
        v_user_id1,
        'admin',
        'Administrador',
        'Sistema',
        '(11) 98765-4321',
        '/avatars/admin.png',
        true,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        username = EXCLUDED.username,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = NOW();

    -- Associar usuário admin à empresa com papel Admin
    INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_user_id1,
        v_company_id1,
        v_role_admin_id1,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id, company_id, role_id) DO NOTHING;

    -- Inserir categorias para a primeira empresa
    INSERT INTO categories (id, company_id, name, transaction_type, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Despesas Administrativas',
        'payable',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_category_id_despesas1;

    INSERT INTO categories (id, company_id, name, transaction_type, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Receitas Operacionais',
        'receivable',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_category_id_receitas1;

    -- Inserir entidades para a primeira empresa
    INSERT INTO entities (id, company_id, name, type, cnpj, phone, contact, email, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Fornecedor Exemplo Ltda',
        'supplier',
        '98.765.432/0001-10',
        '(11) 3333-4444',
        'João Silva',
        '<EMAIL>',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_entity_id_fornecedor1;

    INSERT INTO entities (id, company_id, name, type, cnpj, phone, contact, email, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Cliente Exemplo S.A.',
        'customer',
        '11.222.333/0001-44',
        '(11) 5555-6666',
        'Maria Souza',
        '<EMAIL>',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_entity_id_cliente1;

    -- Inserir contas bancárias para a primeira empresa
    INSERT INTO bank_accounts (id, company_id, bank_id, account_number, account_type, initial_balance, current_balance, balance_date, currency_id, name, is_enabled, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        v_bank_id_itau,
        '12345-6',
        'corrente',
        15000.00,
        15000.00,
        NOW(),
        v_currency_id_brl,
        'Itaú Principal',
        true,
        NOW(),
        NOW()
    )
    RETURNING id INTO v_bank_account_id_itau1;

    INSERT INTO bank_accounts (id, company_id, bank_id, account_number, account_type, initial_balance, current_balance, balance_date, currency_id, name, is_enabled, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        v_bank_id_nubank,
        '9876543-2',
        'corrente',
        5000.00,
        5000.00,
        NOW(),
        v_currency_id_brl,
        'Nubank Pagamentos',
        true,
        NOW(),
        NOW()
    )
    RETURNING id INTO v_bank_account_id_nubank1;

    -- Inserir contas a pagar para a primeira empresa
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, payment_method_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Aluguel Escritório Abril',
        v_entity_id_fornecedor1,
        (date_trunc('month', current_date) + interval '1 month' - interval '1 day')::date + interval '5 days',
        5000.00,
        0.00,
        v_currency_id_brl,
        'pending',
        v_category_id_despesas1,
        v_payment_method_id_transfer,
        v_bank_account_id_itau1,
        NOW(),
        NOW()
    );

    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, invoice_number, bank_account_id, project_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Compra Notebook Dell XPS',
        v_entity_id_fornecedor1,
        current_date + interval '30 days',
        8500.50,
        0.00,
        v_currency_id_brl,
        'pending',
        v_category_id_despesas1,
        'NF-12345',
        v_bank_account_id_nubank1,
        v_project_id_app1,
        NOW(),
        NOW()
    );

    -- Inserir contas a receber para a primeira empresa
    INSERT INTO accounts_receivable (id, company_id, description, entity_id, due_date, amount, received_amount, currency_id, status, category_id, bank_account_id, project_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Serviço de Consultoria',
        v_entity_id_cliente1,
        current_date + interval '15 days',
        12000.00,
        0.00,
        v_currency_id_brl,
        'pending',
        v_category_id_receitas1,
        v_bank_account_id_itau1,
        v_project_id_website1,
        NOW(),
        NOW()
    );

    -- Inserir projetos para a primeira empresa
    INSERT INTO projects (id, company_id, name, description, budget, start_date, end_date, status, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Desenvolvimento Website',
        'Projeto de desenvolvimento do novo website corporativo',
        25000.00,
        current_date - interval '30 days',
        current_date + interval '60 days',
        'in_progress',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_project_id_website1;

    INSERT INTO projects (id, company_id, name, description, budget, start_date, end_date, status, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id1,
        'Aplicativo Mobile',
        'Desenvolvimento de aplicativo para iOS e Android',
        45000.00,
        current_date - interval '15 days',
        current_date + interval '120 days',
        'in_progress',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_project_id_app1;

    -- =============================================
    -- EMPRESA 2: Contabilidade Express
    -- =============================================

    -- Primeiro, criar um UUID para o endereço
    v_address_id2 := gen_random_uuid();

    -- Primeiro inserir o endereço
    INSERT INTO addresses (id, address_type_id, street, number, complement, district, city, state, zip_code, is_default, created_at, updated_at)
    VALUES (
        v_address_id2,
        v_address_type_comercial,
        'Rua Vergueiro',
        '500',
        'Conjunto 502',
        'Liberdade',
        'São Paulo',
        'SP',
        '01504-000',
        true,
        NOW(),
        NOW()
    );

    -- Depois inserir a empresa com referência ao endereço já criado
    INSERT INTO companies (id, name, cnpj, phone, email, logo, active, calendar_type, address_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        'Contabilidade Express',
        '98.765.432/0001-10',
        '(11) 9876-5432',
        '<EMAIL>',
        '/logos/contabilidade.png',
        true,
        'standard',
        v_address_id2,
        NOW(),
        NOW()
    )
    ON CONFLICT (cnpj) DO UPDATE SET
        name = EXCLUDED.name,
        phone = EXCLUDED.phone,
        email = EXCLUDED.email,
        address_id = v_address_id2,
        updated_at = NOW()
    RETURNING id INTO v_company_id2;

    -- Atualizar o endereço com a referência à empresa
    UPDATE addresses
    SET company_id = v_company_id2
    WHERE id = v_address_id2;

    -- Inserir papéis para a segunda empresa
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Admin',
        'Administrador com acesso total',
        NOW(),
        NOW()
    )
    ON CONFLICT (company_id, name) DO UPDATE SET
        description = EXCLUDED.description,
        updated_at = NOW()
    RETURNING id INTO v_role_admin_id2;

    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Member',
        'Membro com acesso limitado',
        NOW(),
        NOW()
    )
    ON CONFLICT (company_id, name) DO UPDATE SET
        description = EXCLUDED.description,
        updated_at = NOW()
    RETURNING id INTO v_role_member_id2;

    -- Inserir usuário regular
    INSERT INTO users (id, email, password, status, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        '<EMAIL>',
        -- Senha: User123 (hash bcrypt)
        '$2b$10$oAA8dart1E8/RG.tzpmfueju/5As1woSxL/N4.NOTf36CKZhiZltu',
        'active',
        NOW(),
        NOW()
    )
    ON CONFLICT (email) DO UPDATE SET
        updated_at = NOW()
    RETURNING id INTO v_user_id2;

    -- Inserir perfil para o usuário regular
    INSERT INTO profiles (id, username, first_name, last_name, phone, avatar_url, is_active, created_at, updated_at)
    VALUES (
        v_user_id2,
        'user',
        'Usuário',
        'Comum',
        '(11) 91234-5678',
        '/avatars/user.png',
        true,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        username = EXCLUDED.username,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = NOW();

    -- Associar usuário regular à segunda empresa com papel Member
    INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_user_id2,
        v_company_id2,
        v_role_member_id2,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id, company_id, role_id) DO NOTHING;

    -- Inserir categorias para a segunda empresa
    INSERT INTO categories (id, company_id, name, transaction_type, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Despesas Operacionais',
        'payable',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_category_id_despesas2;

    INSERT INTO categories (id, company_id, name, transaction_type, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Receitas de Serviços',
        'receivable',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_category_id_receitas2;

    -- Inserir entidades para a segunda empresa
    INSERT INTO entities (id, company_id, name, type, cnpj, phone, contact, email, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Fornecedor ABC Ltda',
        'supplier',
        '33.444.555/0001-66',
        '(11) 7777-8888',
        'Pedro Oliveira',
        '<EMAIL>',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_entity_id_fornecedor2;

    INSERT INTO entities (id, company_id, name, type, cnpj, phone, contact, email, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Cliente XYZ S.A.',
        'customer',
        '55.666.777/0001-88',
        '(11) 9999-0000',
        'Ana Santos',
        '<EMAIL>',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_entity_id_cliente2;

    -- Inserir projeto para a segunda empresa
    INSERT INTO projects (id, company_id, name, description, budget, start_date, end_date, status, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Implementação ERP',
        'Projeto de implementação de sistema ERP para gestão financeira',
        60000.00,
        current_date - interval '60 days',
        current_date + interval '90 days',
        'in_progress',
        NOW(),
        NOW()
    )
    RETURNING id INTO v_project_id_erp2;

    -- Inserir conta bancária para a segunda empresa
    INSERT INTO bank_accounts (id, company_id, bank_id, account_number, account_type, initial_balance, current_balance, balance_date, currency_id, name, is_enabled, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        v_bank_id_itau,
        '54321-0',
        'corrente',
        25000.00,
        25000.00,
        NOW(),
        v_currency_id_brl,
        'Itaú Empresarial',
        true,
        NOW(),
        NOW()
    )
    RETURNING id INTO v_bank_account_id_itau2;

    -- Inserir contas a pagar para a segunda empresa
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, payment_method_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Aluguel Sala Comercial',
        v_entity_id_fornecedor2,
        current_date + interval '10 days',
        3500.00,
        0.00,
        v_currency_id_brl,
        'pending',
        v_category_id_despesas2,
        v_payment_method_id_transfer,
        v_bank_account_id_itau2,
        NOW(),
        NOW()
    );

    -- Inserir contas a receber para a segunda empresa
    INSERT INTO accounts_receivable (id, company_id, description, entity_id, due_date, amount, received_amount, currency_id, status, category_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(),
        v_company_id2,
        'Serviço de Contabilidade Mensal',
        v_entity_id_cliente2,
        current_date + interval '20 days',
        4500.00,
        0.00,
        v_currency_id_brl,
        'pending',
        v_category_id_receitas2,
        v_bank_account_id_itau2,
        NOW(),
        NOW()
    );

END;
$$;

-- =============================================
-- INSERÇÃO DE DADOS RBAC (PERMISSÕES, PAPÉIS, ETC.)
-- =============================================

-- Inserir Permissões do Sistema (SystemPermissions)
INSERT INTO system_permissions (id, code, name, description, module, created_at, updated_at)
VALUES
-- Módulo de Empresas
(gen_random_uuid(), 'companies.view', 'Visualizar empresas', 'Permite visualizar dados da empresa', 'companies', NOW(), NOW()),
(gen_random_uuid(), 'companies.edit', 'Editar empresas', 'Permite editar dados da empresa', 'companies', NOW(), NOW()),

-- Módulo de Usuários
(gen_random_uuid(), 'users.view', 'Visualizar usuários', 'Permite visualizar usuários da empresa', 'users', NOW(), NOW()),
(gen_random_uuid(), 'users.create', 'Criar usuários', 'Permite criar novos usuários', 'users', NOW(), NOW()),
(gen_random_uuid(), 'users.edit', 'Editar usuários', 'Permite editar usuários existentes', 'users', NOW(), NOW()),
(gen_random_uuid(), 'users.delete', 'Remover usuários', 'Permite remover usuários', 'users', NOW(), NOW()),

-- Módulo de Papéis
(gen_random_uuid(), 'roles.view', 'Visualizar papéis', 'Permite visualizar papéis', 'roles', NOW(), NOW()),
(gen_random_uuid(), 'roles.create', 'Criar papéis', 'Permite criar novos papéis', 'roles', NOW(), NOW()),
(gen_random_uuid(), 'roles.edit', 'Editar papéis', 'Permite editar papéis existentes', 'roles', NOW(), NOW()),
(gen_random_uuid(), 'roles.delete', 'Remover papéis', 'Permite remover papéis', 'roles', NOW(), NOW()),

-- Módulo Financeiro - Contas a Pagar
(gen_random_uuid(), 'accounts_payable.view', 'Visualizar contas a pagar', 'Permite visualizar contas a pagar', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_payable.create', 'Criar contas a pagar', 'Permite criar contas a pagar', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_payable.edit', 'Editar contas a pagar', 'Permite editar contas a pagar', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_payable.delete', 'Remover contas a pagar', 'Permite remover contas a pagar', 'finance', NOW(), NOW()),

-- Módulo Financeiro - Contas a Receber
(gen_random_uuid(), 'accounts_receivable.view', 'Visualizar contas a receber', 'Permite visualizar contas a receber', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_receivable.create', 'Criar contas a receber', 'Permite criar contas a receber', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_receivable.edit', 'Editar contas a receber', 'Permite editar contas a receber', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'accounts_receivable.delete', 'Remover contas a receber', 'Permite remover contas a receber', 'finance', NOW(), NOW()),

-- Módulo Financeiro - Transações
(gen_random_uuid(), 'transactions.view', 'Visualizar transações', 'Permite visualizar transações', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'transactions.create', 'Criar transações', 'Permite criar transações', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'transactions.edit', 'Editar transações', 'Permite editar transações', 'finance', NOW(), NOW()),
(gen_random_uuid(), 'transactions.delete', 'Remover transações', 'Permite remover transações', 'finance', NOW(), NOW()),

-- Módulo de Relatórios
(gen_random_uuid(), 'reports.view', 'Visualizar relatórios', 'Permite visualizar relatórios', 'reports', NOW(), NOW()),
(gen_random_uuid(), 'reports.export', 'Exportar relatórios', 'Permite exportar relatórios', 'reports', NOW(), NOW())

ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    module = EXCLUDED.module,
    updated_at = NOW();

-- Atualizar os papéis existentes para incluir o campo isAdmin
UPDATE roles SET is_admin = true WHERE name = 'Admin' OR name = 'Administrador';
UPDATE roles SET is_admin = false WHERE name != 'Admin' AND name != 'Administrador';

-- Criar permissões e associações para as empresas existentes
DO $$
DECLARE
    v_company_record RECORD;
    v_system_permission_record RECORD;
    v_permission_id UUID;
    v_role_admin_id UUID;
    v_role_member_id UUID;
BEGIN
    -- Para cada empresa
    FOR v_company_record IN SELECT id FROM companies LOOP
        -- Para cada permissão do sistema, criar uma permissão da empresa
        FOR v_system_permission_record IN SELECT id, code, description FROM system_permissions LOOP
            -- Inserir a permissão para a empresa
            INSERT INTO permissions (
                id,
                company_id,
                action,
                description,
                system_permission_id,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                v_company_record.id,
                v_system_permission_record.code,
                v_system_permission_record.description,
                v_system_permission_record.id,
                NOW(),
                NOW()
            )
            ON CONFLICT DO NOTHING
            RETURNING id INTO v_permission_id;

            -- Obter o ID do papel Admin para esta empresa
            SELECT id INTO v_role_admin_id FROM roles
            WHERE company_id = v_company_record.id AND (name = 'Admin' OR name = 'Administrador')
            LIMIT 1;

            -- Se encontrou o papel Admin, associar todas as permissões a ele
            IF v_role_admin_id IS NOT NULL AND v_permission_id IS NOT NULL THEN
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES (v_role_admin_id, v_permission_id)
                ON CONFLICT DO NOTHING;
            END IF;

            -- Para o papel Member, associar apenas permissões de visualização
            IF v_system_permission_record.code LIKE '%.view' THEN
                -- Obter o ID do papel Member para esta empresa
                SELECT id INTO v_role_member_id FROM roles
                WHERE company_id = v_company_record.id AND name = 'Member'
                LIMIT 1;

                -- Se encontrou o papel Member, associar permissões de visualização a ele
                IF v_role_member_id IS NOT NULL AND v_permission_id IS NOT NULL THEN
                    INSERT INTO role_permissions (role_id, permission_id)
                    VALUES (v_role_member_id, v_permission_id)
                    ON CONFLICT DO NOTHING;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$;

-- Inserir Períodos Personalizados
DO $$
DECLARE
    v_company_id UUID;
    v_current_year INTEGER := EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER;
    v_next_year INTEGER := v_current_year + 1;
BEGIN
    -- Obter o ID da empresa FluxoMax Tecnologia
    SELECT id INTO v_company_id FROM companies WHERE name = 'FluxoMax Tecnologia';

    IF v_company_id IS NOT NULL THEN
        -- Inserir períodos personalizados para a empresa
        INSERT INTO custom_periods (id, name, start_date, end_date, company_id, created_at, updated_at)
        VALUES
        (gen_random_uuid(), 'Primeiro Trimestre ' || v_current_year,
         make_date(v_current_year, 1, 1), make_date(v_current_year, 3, 31),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Segundo Trimestre ' || v_current_year,
         make_date(v_current_year, 4, 1), make_date(v_current_year, 6, 30),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Terceiro Trimestre ' || v_current_year,
         make_date(v_current_year, 7, 1), make_date(v_current_year, 9, 30),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Quarto Trimestre ' || v_current_year,
         make_date(v_current_year, 10, 1), make_date(v_current_year, 12, 31),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Primeiro Semestre ' || v_current_year,
         make_date(v_current_year, 1, 1), make_date(v_current_year, 6, 30),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Segundo Semestre ' || v_current_year,
         make_date(v_current_year, 7, 1), make_date(v_current_year, 12, 31),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Ano Fiscal ' || v_current_year,
         make_date(v_current_year, 1, 1), make_date(v_current_year, 12, 31),
         v_company_id, NOW(), NOW()),

        (gen_random_uuid(), 'Planejamento ' || v_next_year,
         make_date(v_next_year, 1, 1), make_date(v_next_year, 12, 31),
         v_company_id, NOW(), NOW())
        ON CONFLICT DO NOTHING;
    END IF;
END;
$$;

-- Verificar se os dados foram inseridos corretamente
DO $$
DECLARE
    company_count INTEGER;
    user_count INTEGER;
    profile_count INTEGER;
    system_permission_count INTEGER;
    permission_count INTEGER;
    role_permission_count INTEGER;
    project_count INTEGER;
    custom_period_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO company_count FROM companies;
    SELECT COUNT(*) INTO user_count FROM users;
    SELECT COUNT(*) INTO profile_count FROM profiles;
    SELECT COUNT(*) INTO system_permission_count FROM system_permissions;
    SELECT COUNT(*) INTO permission_count FROM permissions;
    SELECT COUNT(*) INTO role_permission_count FROM role_permissions;
    SELECT COUNT(*) INTO project_count FROM projects;
    SELECT COUNT(*) INTO custom_period_count FROM custom_periods;

    RAISE NOTICE 'Total de empresas: %', company_count;
    RAISE NOTICE 'Total de usuários: %', user_count;
    RAISE NOTICE 'Total de perfis: %', profile_count;
    RAISE NOTICE 'Total de permissões do sistema: %', system_permission_count;
    RAISE NOTICE 'Total de permissões de empresas: %', permission_count;
    RAISE NOTICE 'Total de associações papel-permissão: %', role_permission_count;
    RAISE NOTICE 'Total de projetos: %', project_count;
    RAISE NOTICE 'Total de períodos personalizados: %', custom_period_count;

    IF user_count > profile_count THEN
        RAISE NOTICE 'Usuários sem perfil: %', user_count - profile_count;
    ELSE
        RAISE NOTICE 'Todos os usuários possuem perfil.';
    END IF;

    RAISE NOTICE 'Seed concluído com sucesso!';
END;
$$;
