-- Script DML completo para popular as tabelas do FluxoMax
-- Criado em: 02/04/2025

-- Limpar dados existentes (opcional, remova se quiser preservar dados)
-- TRUNCATE TABLE user_company_roles CASCADE;
-- TRUNCATE TABLE refresh_tokens CASCADE;
-- TRUNCATE TABLE profiles CASCADE;
-- TRUNCATE TABLE users CASCADE;
-- TRUNCATE TABLE roles CASCADE;
-- TRUNCATE TABLE companies CASCADE;

-- Inserir Tipos de Endereço primeiro (necessário antes de inserir endereços)
INSERT INTO address_types (id, name, description)
VALUES
(gen_random_uuid(), 'Comercial', 'Endereço comercial'),
(gen_random_uuid(), 'Residencial', 'Endereço residencial'),
(gen_random_uuid(), 'Entrega', 'Endereço de entrega'),
(gen_random_uuid(), 'Faturamento', 'Endereço de faturamento'),
(gen_random_uuid(), 'Outro', 'Outro tipo de endereço')
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description;

-- Primeiro inserir as empresas sem endereços, depois adicionar os endereços
DO $$
DECLARE
    v_company_id1 UUID;
    v_company_id2 UUID;
    v_address_type_comercial UUID;
    v_address_id1 UUID;
    v_address_id2 UUID;
BEGIN
    -- Obter ID do tipo de endereço comercial
    SELECT id INTO v_address_type_comercial FROM address_types WHERE name = 'Comercial' LIMIT 1;

    -- Inserir primeira empresa sem endereço inicialmente
    INSERT INTO companies (id, name, cnpj, phone, email, logo, active, calendar_type, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        'FluxoMax Tecnologia',
        '12.345.678/0001-90',
        '(11) 3456-7890',
        '<EMAIL>',
        '/logos/fluxomax.png',
        true,
        'standard',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE cnpj = '12.345.678/0001-90')
    RETURNING id INTO v_company_id1;

    -- Inserir segunda empresa sem endereço inicialmente
    INSERT INTO companies (id, name, cnpj, phone, email, logo, active, calendar_type, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        'Contabilidade Express',
        '98.765.432/0001-10',
        '(11) 9876-5432',
        '<EMAIL>',
        '/logos/contabilidade.png',
        true,
        'standard',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE cnpj = '98.765.432/0001-10')
    RETURNING id INTO v_company_id2;

    -- Se as empresas já existiam, obter seus IDs
    IF v_company_id1 IS NULL THEN
        SELECT id INTO v_company_id1 FROM companies WHERE cnpj = '12.345.678/0001-90' LIMIT 1;
    END IF;

    IF v_company_id2 IS NULL THEN
        SELECT id INTO v_company_id2 FROM companies WHERE cnpj = '98.765.432/0001-10' LIMIT 1;
    END IF;

    -- Agora inserir os endereços com referência às empresas
    INSERT INTO addresses (id, company_id, address_type_id, street, number, district, city, state, zip_code, is_default, created_at, updated_at)
    VALUES
    (gen_random_uuid(), v_company_id1, v_address_type_comercial, 'Av. Paulista', '1000', 'Bela Vista', 'São Paulo', 'SP', '01310-100', true, NOW(), NOW())
    RETURNING id INTO v_address_id1;

    INSERT INTO addresses (id, company_id, address_type_id, street, number, district, city, state, zip_code, is_default, created_at, updated_at)
    VALUES
    (gen_random_uuid(), v_company_id2, v_address_type_comercial, 'Rua Vergueiro', '500', 'Liberdade', 'São Paulo', 'SP', '01504-000', true, NOW(), NOW())
    RETURNING id INTO v_address_id2;

    -- Atualizar as empresas com os endereços principais
    UPDATE companies SET address_id = v_address_id1 WHERE id = v_company_id1;
    UPDATE companies SET address_id = v_address_id2 WHERE id = v_company_id2;

END;
$$;

-- Inserir papéis para a primeira empresa
DO $$
DECLARE
    v_company_id UUID;
BEGIN
    -- Obter ID da primeira empresa
    SELECT id INTO v_company_id FROM companies WHERE cnpj = '12.345.678/0001-90';

    -- Inserir papel Admin se não existir
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_company_id,
        'Admin',
        'Administrador com acesso total ao sistema',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE company_id = v_company_id AND name = 'Admin');

    -- Inserir papel Member se não existir
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_company_id,
        'Member',
        'Membro com acesso limitado ao sistema',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE company_id = v_company_id AND name = 'Member');
END;
$$;

-- Inserir papéis para a segunda empresa
DO $$
DECLARE
    v_company_id UUID;
BEGIN
    -- Obter ID da segunda empresa
    SELECT id INTO v_company_id FROM companies WHERE cnpj = '98.765.432/0001-10';

    -- Inserir papel Admin se não existir
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_company_id,
        'Admin',
        'Administrador com acesso total ao sistema',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE company_id = v_company_id AND name = 'Admin');

    -- Inserir papel Member se não existir
    INSERT INTO roles (id, company_id, name, description, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_company_id,
        'Member',
        'Membro com acesso limitado ao sistema',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE company_id = v_company_id AND name = 'Member');
END;
$$;

-- Inserir usuário admin com perfil e associá-lo à primeira empresa
DO $$
DECLARE
    v_user_id UUID;
    v_company_id UUID;
    v_role_id UUID;
BEGIN
    -- Gerar um UUID fixo para o usuário admin para garantir consistência
    v_user_id := 'af003736-7b02-4789-90f8-b3132c40fc79';

    -- Inserir usuário admin se não existir
    INSERT INTO users (id, email, password, status, created_at, updated_at)
    SELECT
        v_user_id,
        '<EMAIL>',
        -- Senha: Admin123 (hash bcrypt)
        '$2b$10$oAA8dart1E8/RG.tzpmfueju/5As1woSxL/N4.NOTf36CKZhiZltu',
        'active',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM users WHERE id = v_user_id);

    -- Inserir perfil para o usuário admin
    INSERT INTO profiles (id, username, first_name, last_name, phone, avatar_url, preferences, is_active, created_at, updated_at)
    SELECT
        v_user_id,
        'admin',
        'Administrador',
        'Sistema',
        '(11) 99999-9999',
        NULL,
        '{}',
        true,
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = v_user_id);

    -- Obter ID da primeira empresa
    SELECT id INTO v_company_id FROM companies WHERE cnpj = '12.345.678/0001-90';

    -- Obter ID do papel Admin da primeira empresa
    SELECT id INTO v_role_id FROM roles WHERE company_id = v_company_id AND name = 'Admin';

    -- Associar usuário à empresa com papel de Admin
    INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_user_id,
        v_company_id,
        v_role_id,
        NOW(),
        NOW()
    WHERE NOT EXISTS (
        SELECT 1 FROM user_company_roles
        WHERE user_id = v_user_id AND company_id = v_company_id AND role_id = v_role_id
    );
END;
$$;

-- Inserir usuário regular com perfil e associá-lo à segunda empresa
DO $$
DECLARE
    v_user_id UUID;
    v_company_id UUID;
    v_role_id UUID;
BEGIN
    -- Gerar um UUID fixo para o usuário regular para garantir consistência
    v_user_id := 'bf003736-7b02-4789-90f8-b3132c40fc80';

    -- Inserir usuário regular se não existir
    INSERT INTO users (id, email, password, status, created_at, updated_at)
    SELECT
        v_user_id,
        '<EMAIL>',
        -- Senha: User123 (hash bcrypt)
        '$2b$10$oAA8dart1E8/RG.tzpmfueju/5As1woSxL/N4.NOTf36CKZhiZltu',
        'active',
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM users WHERE id = v_user_id);

    -- Inserir perfil para o usuário regular
    INSERT INTO profiles (id, username, first_name, last_name, phone, avatar_url, preferences, is_active, created_at, updated_at)
    SELECT
        v_user_id,
        'user',
        'Usuário',
        'Comum',
        '(11) 88888-8888',
        NULL,
        '{}',
        true,
        NOW(),
        NOW()
    WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = v_user_id);

    -- Obter ID da segunda empresa
    SELECT id INTO v_company_id FROM companies WHERE cnpj = '98.765.432/0001-10';

    -- Obter ID do papel Member da segunda empresa
    SELECT id INTO v_role_id FROM roles WHERE company_id = v_company_id AND name = 'Member';

    -- Associar usuário à empresa com papel de Member
    INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
    SELECT
        gen_random_uuid(),
        v_user_id,
        v_company_id,
        v_role_id,
        NOW(),
        NOW()
    WHERE NOT EXISTS (
        SELECT 1 FROM user_company_roles
        WHERE user_id = v_user_id AND company_id = v_company_id AND role_id = v_role_id
    );
END;
$$;

-- Inserir Moedas Padrão (Global) - Conforme docs/database.sql
-- Note: Prisma schema uses UUID for id, but docs/database.sql inserts don't specify it. Assuming UUID generation is desired.
--       Also adding created_at/updated_at which are not in docs/database.sql INSERT but likely desired.
INSERT INTO public.currencies (id, code, name, symbol, decimal_places, is_default, created_at, updated_at) VALUES
(gen_random_uuid(), 'BRL', 'Real Brasileiro', 'R$', 2, true, NOW(), NOW()),
(gen_random_uuid(), 'USD', 'Dólar Americano', '$', 2, false, NOW(), NOW()),
(gen_random_uuid(), 'EUR', 'Euro', '€', 2, false, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    symbol = EXCLUDED.symbol,
    decimal_places = EXCLUDED.decimal_places,
    is_default = EXCLUDED.is_default,
    updated_at = NOW();

-- Inserir Métodos de Pagamento Padrão (Global) - Conforme docs/database.sql
INSERT INTO public.payment_methods (id, name, description, created_at, updated_at) VALUES
(gen_random_uuid(), 'dinheiro', 'Pagamento em dinheiro', NOW(), NOW()),
(gen_random_uuid(), 'pix', 'Transferência via PIX', NOW(), NOW()),
(gen_random_uuid(), 'cartao_credito', 'Pagamento com cartão de crédito', NOW(), NOW()),
(gen_random_uuid(), 'cartao_debito', 'Pagamento com cartão de débito', NOW(), NOW()),
(gen_random_uuid(), 'boleto', 'Pagamento via boleto bancário', NOW(), NOW()),
(gen_random_uuid(), 'transferencia', 'Transferência bancária', NOW(), NOW()),
(gen_random_uuid(), 'cheque', 'Pagamento com cheque', NOW(), NOW()),
(gen_random_uuid(), 'debito_automatico', 'Débito automático em conta', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Tipos de Recorrência Padrão (Global) - Conforme docs/database.sql
INSERT INTO public.recurrence_types (id, name, description, created_at, updated_at) VALUES
(gen_random_uuid(), 'unico', 'Pagamento/recebimento único', NOW(), NOW()),
(gen_random_uuid(), 'diario', 'Recorrência diária', NOW(), NOW()),
(gen_random_uuid(), 'semanal', 'Recorrência semanal', NOW(), NOW()),
(gen_random_uuid(), 'quinzenal', 'Recorrência quinzenal', NOW(), NOW()),
(gen_random_uuid(), 'mensal', 'Recorrência mensal', NOW(), NOW()),
(gen_random_uuid(), 'bimestral', 'Recorrência bimestral', NOW(), NOW()),
(gen_random_uuid(), 'trimestral', 'Recorrência trimestral', NOW(), NOW()),
(gen_random_uuid(), 'semestral', 'Recorrência semestral', NOW(), NOW()),
(gen_random_uuid(), 'anual', 'Recorrência anual', NOW(), NOW())
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = NOW();

-- Inserir Bancos Padrão (Global)
INSERT INTO public.banks (id, code, name, created_at, updated_at) VALUES
(gen_random_uuid(), '001', 'Banco do Brasil S.A.', NOW(), NOW()),
(gen_random_uuid(), '237', 'Banco Bradesco S.A.', NOW(), NOW()),
(gen_random_uuid(), '341', 'Itaú Unibanco S.A.', NOW(), NOW()),
(gen_random_uuid(), '104', 'Caixa Econômica Federal', NOW(), NOW()),
(gen_random_uuid(), '033', 'Banco Santander (Brasil) S.A.', NOW(), NOW()),
(gen_random_uuid(), '260', 'Nu Pagamentos S.A. (Nubank)', NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    updated_at = NOW();

-- Inserir dados relacionados para Contas a Pagar (Entities, Categories, etc.)
-- Inserir dados relacionados para Contas a Pagar e Contas Bancárias
DO $$
DECLARE
    v_company_id_fluxomax UUID;
    v_currency_id_brl UUID;
    v_entity_id_fornecedor UUID;
    v_category_id_despesas UUID;
    v_payment_method_id_transfer UUID;
    v_bank_id_itau UUID;
    v_bank_id_nubank UUID;
    v_bank_account_id_itau UUID;
    v_bank_account_id_nubank UUID;
BEGIN
    -- Obter ID da empresa FluxoMax Tecnologia (Assume it exists from previous steps)
    SELECT id INTO v_company_id_fluxomax FROM companies WHERE cnpj = '12.345.678/0001-90' LIMIT 1;
    IF v_company_id_fluxomax IS NULL THEN RAISE EXCEPTION 'Company FluxoMax Tecnologia not found'; END IF;

    -- Obter ID da Moeda BRL (Assume it exists from previous steps)
    SELECT id INTO v_currency_id_brl FROM currencies WHERE code = 'BRL' LIMIT 1;
    IF v_currency_id_brl IS NULL THEN RAISE EXCEPTION 'Currency BRL not found'; END IF;

    -- Obter ID do Método de Pagamento (Assume it exists from previous steps)
    SELECT id INTO v_payment_method_id_transfer FROM payment_methods WHERE name = 'transferencia' LIMIT 1;
    IF v_payment_method_id_transfer IS NULL THEN RAISE EXCEPTION 'Payment Method transferencia not found'; END IF;

    -- Obter IDs dos Bancos (Assume they exist from previous steps)
    SELECT id INTO v_bank_id_itau FROM banks WHERE code = '341' LIMIT 1;
    IF v_bank_id_itau IS NULL THEN RAISE EXCEPTION 'Bank 341 (Itaú) not found'; END IF;
    SELECT id INTO v_bank_id_nubank FROM banks WHERE code = '260' LIMIT 1;
    IF v_bank_id_nubank IS NULL THEN RAISE EXCEPTION 'Bank 260 (Nubank) not found'; END IF;

    -- Inserir/Obter Entidade (Fornecedor Exemplo)
    INSERT INTO entities (id, company_id, name, type, created_at, updated_at)
    VALUES (gen_random_uuid(), v_company_id_fluxomax, 'Fornecedor Exemplo Ltda', 'supplier', NOW(), NOW())
    ON CONFLICT DO NOTHING; -- Use ON CONFLICT or ensure WHERE NOT EXISTS works reliably
    -- Always select the ID after attempting insert
    SELECT id INTO v_entity_id_fornecedor FROM entities WHERE company_id = v_company_id_fluxomax AND name = 'Fornecedor Exemplo Ltda' LIMIT 1;
    IF v_entity_id_fornecedor IS NULL THEN RAISE EXCEPTION 'Failed to insert or find entity Fornecedor Exemplo Ltda'; END IF;


    -- Inserir/Obter Categoria (Despesas Administrativas)
    INSERT INTO categories (id, company_id, name, transaction_type, created_at, updated_at)
    VALUES (gen_random_uuid(), v_company_id_fluxomax, 'Despesas Administrativas', 'payable', NOW(), NOW())
    ON CONFLICT DO NOTHING; -- Assuming unique constraint on (company_id, name) exists or add WHERE NOT EXISTS
     -- Always select the ID after attempting insert
    SELECT id INTO v_category_id_despesas FROM categories WHERE company_id = v_company_id_fluxomax AND name = 'Despesas Administrativas' LIMIT 1;
    IF v_category_id_despesas IS NULL THEN RAISE EXCEPTION 'Failed to insert or find category Despesas Administrativas'; END IF;


    -- Inserir/Obter Contas Bancárias
    -- Conta Itaú
    INSERT INTO bank_accounts (id, company_id, bank_id, account_number, account_type, initial_balance, current_balance, balance_date, currency_id, name, is_enabled, created_at, updated_at)
    VALUES (gen_random_uuid(), v_company_id_fluxomax, v_bank_id_itau, '12345-6', 'corrente', 15000.00, 15000.00, NOW(), v_currency_id_brl, 'Itaú Principal', true, NOW(), NOW())
    ON CONFLICT DO NOTHING; -- Assuming unique constraint exists
     -- Always select the ID after attempting insert
    SELECT id INTO v_bank_account_id_itau FROM bank_accounts WHERE company_id = v_company_id_fluxomax AND bank_id = v_bank_id_itau AND account_number = '12345-6' LIMIT 1;
    IF v_bank_account_id_itau IS NULL THEN RAISE EXCEPTION 'Failed to insert or find bank account Itaú Principal'; END IF;

    -- Conta Nubank
    INSERT INTO bank_accounts (id, company_id, bank_id, account_number, account_type, initial_balance, current_balance, balance_date, currency_id, name, is_enabled, created_at, updated_at)
    VALUES (gen_random_uuid(), v_company_id_fluxomax, v_bank_id_nubank, '9876543-2', 'corrente', 5000.00, 5000.00, NOW(), v_currency_id_brl, 'Nubank Pagamentos', true, NOW(), NOW())
    ON CONFLICT DO NOTHING; -- Assuming unique constraint exists
     -- Always select the ID after attempting insert
    SELECT id INTO v_bank_account_id_nubank FROM bank_accounts WHERE company_id = v_company_id_fluxomax AND bank_id = v_bank_id_nubank AND account_number = '9876543-2' LIMIT 1;
    IF v_bank_account_id_nubank IS NULL THEN RAISE EXCEPTION 'Failed to insert or find bank account Nubank Pagamentos'; END IF;


    -- Inserir Contas a Pagar de Exemplo (Agora IDs should be populated)
    -- Conta 1: Aluguel Escritório (Pago pelo Itaú)
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, payment_method_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(), v_company_id_fluxomax, 'Aluguel Escritório Abril', v_entity_id_fornecedor,
        (date_trunc('month', current_date) + interval '1 month' - interval '1 day')::date + interval '5 days',
        5000.00, 0.00, v_currency_id_brl, 'pending', v_category_id_despesas, v_payment_method_id_transfer, v_bank_account_id_itau, NOW(), NOW()
    );

    -- Conta 2: Compra de Equipamento (Pago pelo Nubank)
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, invoice_number, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(), v_company_id_fluxomax, 'Compra Notebook Dell XPS', v_entity_id_fornecedor,
        current_date + interval '30 days',
        8500.50, 0.00, v_currency_id_brl, 'pending', v_category_id_despesas, 'NF-12345', v_bank_account_id_nubank, NOW(), NOW()
    );

    -- Conta 3: Serviço de Limpeza (Vencida - Pago pelo Itaú)
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(), v_company_id_fluxomax, 'Serviço Limpeza Março', v_entity_id_fornecedor,
        current_date - interval '10 days',
        800.00, 0.00, v_currency_id_brl, 'overdue', v_category_id_despesas, v_bank_account_id_itau, NOW() - interval '40 days', NOW() - interval '40 days'
    );

    -- Conta 4: Assinatura Software (Paga Parcialmente - Pago pelo Nubank)
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, notes, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(), v_company_id_fluxomax, 'Assinatura Software XYZ', v_entity_id_fornecedor,
        current_date + interval '5 days',
        350.00, 150.00, v_currency_id_brl, 'partially_paid', v_category_id_despesas, 'Pagamento parcial realizado dia X', v_bank_account_id_nubank, NOW(), NOW()
    );

    -- Conta 5: Consultoria Marketing (Pago pelo Itaú)
    INSERT INTO accounts_payable (id, company_id, description, entity_id, due_date, amount, paid_amount, currency_id, status, category_id, bank_account_id, created_at, updated_at)
    VALUES (
        gen_random_uuid(), v_company_id_fluxomax, 'Consultoria Marketing Digital', v_entity_id_fornecedor,
        current_date + interval '15 days',
        2200.75, 0.00, v_currency_id_brl, 'pending', v_category_id_despesas, v_bank_account_id_itau, NOW(), NOW()
    );

END;
$$;

-- Confirmar que todos os usuários têm perfis
DO $$
DECLARE
    v_user_count INT;
    v_profile_count INT;
    v_users_without_profiles INT;
BEGIN
    SELECT COUNT(*) INTO v_user_count FROM users WHERE deleted_at IS NULL;
    SELECT COUNT(*) INTO v_profile_count FROM profiles WHERE deleted_at IS NULL;

    SELECT COUNT(*) INTO v_users_without_profiles
    FROM users u
    LEFT JOIN profiles p ON u.id = p.id
    WHERE u.deleted_at IS NULL AND p.id IS NULL;

    RAISE NOTICE 'Total de usuários: %', v_user_count;
    RAISE NOTICE 'Total de perfis: %', v_profile_count;
    RAISE NOTICE 'Usuários sem perfil: %', v_users_without_profiles;

    IF v_users_without_profiles > 0 THEN
        RAISE WARNING 'Existem usuários sem perfil! Verifique o script de seed.';
    ELSE
        RAISE NOTICE 'Todos os usuários possuem perfil. Seed concluído com sucesso!';
    END IF;
END;
$$;
