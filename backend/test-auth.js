const bcrypt = require('bcrypt');

async function testAuth() {
  const password = 'Admin123';
  // Logs removidos por segurança - não expor informações de autenticação

  // Gerar um novo hash
  const hash = await bcrypt.hash(password, 10);
  // Hash gerado - log removido por segurança

  // Verificar se a senha corresponde ao hash
  const isValid = await bcrypt.compare(password, hash);
  // Senha válida com hash gerado - log removido por segurança

  // Verificar com o hash atual no banco
  const currentHash =
    '$2b$10$Ky4EUrgWAlviutkebawxBungrxj.AHsDILDfweIBIl1nlFXFqdiGK';
  // Hash atual no banco - log removido por segurança

  const isValidWithCurrent = await bcrypt.compare(password, currentHash);
  // Senha válida com hash atual - log removido por segurança
}

testAuth().catch(() => {
  // Erro no teste de autenticação - falha silenciosa
});
