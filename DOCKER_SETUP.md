# Docker Setup Instructions for macOS

Before you can use the development environment, you need to install Docker Desktop for macOS. Follow these steps:

## Installing Docker Desktop

1. Visit the [Docker Desktop for Mac download page](https://www.docker.com/products/docker-desktop/)
2. Download the appropriate version for your Mac (Intel or Apple Silicon)
3. Double-click the downloaded `.dmg` file and drag the Docker app to your Applications folder
4. Open Docker Desktop from your Applications folder
5. Follow the installation wizard to complete the setup
6. Wait for <PERSON><PERSON> to start (you'll see the Docker icon in the menu bar)

## Verifying the Installation

After installation, verify that Dock<PERSON> is working correctly:

1. Open Terminal
2. Run the following commands:

```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Run a simple test container
docker run hello-world
```

If all commands execute without errors, Docker is installed correctly.

## Using the Development Environment

Once Docker is installed, you can use the development environment as described in the README.md:

```bash
# Navigate to the project directory
cd /Users/<USER>/Development/Projects/finance-gest

# Start the development environment
./dev.sh start

# View logs
./dev.sh logs
```

## Troubleshooting

If you encounter any issues:

1. Make sure Docker Desktop is running (check the Docker icon in the menu bar)
2. Restart Docker Desktop if necessary
3. Check Docker's resource allocation in Docker Desktop preferences
4. Ensure you have sufficient disk space

For more help, refer to the [Docker Desktop for Mac documentation](https://docs.docker.com/desktop/install/mac-install/).
