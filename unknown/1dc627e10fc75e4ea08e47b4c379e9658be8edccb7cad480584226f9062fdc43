
import {
  Table,
} from "@/components/ui/table";
import { useState, useEffect } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useIsMobile } from "@/hooks/use-mobile";
import { Transaction } from "@/types/transaction";
import { transactionService } from "@/services/api/transactionService";
import { TransactionTableHeader } from "./TransactionTableHeader";
import { TransactionTableBody } from "./TransactionTableBody";
import { TransactionMobileCard } from "./TransactionMobileCard";
import { toast } from "react-toastify";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface TransactionsTableProps {
  onEdit: (item: Transaction, mode?: "view" | "edit") => void;
  searchTerm?: string;
  selectedPeriod?: string;
  selectedAccountId?: string;
  selectedEntityId?: string;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  transactionType?: string;
}

export default function TransactionsTable({ 
  onEdit, 
  searchTerm = "", 
  selectedPeriod = "current-month",
  selectedAccountId = "all",
  selectedEntityId = "all",
  customDateRange = {},
  transactionType
}: TransactionsTableProps) {
  const [data, setData] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const isMobile = useIsMobile();
  
  // Converter o período selecionado para datas
  const getDateRangeFromPeriod = () => {
    const now = new Date();
    let startDate: Date | undefined;
    let endDate: Date | undefined;
    
    switch (selectedPeriod) {
      case "today":
        startDate = new Date(now.setHours(0, 0, 0, 0));
        endDate = new Date(now.setHours(23, 59, 59, 999));
        break;
      case "current-week":
        const firstDay = new Date(now);
        firstDay.setDate(now.getDate() - now.getDay());
        firstDay.setHours(0, 0, 0, 0);
        const lastDay = new Date(now);
        lastDay.setDate(now.getDate() + (6 - now.getDay()));
        lastDay.setHours(23, 59, 59, 999);
        startDate = firstDay;
        endDate = lastDay;
        break;
      case "current-month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        break;
      case "previous-month":
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
        break;
      case "custom":
        startDate = customDateRange.startDate;
        endDate = customDateRange.endDate;
        break;
    }
    
    return {
      startDate: startDate ? startDate.toISOString() : undefined,
      endDate: endDate ? endDate.toISOString() : undefined
    };
  };
  
  // Carregar dados da API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const { startDate, endDate } = getDateRangeFromPeriod();
        
        let response;
        if (selectedAccountId !== "all") {
          response = await transactionService.getTransactionsByBankAccount(
            selectedAccountId,
            currentPage,
            itemsPerPage,
            startDate,
            endDate,
            transactionType
          );
        } else {
          response = await transactionService.getTransactions(
            currentPage,
            itemsPerPage,
            searchTerm,
            transactionType,
            undefined, // status
            startDate,
            endDate,
            undefined, // categoryId
            selectedEntityId !== "all" ? selectedEntityId : undefined
          );
        }
        
        setData(response.items || []);
        setTotalPages(typeof response.totalPages === 'string' ? parseInt(response.totalPages) : (response.totalPages || 1));
        setTotalItems(typeof response.total === 'string' ? parseInt(response.total) : (response.total || 0));
      } catch (err) {
        setError("Erro ao carregar transações");
        toast.error("Não foi possível carregar as transações. Tente novamente mais tarde.");
        console.error("Erro ao carregar transações:", err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [
    currentPage, 
    itemsPerPage, 
    searchTerm, 
    selectedPeriod, 
    selectedAccountId, 
    selectedEntityId, 
    customDateRange, 
    transactionType
  ]);

  // Função para mudar de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (loading) {
    return (
      <div className="rounded-md border p-8 flex justify-center items-center">
        <LoadingSpinner size={36} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md border p-8 text-center text-red-500">
        <p>{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="mt-4 px-4 py-2 bg-primary text-white rounded-md"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  // Garantir que data é sempre um array
  const safeData = Array.isArray(data) ? data : [];

  return (
    <div className="rounded-md border">
      {isMobile ? (
        <div className="p-4">
          {safeData.length > 0 ? (
            safeData.map(transaction => (
              <TransactionMobileCard 
                key={transaction.id}
                transaction={transaction} 
                onEdit={onEdit} 
              />
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Nenhuma transação encontrada para os filtros selecionados.
            </div>
          )}
        </div>
      ) : (
        <Table>
          <TransactionTableHeader />
          <TransactionTableBody 
            transactions={safeData} 
            onEdit={onEdit} 
          />
        </Table>
      )}
      
      <div className="p-4 border-t">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                href="#" 
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) handlePageChange(currentPage - 1);
                }} 
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
            
            {/* Gerar links de paginação dinamicamente */}
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, i) => {
              // Lógica para mostrar páginas ao redor da página atual
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }
              
              return (
                <PaginationItem key={pageNum}>
                  <PaginationLink 
                    href="#" 
                    isActive={currentPage === pageNum}
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageChange(pageNum);
                    }}
                  >
                    {pageNum}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            
            <PaginationItem>
              <PaginationNext 
                href="#" 
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < totalPages) handlePageChange(currentPage + 1);
                }} 
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
        
        <div className="text-center text-sm text-muted-foreground mt-2">
          Mostrando {safeData.length} de {totalItems} transações
        </div>
      </div>
    </div>
  );
}
