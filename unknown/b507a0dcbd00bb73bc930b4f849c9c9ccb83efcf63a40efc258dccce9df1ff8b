import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface DataPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  siblingCount?: number;
  className?: string;
  showItemsPerPage?: boolean;
  itemsPerPageOptions?: number[];
  loading?: boolean;
}

export function DataPagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  siblingCount = 1,
  className = '',
  showItemsPerPage = true,
  itemsPerPageOptions = [10, 25, 50, 100],
  loading = false,
}: DataPaginationProps) {
  // Função para gerar o intervalo de páginas a serem exibidas
  const generatePaginationRange = () => {
    // Garantir que siblingCount seja pelo menos 1
    const actualSiblingCount = Math.max(1, siblingCount);
    
    // Calcular o número total de itens a serem exibidos (incluindo elipses e primeira/última página)
    const totalPageNumbers = actualSiblingCount * 2 + 3; // siblingCount em cada lado + página atual + primeira e última página
    
    // Se o número total de páginas for menor que o número total de itens a serem exibidos,
    // exibir todas as páginas sem elipses
    if (totalPages <= totalPageNumbers) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    // Calcular os índices das páginas à esquerda e à direita da página atual
    const leftSiblingIndex = Math.max(currentPage - actualSiblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + actualSiblingCount, totalPages);
    
    // Determinar se devemos mostrar elipses à esquerda e à direita
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1;
    
    // Caso 1: Mostrar elipses à direita
    if (!shouldShowLeftDots && shouldShowRightDots) {
      const leftItemCount = 3 + 2 * actualSiblingCount;
      const leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
      
      return [...leftRange, 'ellipsis', totalPages];
    }
    
    // Caso 2: Mostrar elipses à esquerda
    if (shouldShowLeftDots && !shouldShowRightDots) {
      const rightItemCount = 3 + 2 * actualSiblingCount;
      const rightRange = Array.from(
        { length: rightItemCount },
        (_, i) => totalPages - rightItemCount + i + 1
      );
      
      return [1, 'ellipsis', ...rightRange];
    }
    
    // Caso 3: Mostrar elipses em ambos os lados
    if (shouldShowLeftDots && shouldShowRightDots) {
      const middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i
      );
      
      return [1, 'ellipsis', ...middleRange, 'ellipsis', totalPages];
    }
    
    // Não deve chegar aqui, mas por segurança
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  };
  
  const paginationRange = generatePaginationRange();
  
  // Manipulador para alteração de itens por página
  const handleItemsPerPageChange = (value: string) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(Number(value));
    }
  };
  
  // Calcular o intervalo de itens exibidos
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  
  return (
    <div className={`flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0 ${className}`}>
      <div className="text-sm text-muted-foreground">
        {totalItems > 0 ? (
          <>
            Mostrando <span className="font-medium">{startItem}</span> a{' '}
            <span className="font-medium">{endItem}</span> de{' '}
            <span className="font-medium">{totalItems}</span> itens
          </>
        ) : (
          'Nenhum item encontrado'
        )}
      </div>
      
      <div className="flex items-center space-x-4">
        {showItemsPerPage && onItemsPerPageChange && (
          <div className="flex items-center space-x-2">
            <Label htmlFor="itemsPerPage" className="text-sm">
              Itens por página:
            </Label>
            <Select
              value={String(itemsPerPage)}
              onValueChange={handleItemsPerPageChange}
              disabled={loading}
            >
              <SelectTrigger id="itemsPerPage" className="h-8 w-[70px]">
                <SelectValue placeholder={String(itemsPerPage)} />
              </SelectTrigger>
              <SelectContent>
                {itemsPerPageOptions.map((option) => (
                  <SelectItem key={option} value={String(option)}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => currentPage > 1 && !loading && onPageChange(currentPage - 1)}
                className={currentPage === 1 || loading ? 'pointer-events-none opacity-50' : ''}
                tabIndex={currentPage === 1 || loading ? -1 : 0}
              />
            </PaginationItem>
            
            {paginationRange.map((pageNumber, index) => {
              if (pageNumber === 'ellipsis') {
                return (
                  <PaginationItem key={`ellipsis-${index}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
              
              return (
                <PaginationItem key={pageNumber}>
                  <PaginationLink
                    isActive={pageNumber === currentPage}
                    onClick={() => !loading && onPageChange(pageNumber as number)}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            
            <PaginationItem>
              <PaginationNext
                onClick={() => currentPage < totalPages && !loading && onPageChange(currentPage + 1)}
                className={currentPage === totalPages || loading ? 'pointer-events-none opacity-50' : ''}
                tabIndex={currentPage === totalPages || loading ? -1 : 0}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}

// Componente de paginação que se integra diretamente com o hook usePaginatedApi
interface PaginatedDataProps<T> {
  data: {
    data: T[];
    meta: {
      currentPage: number;
      itemsPerPage: number;
      totalItems: number;
      totalPages: number;
    };
  } | null;
  loading: boolean;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  siblingCount?: number;
  className?: string;
  showItemsPerPage?: boolean;
  itemsPerPageOptions?: number[];
}

export function PaginatedData<T>({
  data,
  loading,
  onPageChange,
  onItemsPerPageChange,
  siblingCount,
  className,
  showItemsPerPage,
  itemsPerPageOptions,
}: PaginatedDataProps<T>) {
  if (!data || data.meta.totalItems === 0) {
    return null;
  }
  
  return (
    <DataPagination
      currentPage={data.meta.currentPage}
      totalPages={data.meta.totalPages}
      totalItems={data.meta.totalItems}
      itemsPerPage={data.meta.itemsPerPage}
      onPageChange={onPageChange}
      onItemsPerPageChange={onItemsPerPageChange}
      siblingCount={siblingCount}
      className={className}
      showItemsPerPage={showItemsPerPage}
      itemsPerPageOptions={itemsPerPageOptions}
      loading={loading}
    />
  );
}
