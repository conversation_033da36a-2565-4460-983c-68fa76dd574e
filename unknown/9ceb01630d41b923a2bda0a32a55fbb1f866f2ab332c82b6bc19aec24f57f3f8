FROM node:20-alpine

# Define working directory
WORKDIR /app

# Install dependencies required for node-gyp
RUN apk add --no-cache python3 make g++

# Set environment variables
ENV VITE_API_URL=http://localhost:3000/api
ENV NODE_ENV=development

# Expose the application port
EXPOSE 3001

# Use a shell script to ensure dependencies are installed before starting the dev server
COPY ./entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
