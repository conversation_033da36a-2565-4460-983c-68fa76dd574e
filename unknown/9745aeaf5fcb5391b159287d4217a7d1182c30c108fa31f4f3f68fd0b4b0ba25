
import { Check, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface Account {
  id: string;
  name: string;
}

interface AccountFilterProps {
  selectedAccountId: string;
  accounts: Account[];
  onAccountChange: (accountId: string) => void;
}

const AccountFilter = ({ selectedAccountId, accounts, onAccountChange }: AccountFilterProps) => {
  const [open, setOpen] = useState(false);
  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId) || 
                         { id: 'all', name: 'Todas as Contas' };

  const handleAccountSelect = (accountId: string) => {
    onAccountChange(accountId);
    setOpen(false); // Close the popover after selection
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-2 bg-white dark:bg-dark-card shadow-sm border border-gray-100 dark:border-dark-card-light rounded-md justify-between h-10 px-4 py-2 w-36"
        >
          <div className="flex items-center gap-1.5">
            <CreditCard className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
              {selectedAccount.name.length > 10 
                ? selectedAccount.name.substring(0, 10) + "..." 
                : selectedAccount.name}
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-0 shadow-lg rounded-md border border-gray-100 dark:border-dark-card-light dark:bg-dark-card">
        <div className="py-1 max-h-72 overflow-y-auto scrollbar-thin">
          <div 
            key="all"
            className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-dark-card-light cursor-pointer"
            onClick={() => handleAccountSelect('all')}
          >
            <div className="flex items-center gap-2">
              {selectedAccountId === 'all' ? (
                <Check className="h-4 w-4 text-[#007FFF] dark:text-gray-200 min-w-4" />
              ) : (
                <div className="w-4 min-w-4" />
              )}
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium">Todas as Contas</span>
            </div>
          </div>
          {accounts.map((account) => (
            <div 
              key={account.id}
              className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-dark-card-light cursor-pointer"
              onClick={() => handleAccountSelect(account.id)}
            >
              <div className="flex items-center gap-2">
                {selectedAccountId === account.id ? (
                  <Check className="h-4 w-4 text-[#007FFF] dark:text-gray-200 min-w-4" />
                ) : (
                  <div className="w-4 min-w-4" />
                )}
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium">{account.name}</span>
              </div>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default AccountFilter;
