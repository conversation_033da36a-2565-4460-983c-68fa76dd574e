export type BaseTransaction = {
  id: string;
  description: string;
  amount: number;
  transactionDate: Date;
  bankAccount: string;
  bankAccountId: string;
  type: string;
  relatedId: string;
  entity: string;
  entityId?: string;
  interestAmount?: number;
  discountAmount?: number;
};

export type TransferTransaction = BaseTransaction & {
  type: 'transfer';
  destinationAccount: string;
  destinationAccountId: string;
};

export type Transaction = BaseTransaction | TransferTransaction;

// Interface para as transações da lista
export interface TransactionListItem {
  id: string;
  description: string;
  amount: number;
  date: string;
  type: 'INCOME' | 'EXPENSE';
  category: string;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  accountId: string;
  account: {
    id: string;
    name: string;
  };
  entityId?: string;
  entity?: {
    id: string;
    name: string;
    type: 'CUSTOMER' | 'SUPPLIER';
  };
  projectId?: string;
  project?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}
