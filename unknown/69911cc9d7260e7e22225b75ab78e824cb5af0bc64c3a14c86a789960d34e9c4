
import { Button } from "@/components/ui/button";

interface AccountsPayableStatusFilterProps {
  filter: string;
  onFilterChange: (filter: string) => void;
}

export default function AccountsPayableStatusFilter({
  filter,
  onFilterChange,
}: AccountsPayableStatusFilterProps) {
  return (
    <>
      <Button 
        variant={filter === "all" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("all")}
        className={filter === "all" ? "bg-[#007FFF] hover:bg-[#0066CC]" : ""}
      >
        Todas
      </Button>
      <Button 
        variant={filter === "pending" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("pending")}
        className={filter === "pending" ? "bg-[#007FFF] hover:bg-[#0066CC]" : ""}
      >
        Pendentes
      </Button>
      <Button 
        variant={filter === "overdue" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("overdue")}
        className={filter === "overdue" ? "bg-[#007FFF] hover:bg-[#0066CC]" : ""}
      >
        Em Atraso
      </Button>
      <Button 
        variant={filter === "partial" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("partial")}
        className={filter === "partial" ? "bg-[#007FFF] hover:bg-[#0066CC]" : ""}
      >
        Parciais
      </Button>
      <Button 
        variant={filter === "paid" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("paid")}
        className={filter === "paid" ? "bg-[#007FFF] hover:bg-[#0066CC]" : ""}
      >
        Pagas
      </Button>
    </>
  );
}
