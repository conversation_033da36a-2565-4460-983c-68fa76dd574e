import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { MoneyInput } from "@/components/ui-custom/MoneyInput";
import { parseCurrencyToNumber, numberToFormattedString } from "@/utils/currencyUtils";

interface TransactionAmountFieldProps {
  id: string;
  label?: string;
  value: number | string | null | undefined;
  onChange: (value: number) => void;
  isMainAmount?: boolean;
  className?: string;
  disabled?: boolean;
}

export default function TransactionAmountField({
  id,
  label,
  value,
  onChange,
  isMainAmount = false,
  className = "",
  disabled = false
}: TransactionAmountFieldProps) {

  const formattedValueForInput = typeof value === 'number' ? numberToFormattedString(value) : (value ?? '');

  const numericValueForMoneyInput = parseCurrencyToNumber(formattedValueForInput);

  const handleValueChange = (newValue: number) => {
    onChange(newValue);
  };

  return (
    <div className="space-y-2 w-full">
      {label && <Label htmlFor={id}>{label}</Label>}
      <MoneyInput
        id={id}
        value={numericValueForMoneyInput}
        onValueChange={handleValueChange}
        disabled={disabled}
        className={cn(
          "w-full",
          isMainAmount ? "text-base border-2 border-[#007FFF]/30 focus-visible:ring-[#007FFF]" : "",
          className
        )}
      />
    </div>
  );
}
