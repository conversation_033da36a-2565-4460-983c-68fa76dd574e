import { Layout } from '@/components/layout/Layout';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { PlusCircle, AlertTriangle, ArrowDown, CalendarClock } from 'lucide-react';
import { useState, useEffect } from 'react';
import AccountsPayableTable from '@/components/accounts-payable/AccountsPayableTable';
import DashboardCard from '@/components/dashboard/DashboardCard';
import { useQuery } from '@tanstack/react-query';
import { accountsPayableService } from '@/services/api/accountsPayableService';
import { bankAccountService } from '@/services/api/bankAccountService';
import { useAccountsPayable } from '@/hooks/api/useAccountsPayable';
import { AccountsPayableSummary, ApiResponse, BankAccount } from '@/types/api';
import { useAuth } from '@/contexts/AuthContext';

const formatCurrency = (value: number | undefined | null) => {
  if (value === undefined || value === null) return '--';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
};

export default function AccountsPayable() {
  const location = useLocation();
  const navigate = useNavigate();
  const { activeCompanyId } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedAccountId, setSelectedAccountId] = useState('all');
  const [selectedEntityId, setSelectedEntityId] = useState('all');
  const [accounts, setAccounts] = useState<Array<{ id: string; name: string }>>([]);
  const [customDateRange, setCustomDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>({});

  // Converter o período selecionado para datas para a API
  const getDateRangeFromPeriod = () => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    switch (selectedPeriod) {
      case 'current-month':
        return { startDate: startOfMonth.toISOString().split('T')[0], endDate: endOfMonth.toISOString().split('T')[0] };
      case 'custom':
        return {
          startDate: customDateRange.startDate?.toISOString().split('T')[0],
          endDate: customDateRange.endDate?.toISOString().split('T')[0]
        };
      default:
        return {};
    }
  };

  // Converter o filtro de status para o formato da API
  const getStatusFilter = () => {
    switch (activeFilter) {
      case 'pending': return 'pending';
      case 'paid': return 'paid';
      case 'overdue': return 'overdue';
      case 'partial': return 'partial';
      default: return undefined;
    }
  };

  // Buscar resumo das contas a pagar
  const {
    data: summaryData,
    isLoading: isLoadingSummary,
    error: summaryError,
  } = useQuery<
    ApiResponse<AccountsPayableSummary>,
    Error,
    AccountsPayableSummary
  >({
    queryKey: ['accountsPayableSummary', activeCompanyId],
    queryFn: () => accountsPayableService.getAccountsPayableSummary(activeCompanyId),
    select: (response) => response.data,
    staleTime: 1000 * 60 * 5,
    enabled: !!activeCompanyId,
  });

  // Buscar contas a pagar com filtros
  const dateRange = getDateRangeFromPeriod();
  const status = getStatusFilter();

  const {
    data: accountsPayableData,
    isLoading: isLoadingAccounts,
    error: accountsError,
    refetch: refetchAccounts
  } = useAccountsPayable(
    1, // página inicial
    100, // limite por página
    {
      status,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      entityId: selectedEntityId !== 'all' ? selectedEntityId : undefined,
      search: searchTerm || undefined
    }
  );

  // Buscar contas bancárias para o filtro
  const {
    data: bankAccountsData,
    isLoading: isLoadingBankAccounts
  } = useQuery({
    queryKey: ['bankAccounts', activeCompanyId],
    queryFn: () => bankAccountService.getBankAccounts(1, 100, undefined, undefined, undefined, activeCompanyId),
    enabled: !!activeCompanyId,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Atualizar a lista de contas bancárias quando os dados forem carregados
  useEffect(() => {
    if (bankAccountsData?.data) {
      const formattedAccounts = bankAccountsData.data.map((account: BankAccount) => ({
        id: account.id,
        name: account.name
      }));
      setAccounts(formattedAccounts);
    }
  }, [bankAccountsData]);

  const handleAddNew = () => {
    navigate('/accounts-payable/new/new');
  };

  const handleEdit = (item: any) => {
    navigate(`/accounts-payable/${item.id}/edit`);
  };

  const handleView = (item: any) => {
    navigate(`/accounts-payable/${item.id}/view`);
  };

  const handleFilter = (filter: string) => {
    setActiveFilter(filter);
    // Recarregar dados com o novo filtro
    setTimeout(() => refetchAccounts(), 0);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    // Recarregar dados após limpar o filtro
    setTimeout(() => refetchAccounts(), 0);
  };

  const handleClearAllFilters = () => {
    setSearchTerm('');
    setActiveFilter('all');
    setSelectedPeriod('current-month');
    setSelectedAccountId('all');
    setSelectedEntityId('all');
    setCustomDateRange({});
    // Recarregar dados após limpar todos os filtros
    setTimeout(() => refetchAccounts(), 0);
  };

  const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
    setCustomDateRange({ startDate, endDate });
    if (startDate && endDate) {
      setSelectedPeriod('custom');
      // Recarregar dados com o novo intervalo de datas
      setTimeout(() => refetchAccounts(), 0);
    }
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <h1 className="text-2xl font-bold mb-6">Contas a Pagar</h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-5 mb-6">
          <div className="animate-scale-in" style={{ animationDelay: '0.1s' }}>
            <DashboardCard
              title="Total a Pagar"
              value={formatCurrency(summaryData?.totalDue)}
              valueColor={'text-red-500'}
              icon={<ArrowDown className="h-5 w-5" />}
              subtitle="Todas as contas abertas"
              isLoading={isLoadingSummary}
              hasError={!!summaryError}
            />
          </div>

          <div className="animate-scale-in" style={{ animationDelay: '0.2s' }}>
            <DashboardCard
              title="Em Atraso"
              value={formatCurrency(summaryData?.totalOverdue)}
              valueColor={(summaryData?.totalOverdue ?? 0) > 0 ? 'text-orange-500' : 'text-gray-500'}
              icon={<AlertTriangle className="h-5 w-5" />}
              subtitle="Vencidas e não pagas"
              isLoading={isLoadingSummary}
              hasError={!!summaryError}
            />
          </div>

          <div className="animate-scale-in" style={{ animationDelay: '0.3s' }}>
            <DashboardCard
              title="A Vencer"
              value={formatCurrency(summaryData?.totalUpcoming)}
              valueColor={'text-yellow-500'}
              icon={<CalendarClock className="h-5 w-5" />}
              subtitle="Próximos 30 dias"
              isLoading={isLoadingSummary}
              hasError={!!summaryError}
            />
          </div>
        </div>

        <div className="flex mb-6">
          <Button onClick={handleAddNew} className="gap-1 whitespace-nowrap">
            <PlusCircle className="h-4 w-4" />
            Nova Conta
          </Button>
        </div>

        {isLoadingAccounts ? (
          <div className="flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : accountsError ? (
          <div className="p-4 border rounded-md bg-red-50 text-red-500">
            <p>Erro ao carregar contas a pagar. Por favor, tente novamente.</p>
            <p className="text-sm">{accountsError.message}</p>
          </div>
        ) : (
          <AccountsPayableTable
            onEdit={handleEdit}
            onView={handleView}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            filter={activeFilter}
            onFilterChange={handleFilter}
            selectedPeriod={selectedPeriod}
            onPeriodChange={setSelectedPeriod}
            selectedAccountId={selectedAccountId}
            onAccountChange={setSelectedAccountId}
            selectedEntityId={selectedEntityId}
            onEntityChange={setSelectedEntityId}
            accounts={accounts}
            onClearFilters={handleClearFilters}
            onClearAllFilters={handleClearAllFilters}
            customDateRange={customDateRange}
            onDateRangeChange={handleDateRangeChange}
            data={accountsPayableData?.data || []}
            totalItems={accountsPayableData?.total || 0}
            isLoading={isLoadingAccounts}
          />
        )}
      </div>
    </Layout>
  );
}
