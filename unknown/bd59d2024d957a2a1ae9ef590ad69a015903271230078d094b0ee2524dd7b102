// backend/src/routes/currencies/currencies.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { Currency, Prisma } from '@prisma/client';

@Injectable()
export class CurrenciesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new currency
   */
  async create(createCurrencyDto: CreateCurrencyDto): Promise<Currency> {
    try {
      // Check if a currency with the same code already exists
      const existingCurrency = await this.prisma.currency.findUnique({
        where: { code: createCurrencyDto.code },
      });

      if (existingCurrency) {
        throw new ConflictException(`Currency with code ${createCurrencyDto.code} already exists`);
      }

      // If this currency is set as default, unset any existing default
      if (createCurrencyDto.isDefault) {
        await this.unsetDefaultCurrency();
      }

      // Create the currency
      return await this.prisma.currency.create({
        data: createCurrencyDto,
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      console.error('Error creating currency:', error);
      throw new InternalServerErrorException('Could not create currency');
    }
  }

  /**
   * Find all currencies with pagination
   */
  async findAll(
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: Currency[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    try {
      // Get total count
      const total = await this.prisma.currency.count();

      // Get currencies with pagination
      const currencies = await this.prisma.currency.findMany({
        skip,
        take: limit,
        orderBy: [
          { isDefault: 'desc' }, // Default currency first
          { code: 'asc' }, // Then alphabetically by code
        ],
      });

      return {
        data: currencies,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding currencies:', error);
      throw new InternalServerErrorException('Could not retrieve currencies');
    }
  }

  /**
   * Find one currency by id
   */
  async findOne(id: string): Promise<Currency> {
    try {
      const currency = await this.prisma.currency.findUnique({
        where: { id },
      });

      if (!currency) {
        throw new NotFoundException(`Currency with ID ${id} not found`);
      }

      return currency;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding currency with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve currency');
    }
  }

  /**
   * Update a currency
   */
  async update(id: string, updateCurrencyDto: UpdateCurrencyDto): Promise<Currency> {
    try {
      // Check if currency exists
      await this.findOne(id);

      // If updating code, check if the new code already exists for another currency
      if (updateCurrencyDto.code) {
        const existingCurrency = await this.prisma.currency.findUnique({
          where: { code: updateCurrencyDto.code },
        });

        if (existingCurrency && existingCurrency.id !== id) {
          throw new ConflictException(`Currency with code ${updateCurrencyDto.code} already exists`);
        }
      }

      // If setting this currency as default, unset any existing default
      if (updateCurrencyDto.isDefault) {
        await this.unsetDefaultCurrency();
      }

      // Update the currency
      return await this.prisma.currency.update({
        where: { id },
        data: updateCurrencyDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.error(`Error updating currency with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update currency');
    }
  }

  /**
   * Remove a currency
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if currency exists
      const currency = await this.findOne(id);

      // Check if currency is being used by any account payable
      const accountsPayable = await this.prisma.accountsPayable.findFirst({
        where: { currencyId: id },
      });

      if (accountsPayable) {
        throw new BadRequestException(
          'Cannot delete currency because it is being used by one or more accounts payable'
        );
      }

      // Check if currency is being used by any account receivable
      const accountsReceivable = await this.prisma.accountsReceivable.findFirst({
        where: { currencyId: id },
      });

      if (accountsReceivable) {
        throw new BadRequestException(
          'Cannot delete currency because it is being used by one or more accounts receivable'
        );
      }

      // Check if currency is being used by any bank account
      const bankAccounts = await this.prisma.bankAccount.findFirst({
        where: { currencyId: id },
      });

      if (bankAccounts) {
        throw new BadRequestException(
          'Cannot delete currency because it is being used by one or more bank accounts'
        );
      }

      // Check if currency is default
      if (currency.isDefault) {
        throw new BadRequestException(
          'Cannot delete the default currency. Set another currency as default first.'
        );
      }

      // Delete the currency
      await this.prisma.currency.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error removing currency with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete currency');
    }
  }

  /**
   * Helper method to unset any existing default currency
   */
  private async unsetDefaultCurrency(): Promise<void> {
    try {
      await this.prisma.currency.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    } catch (error) {
      console.error('Error unsetting default currency:', error);
      throw new InternalServerErrorException('Could not update default currency status');
    }
  }
}
