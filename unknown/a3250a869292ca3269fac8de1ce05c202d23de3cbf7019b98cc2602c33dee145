import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { RecurringTasksService } from './recurring-tasks.service';
import { RecurringSchedulesModule } from '../routes/recurring-schedules/recurring-schedules.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    RecurringSchedulesModule,
  ],
  providers: [RecurringTasksService],
})
export class TasksModule {}
