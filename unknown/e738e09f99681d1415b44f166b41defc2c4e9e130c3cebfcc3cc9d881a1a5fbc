
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { formatCurrency, parseCurrencyToNumber } from "@/utils/currencyUtils";
import TransactionAmountField from "./TransactionAmountField";
import TransactionDateSelector from "./TransactionDateSelector";
import TransactionAccountSelector from "./TransactionAccountSelector";
import TransactionFinalValueDisplay from "./TransactionFinalValueDisplay";
import { transactionService } from "@/services/api";
import { toast } from "sonner";

interface PaymentFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: any;
}

import { useActiveCompany } from '@/hooks/useActiveCompany';
import { useBankAccounts } from '@/hooks/api/useBankAccounts';

export default function PaymentForm({ open, onOpenChange, transaction }: PaymentFormProps) {
  const activeCompanyId = useActiveCompany();
  const { data, isLoading } = useBankAccounts(1, 100, { companyId: activeCompanyId });
  const accounts = data?.data || [];
  const [paymentValue, setPaymentValue] = useState<string>("0,00");
  const [interestValue, setInterestValue] = useState<string>("0,00");
  const [discountValue, setDiscountValue] = useState<string>("0,00");
  const [selectedAccount, setSelectedAccount] = useState<string>("");
  const [paymentDate, setPaymentDate] = useState<Date | undefined>(new Date());
  const [finalValue, setFinalValue] = useState<number>(0);

  useEffect(() => {
    if (transaction && open) {
      // Initialize form with transaction data
      const amount = transaction.amount || 0;
      setPaymentValue(amount.toFixed(2).replace(".", ","));
      setInterestValue("0,00");
      setDiscountValue("0,00");
      setFinalValue(amount);
      setSelectedAccount(transaction.bankAccountId || "");
      setPaymentDate(new Date());
    }
  }, [transaction, open]);

  // Calculate final value when payment, interest or discount changes
  useEffect(() => {
    const payment = parseCurrencyToNumber(paymentValue) || 0;
    const interest = parseCurrencyToNumber(interestValue) || 0;
    const discount = parseCurrencyToNumber(discountValue) || 0;

    const calculatedValue = payment + interest - discount;
    setFinalValue(calculatedValue);
  }, [paymentValue, interestValue, discountValue]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    if (!selectedAccount) {
      toast.error("Selecione uma conta bancária para o pagamento");
      return;
    }

    if (parseCurrencyToNumber(paymentValue) <= 0) {
      toast.error("O valor do pagamento deve ser maior que zero");
      return;
    }

    try {
      setIsSubmitting(true);

      // Criar uma transação do tipo expense vinculada à conta a pagar
      await transactionService.createTransaction({
        type: 'expense',
        amount: parseCurrencyToNumber(paymentValue),
        description: `Pagamento: ${transaction.description}`,
        transactionDate: paymentDate?.toISOString().split('T')[0],
        bankAccountId: selectedAccount,
        categoryId: transaction.categoryId,
        entityId: transaction.entityId,
        projectId: transaction.projectId,
        paymentMethodId: transaction.paymentMethodId,
        accountsPayableId: transaction.id,
        notes: `Pagamento com juros de ${interestValue} e desconto de ${discountValue}. Valor final: ${formatCurrency(finalValue)}`
      });

      toast.success("Pagamento registrado com sucesso!");
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao registrar pagamento:", error);
      toast.error("Erro ao registrar pagamento. Tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Registrar Pagamento</DialogTitle>
          <p className="text-muted-foreground mt-1">
            Informe o valor, a conta e a data do pagamento.
          </p>
        </DialogHeader>

        <div className="py-3">
          <h2 className="text-xl font-semibold">{transaction?.description || "Pagamento"}</h2>
          <p className="text-muted-foreground">
            Valor total: {formatCurrency(transaction?.amount || 0)}
          </p>
        </div>

        <div className="space-y-6 pt-3">
          <TransactionAmountField
            id="paymentValue"
            label="Valor do pagamento"
            value={paymentValue}
            onChange={setPaymentValue}
            isMainAmount={true}
          />

          <div className="grid grid-cols-2 gap-4">
            <TransactionAmountField
              id="interestValue"
              label="Juros (R$)"
              value={interestValue}
              onChange={setInterestValue}
            />
            <TransactionAmountField
              id="discountValue"
              label="Descontos (R$)"
              value={discountValue}
              onChange={setDiscountValue}
            />
          </div>

          <TransactionFinalValueDisplay
            finalValue={finalValue}
            isPayment={true}
            description="Valor do Pagamento + Juros - Descontos"
          />

          <TransactionAccountSelector
            accounts={accounts}
            selectedAccount={selectedAccount}
            onAccountChange={setSelectedAccount}
          />
          {isLoading && <div className="text-sm text-gray-500 mt-1">Carregando contas bancárias...</div>}
          {!isLoading && accounts.length === 0 && (
            <div className="text-sm text-gray-500 mt-1">Nenhuma conta bancária encontrada</div>
          )}

          <TransactionDateSelector
            id="paymentDate"
            label="Data do pagamento"
            date={paymentDate}
            onDateChange={setPaymentDate}
          />

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancelar
            </Button>
            <Button
              className="bg-[#007FFF] hover:bg-[#0066CC]"
              onClick={handleConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Processando..." : "Confirmar Pagamento"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
