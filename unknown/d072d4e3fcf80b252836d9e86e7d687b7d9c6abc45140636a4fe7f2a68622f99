services:
  backend:
    build:
      context: ./backend
    ports:
      - "3000:3000"
    restart: unless-stopped
    init: true
    env_file:
      - ./backend/.env
    depends_on:
      - database
  frontend:
    build:
      context: ./frontend
    ports:
      - "3001:3001"
    restart: unless-stopped
    init: true
    depends_on:
      - backend
  database:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: fluxomax
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    restart: unless-stopped
volumes:
  db_data: