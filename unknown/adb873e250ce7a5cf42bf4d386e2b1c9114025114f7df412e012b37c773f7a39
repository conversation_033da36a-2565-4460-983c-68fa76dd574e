// backend/src/routes/entities/dto/update-entity-address.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUUID,
  IsBoolean,
  Length,
  Matches,
} from 'class-validator';

export class UpdateEntityAddressDto {
  @ApiProperty({
    description: 'ID do tipo de endereço (opcional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID(4, { message: 'ID do tipo de endereço inválido' })
  @IsOptional()
  addressTypeId?: string;

  @ApiProperty({
    description: 'Rua/Logradouro',
    example: 'Avenida Paulista',
    required: false,
  })
  @IsString()
  @IsOptional()
  street?: string;

  @ApiProperty({ description: 'Número', example: '1000', required: false })
  @IsString()
  @IsOptional()
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    required: false,
  })
  @IsString()
  @IsOptional()
  complement?: string;

  @ApiProperty({
    description: 'Bairro',
    example: 'Bela Vista',
    required: false,
  })
  @IsString()
  @IsOptional()
  district?: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo', required: false })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  state?: string;

  @ApiProperty({ description: 'CEP', example: '01310-100', required: false })
  @IsString()
  @IsOptional()
  @Matches(/^\d{5}-\d{3}$/, {
    message: 'CEP inválido. Use o formato: XXXXX-XXX',
  })
  zipCode?: string;

  @ApiProperty({
    description: 'Endereço padrão',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}
