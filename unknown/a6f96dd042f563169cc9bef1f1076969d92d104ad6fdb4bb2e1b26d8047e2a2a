
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface IconButtonProps {
  icon: ReactNode;
  onClick?: () => void;
  className?: string;
  label?: string;
  variant?: "primary" | "secondary" | "ghost" | "glass";
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
}

const IconButton = ({
  icon,
  onClick,
  className,
  label,
  variant = "primary",
  size = "md",
  disabled = false,
}: IconButtonProps) => {
  const sizeClass = {
    sm: "p-1.5 text-sm",
    md: "p-2 text-base",
    lg: "p-3 text-lg",
  };

  const variantClass = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    ghost: "hover:bg-secondary dark:hover:bg-dark-card-light text-foreground",
    glass: "button-glass text-foreground",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "rounded-full flex items-center justify-center transition-all duration-300",
        sizeClass[size],
        variantClass[variant],
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      aria-label={label}
      title={label}
    >
      {icon}
    </button>
  );
};

export default IconButton;
