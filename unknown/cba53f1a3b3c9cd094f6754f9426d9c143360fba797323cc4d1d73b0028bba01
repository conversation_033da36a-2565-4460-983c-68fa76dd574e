import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { AssignPermissionsDto, RemovePermissionsDto } from './dto/role-permission.dto';
import { PermissionDto } from '../permissions/dto/permission.dto';

@Injectable()
export class RolePermissionsService {
  private readonly logger = new Logger(RolePermissionsService.name);

  constructor(private prisma: PrismaService) {}

  async getRolePermissions(
    companyId: string,
    roleId: string,
  ): Promise<PermissionDto[]> {
    try {
      // Verificar se o papel existe e pertence à empresa
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Buscar as permissões do papel
      const permissions = await this.prisma.permission.findMany({
        where: {
          rolePermissions: {
            some: {
              roleId,
            },
          },
          companyId,
        },
      });

      return permissions.map((permission) => ({
        id: permission.id,
        companyId: permission.companyId,
        action: permission.action,
        description: permission.description || undefined,
        systemPermissionId: permission.systemPermissionId || undefined,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt,
      }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar permissões do papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async assignPermissions(
    companyId: string,
    roleId: string,
    assignPermissionsDto: AssignPermissionsDto,
  ): Promise<void> {
    try {
      // Verificar se o papel existe e pertence à empresa
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Verificar se todas as permissões existem e pertencem à empresa
      const permissions = await this.prisma.permission.findMany({
        where: {
          id: { in: assignPermissionsDto.permissionIds },
          companyId,
        },
      });

      if (permissions.length !== assignPermissionsDto.permissionIds.length) {
        throw new NotFoundException(
          'Uma ou mais permissões não foram encontradas ou não pertencem à empresa',
        );
      }

      // Usar transação para garantir consistência
      await this.prisma.$transaction(async (prisma) => {
        // Criar as associações entre papel e permissões
        for (const permissionId of assignPermissionsDto.permissionIds) {
          // Verificar se a associação já existe
          const existingAssociation = await prisma.rolePermission.findUnique({
            where: {
              roleId_permissionId: {
                roleId,
                permissionId,
              },
            },
          });

          if (!existingAssociation) {
            await prisma.rolePermission.create({
              data: {
                roleId,
                permissionId,
              },
            });
          }
        }
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao atribuir permissões ao papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async removePermissions(
    companyId: string,
    roleId: string,
    removePermissionsDto: RemovePermissionsDto,
  ): Promise<void> {
    try {
      // Verificar se o papel existe e pertence à empresa
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Usar transação para garantir consistência
      await this.prisma.$transaction(async (prisma) => {
        // Remover as associações entre papel e permissões
        for (const permissionId of removePermissionsDto.permissionIds) {
          await prisma.rolePermission.deleteMany({
            where: {
              roleId,
              permissionId,
            },
          });
        }
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao remover permissões do papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
