-- Adicionar permissões do sistema para o módulo de agendamentos recorrentes
INSERT INTO system_permissions (id, code, name, description, module, created_at, updated_at)
VALUES
(gen_random_uuid(), 'recurring-schedules.view', 'Visualizar agendamentos recorrentes', 'Permite visualizar agendamentos recorrentes', 'recurring-schedules', NOW(), NOW()),
(gen_random_uuid(), 'recurring-schedules.create', 'Criar agendamentos recorrentes', 'Permite criar novos agendamentos recorrentes', 'recurring-schedules', NOW(), NOW()),
(gen_random_uuid(), 'recurring-schedules.update', 'Atualizar agendamentos recorrentes', 'Permite atualizar agendamentos recorrentes existentes', 'recurring-schedules', NOW(), NOW()),
(gen_random_uuid(), 'recurring-schedules.delete', 'Remover agendamentos recorrentes', 'Permite remover agendamentos recorrentes', 'recurring-schedules', NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    module = EXCLUDED.module,
    updated_at = NOW();

-- Criar permissões para as empresas existentes
DO $$
DECLARE
    v_company_record RECORD;
    v_system_permission_record RECORD;
    v_permission_id UUID;
    v_role_admin_id UUID;
    v_role_member_id UUID;
BEGIN
    -- Para cada empresa
    FOR v_company_record IN SELECT id FROM companies LOOP
        -- Para cada permissão do sistema de agendamentos recorrentes, criar uma permissão da empresa
        FOR v_system_permission_record IN 
            SELECT id, code, description 
            FROM system_permissions 
            WHERE module = 'recurring-schedules'
        LOOP
            -- Inserir a permissão para a empresa
            INSERT INTO permissions (
                id,
                company_id,
                action,
                description,
                system_permission_id,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                v_company_record.id,
                v_system_permission_record.code,
                v_system_permission_record.description,
                v_system_permission_record.id,
                NOW(),
                NOW()
            )
            ON CONFLICT DO NOTHING
            RETURNING id INTO v_permission_id;

            -- Obter o ID do papel Admin para esta empresa
            SELECT id INTO v_role_admin_id FROM roles
            WHERE company_id = v_company_record.id AND (name = 'Admin' OR name = 'Administrador')
            LIMIT 1;

            -- Se encontrou o papel Admin, associar todas as permissões a ele
            IF v_role_admin_id IS NOT NULL AND v_permission_id IS NOT NULL THEN
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES (v_role_admin_id, v_permission_id)
                ON CONFLICT DO NOTHING;
            END IF;

            -- Para o papel Member, associar apenas permissões de visualização
            IF v_system_permission_record.code LIKE '%.view' THEN
                -- Obter o ID do papel Member para esta empresa
                SELECT id INTO v_role_member_id FROM roles
                WHERE company_id = v_company_record.id AND name = 'Member'
                LIMIT 1;

                -- Se encontrou o papel Member, associar permissões de visualização a ele
                IF v_role_member_id IS NOT NULL AND v_permission_id IS NOT NULL THEN
                    INSERT INTO role_permissions (role_id, permission_id)
                    VALUES (v_role_member_id, v_permission_id)
                    ON CONFLICT DO NOTHING;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$;
