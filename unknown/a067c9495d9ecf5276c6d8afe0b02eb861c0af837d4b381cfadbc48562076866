import { Module } from '@nestjs/common';
import { SystemPermissionsService } from './system-permissions.service';
import { SystemPermissionsController } from './system-permissions.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [SystemPermissionsController],
  providers: [SystemPermissionsService],
  exports: [SystemPermissionsService],
})
export class SystemPermissionsModule {}
