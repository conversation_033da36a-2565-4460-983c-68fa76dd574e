
import { Button } from "@/components/ui/button";

interface AccountsReceivableStatusFilterProps {
  filter: string;
  onFilterChange: (filter: string) => void;
}

export default function AccountsReceivableStatusFilter({
  filter,
  onFilterChange,
}: AccountsReceivableStatusFilterProps) {
  return (
    <>
      <Button 
        variant={filter === "all" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("all")}
      >
        Todas
      </Button>
      <Button 
        variant={filter === "pending" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("pending")}
      >
        Pendentes
      </Button>
      <Button 
        variant={filter === "overdue" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("overdue")}
      >
        Em Atraso
      </Button>
      <Button 
        variant={filter === "partial" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("partial")}
      >
        Parciais
      </Button>
      <Button 
        variant={filter === "received" ? "default" : "outline"} 
        size="sm"
        onClick={() => onFilterChange("received")}
      >
        Recebidas
      </Button>
    </>
  );
}
