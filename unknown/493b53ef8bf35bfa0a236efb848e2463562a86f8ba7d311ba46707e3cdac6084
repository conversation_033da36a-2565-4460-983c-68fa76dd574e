import { ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../services/prisma.service';

/**
 * Utilitários para controle de acesso
 */
export class AccessControlUtil {
  constructor(private prisma: PrismaService) {}

  /**
   * Verifica se um usuário tem acesso a uma empresa
   * @param userId ID do usuário
   * @param companyId ID da empresa
   * @returns true se o usuário tem acesso, false caso contrário
   */
  async checkUserHasAccessToCompany(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    const hasAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND ucr.deleted_at IS NULL
    `;
    
    return parseInt(hasAccess[0].count, 10) > 0;
  }

  /**
   * Verifica se um usuário é administrador de uma empresa
   * @param userId ID do usuário
   * @param companyId ID da empresa
   * @returns true se o usuário é administrador, false caso contrário
   */
  async checkUserIsCompanyAdmin(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
    `;
    
    return parseInt(hasAdminAccess[0].count, 10) > 0;
  }

  /**
   * Verifica se um usuário tem acesso a uma empresa e lança uma exceção se não tiver
   * @param userId ID do usuário
   * @param companyId ID da empresa
   * @throws ForbiddenException se o usuário não tiver acesso
   */
  async validateUserHasAccessToCompany(
    userId: string,
    companyId: string,
  ): Promise<void> {
    const hasAccess = await this.checkUserHasAccessToCompany(userId, companyId);
    
    if (!hasAccess) {
      throw new ForbiddenException('Você não tem acesso a esta empresa');
    }
  }

  /**
   * Verifica se um usuário é administrador de uma empresa e lança uma exceção se não for
   * @param userId ID do usuário
   * @param companyId ID da empresa
   * @throws ForbiddenException se o usuário não for administrador
   */
  async validateUserIsCompanyAdmin(
    userId: string,
    companyId: string,
  ): Promise<void> {
    const isAdmin = await this.checkUserIsCompanyAdmin(userId, companyId);
    
    if (!isAdmin) {
      throw new ForbiddenException('Você não tem permissão de administrador nesta empresa');
    }
  }
} 