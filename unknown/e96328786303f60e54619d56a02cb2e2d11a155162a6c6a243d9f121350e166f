import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ZipCodesService } from './zip-codes.service';
import { ZipCodeDto, CreateZipCodeDto, SearchAddressDto } from './dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery
} from '@nestjs/swagger';

@ApiTags('zip-codes')
@Controller('zip-codes')
export class ZipCodesController {
  constructor(private readonly zipCodesService: ZipCodesService) {}

  @Get(':cep')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar CEP' })
  @ApiParam({ name: 'cep', description: 'CEP a ser buscado', example: '01310100' })
  @ApiResponse({
    status: 200,
    description: 'CEP encontrado com sucesso',
    type: ZipCodeDto,
  })
  @ApiResponse({ status: 404, description: 'CEP não encontrado' })
  async findByCep(@Param('cep') cep: string): Promise<ZipCodeDto> {
    return this.zipCodesService.findByCep(cep);
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Cadastrar CEP manualmente' })
  @ApiBody({ type: CreateZipCodeDto })
  @ApiResponse({
    status: 201,
    description: 'CEP cadastrado com sucesso',
    type: ZipCodeDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async create(@Body() createZipCodeDto: CreateZipCodeDto): Promise<ZipCodeDto> {
    return this.zipCodesService.createManual(createZipCodeDto);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar CEPs por endereço' })
  @ApiQuery({ name: 'street', description: 'Logradouro', example: 'Avenida Paulista', required: true })
  @ApiQuery({ name: 'city', description: 'Cidade', example: 'São Paulo', required: true })
  @ApiQuery({ name: 'state', description: 'Estado (UF)', example: 'SP', required: true })
  @ApiResponse({
    status: 200,
    description: 'CEPs encontrados com sucesso',
    type: [ZipCodeDto],
  })
  @ApiResponse({ status: 404, description: 'Nenhum CEP encontrado' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async findByAddress(
    @Query('street') street: string,
    @Query('city') city: string,
    @Query('state') state: string,
  ): Promise<ZipCodeDto[]> {
    const searchDto: SearchAddressDto = { street, city, state };
    return this.zipCodesService.findByAddress(searchDto);
  }
}
