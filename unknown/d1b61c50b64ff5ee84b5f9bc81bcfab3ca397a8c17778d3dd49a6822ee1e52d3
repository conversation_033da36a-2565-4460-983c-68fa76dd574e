import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsDateString,
  IsNumber,
  IsPositive,
  IsOptional,
  IsEnum,
  IsBoolean,
  Min,
  Max,
  <PERSON>ength,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum ReferenceTableEnum {
  ACCOUNTS_PAYABLE = 'accounts_payable',
  ACCOUNTS_RECEIVABLE = 'accounts_receivable',
}

export class CreateRecurringScheduleDto {
  @ApiProperty({
    description: 'ID do tipo de recorrência',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  recurrenceTypeId: string;

  @ApiPropertyOptional({
    description: 'ID da entidade (cliente/fornecedor)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;

  @ApiProperty({
    description: 'Descrição do agendamento recorrente',
    example: 'Mensalidade do Serviço XYZ',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  description: string;

  @ApiProperty({
    description: 'Tabela de referência (accounts_payable ou accounts_receivable)',
    enum: ReferenceTableEnum,
    example: 'accounts_payable',
  })
  @IsEnum(ReferenceTableEnum)
  referenceTable: ReferenceTableEnum;

  @ApiPropertyOptional({
    description: 'ID da primeira instância criada (opcional)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  referenceId?: string;

  @ApiPropertyOptional({
    description: 'Dia do mês para recorrências mensais ou superiores (1-31)',
    example: 15,
    minimum: 1,
    maximum: 31,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(31)
  dayOfMonth?: number;

  @ApiPropertyOptional({
    description: 'Dia da semana para recorrências semanais (0-6, onde 0=Domingo)',
    example: 1,
    minimum: 0,
    maximum: 6,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(6)
  dayOfWeek?: number;

  @ApiProperty({
    description: 'Data de início do agendamento',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsDateString()
  startDate: string;

  @ApiPropertyOptional({
    description: 'Data de término do agendamento (opcional)',
    example: '2025-12-31T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Valor do agendamento',
    example: 1500.50,
    minimum: 0.01,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Type(() => Number)
  amount: number;

  @ApiProperty({
    description: 'ID da moeda',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  currencyId: string;

  @ApiPropertyOptional({
    description: 'ID da categoria',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'ID do projeto',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  projectId?: string;

  @ApiPropertyOptional({
    description: 'ID do método de pagamento',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'ID da conta bancária',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  bankAccountId?: string;

  // nextGenerationDate e active são gerenciados pelo serviço
}
