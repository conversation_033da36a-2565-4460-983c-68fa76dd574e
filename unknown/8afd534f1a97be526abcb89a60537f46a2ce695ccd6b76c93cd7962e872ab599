import api from './axios';
import { 
  CashFlowReport, 
  BalanceSheetReport, 
  IncomeStatementReport, 
  ApiResponse 
} from '@/types/api';

export const reportService = {
  // Obter relatório de fluxo de caixa
  getCashFlowReport: async (
    startDate: string,
    endDate: string,
    companyId: string,
    bankAccountIds?: string[],
    categoryIds?: string[],
    periodType?: 'daily' | 'weekly' | 'monthly'
  ): Promise<CashFlowReport> => {
    const params = { startDate, endDate, companyId, bankAccountIds, categoryIds, periodType };
    const response = await api.get<ApiResponse<CashFlowReport>>('/reports/cash-flow', { params });
    return response.data.data;
  },
  
  // Obter balanço patrimonial
  getBalanceSheetReport: async (
    date: string,
    companyId: string
  ): Promise<BalanceSheetReport> => {
    const params = { date, companyId };
    const response = await api.get<ApiResponse<BalanceSheetReport>>('/reports/balance-sheet', { params });
    return response.data.data;
  },
  
  // Obter demonstração de resultados
  getIncomeStatementReport: async (
    startDate: string,
    endDate: string,
    companyId: string,
    compareWithPreviousPeriod?: boolean
  ): Promise<IncomeStatementReport> => {
    const params = { startDate, endDate, companyId, compareWithPreviousPeriod };
    const response = await api.get<ApiResponse<IncomeStatementReport>>('/reports/income-statement', { params });
    return response.data.data;
  }
};
