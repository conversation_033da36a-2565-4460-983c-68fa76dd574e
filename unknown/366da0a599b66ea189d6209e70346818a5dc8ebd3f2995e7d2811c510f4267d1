// backend/src/routes/accounts-payable/accounts-payable.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req, // To access the request object for user info
  Query, // To handle query parameters for filtering/pagination
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { AccountsPayableService } from './accounts-payable.service';
import { CreateAccountPayableDto } from './dto/create-account-payable.dto';
import { UpdateAccountPayableDto } from './dto/update-account-payable.dto';
import { AccountsPayableSummaryDto } from './dto/accounts-payable-summary.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  Api<PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>pi<PERSON>ody,
} from '@nestjs/swagger';
// Import RBAC guards if you have them (e.g., RolesGuard, PermissionsGuard)
// import { RolesGuard } from '../../guards/roles.guard';
// import { Roles } from '../../decorators/roles.decorator';
// import { Role } from '../../enums/role.enum'; // Assuming you have Role enum

@ApiTags('accounts-payable')
@Controller('accounts-payable')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
// Apply RBAC guards globally or per route if needed
// @UseGuards(RolesGuard)
export class AccountsPayableController {
  constructor(private readonly accountsPayableService: AccountsPayableService) {}

  @Post()
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova conta a pagar' })
  @ApiBody({ type: CreateAccountPayableDto })
  @ApiResponse({
    status: 201,
    description: 'Conta a pagar criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createAccountPayableDto: CreateAccountPayableDto,
    @Req() req: RequestWithUser,
  ) {
    // Pass the authenticated user (including companyId) to the service
    return this.accountsPayableService.create(createAccountPayableDto, req.user);
  }

  // New endpoint for summary data
  @Get('summary')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter resumo das contas a pagar (Total, Vencidas, A Vencer)' })
  @ApiResponse({ status: 200, description: 'Resumo das contas a pagar', type: AccountsPayableSummaryDto })
  getSummary(@Req() req: RequestWithUser) {
    return this.accountsPayableService.getSummary(req.user);
  }

  @Get()
  // @Roles(Role.Admin, Role.FinanceManager, Role.FinanceViewer) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as contas a pagar' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página (padrão: 10)' })
  @ApiQuery({ name: 'status', required: false, description: 'Filtrar por status (pending, paid, partially_paid, overdue, cancelled)' })
  @ApiQuery({ name: 'dueDateFrom', required: false, description: 'Data de vencimento inicial (formato: YYYY-MM-DD)' })
  @ApiQuery({ name: 'dueDateTo', required: false, description: 'Data de vencimento final (formato: YYYY-MM-DD)' })
  @ApiQuery({ name: 'entityId', required: false, description: 'ID da entidade (fornecedor)' })
  @ApiResponse({ status: 200, description: 'Lista de contas a pagar' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string,
    @Query('dueDateFrom') dueDateFrom?: string,
    @Query('dueDateTo') dueDateTo?: string,
    @Query('entityId') entityId?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
        throw new BadRequestException('Invalid page number');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
        throw new BadRequestException('Invalid limit number');
    }

    return this.accountsPayableService.findAll(req.user, {
        page: pageNumber,
        limit: limitNumber,
        status,
        dueDateFrom,
        dueDateTo,
        entityId
    });
  }

  @Get(':id')
  // @Roles(Role.Admin, Role.FinanceManager, Role.FinanceViewer) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma conta a pagar pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da conta a pagar' })
  @ApiResponse({ status: 200, description: 'Conta a pagar encontrada' })
  @ApiResponse({ status: 404, description: 'Conta a pagar não encontrada' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsPayableService.findOne(id, req.user);
  }

  @Put(':id')
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma conta a pagar' })
  @ApiParam({ name: 'id', description: 'ID da conta a pagar' })
  @ApiBody({ type: UpdateAccountPayableDto })
  @ApiResponse({ status: 200, description: 'Conta a pagar atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Conta a pagar não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAccountPayableDto: UpdateAccountPayableDto,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsPayableService.update(id, updateAccountPayableDto, req.user);
  }

  @Delete(':id')
  // @Roles(Role.Admin, Role.FinanceManager) // Example RBAC
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma conta a pagar' })
  @ApiParam({ name: 'id', description: 'ID da conta a pagar' })
  @ApiResponse({ status: 204, description: 'Conta a pagar removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Conta a pagar não encontrada' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.accountsPayableService.remove(id, req.user);
  }

  // Endpoint /pay removido para manter consistência com o módulo de contas a receber
  // A ação de pagar agora deve ser feita criando uma Transaction do tipo "expense" vinculada à conta a pagar
}
