
import { useState } from "react";
import { Search, X, FilterX } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import EntitySearchFilter from "./EntitySearchFilter";

interface TransactionFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  selectedAccountId: string;
  onAccountChange: (accountId: string) => void;
  selectedEntityId: string;
  onEntityChange: (entityId: string) => void;
  onClearFilters: () => void;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  onDateRangeChange?: (startDate?: Date, endDate?: Date) => void;
}

export default function TransactionFilters({
  searchTerm,
  setSearchTerm,
  selectedPeriod,
  onPeriodChange,
  selectedAccountId,
  onAccountChange,
  selectedEntityId,
  onEntityChange,
  onClearFilters,
  customDateRange,
  onDateRangeChange
}: TransactionFiltersProps) {
  return (
    <div className="flex flex-wrap gap-2 mb-6 items-center">
      <EntitySearchFilter
        selectedEntityId={selectedEntityId}
        onEntityChange={onEntityChange}
      />
      
      <div className="relative min-w-[200px] max-w-[300px] flex-grow md:flex-grow-0">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input 
          placeholder="Pesquisar transações..." 
          className="pl-10 pr-9"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        {searchTerm && (
          <button
            className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
            onClick={() => setSearchTerm("")}
            aria-label="Limpar pesquisa"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      
      <Button 
        variant="outline" 
        size="sm" 
        onClick={onClearFilters}
        className="ml-auto h-9 gap-2 bg-white dark:bg-dark-card shadow-sm dark:text-gray-200 dark:border-dark-card-light"
      >
        <FilterX className="h-4 w-4" strokeWidth={2.5} />
        <span>Limpar</span>
      </Button>
    </div>
  );
}
