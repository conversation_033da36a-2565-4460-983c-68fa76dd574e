import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { PaginatedData } from '@/components/ui/data-pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

export interface ColumnDef<T> {
  accessorKey: string;
  header: string;
  cell?: (item: T) => React.ReactNode;
  enableSorting?: boolean;
  sortKey?: string;
  className?: string;
  headerClassName?: string;
}

interface DataTableProps<T> {
  columns: ColumnDef<T>[];
  data: {
    data: T[];
    meta: {
      currentPage: number;
      itemsPerPage: number;
      totalItems: number;
      totalPages: number;
    };
  } | null;
  loading: boolean;
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  onSearchChange?: (search: string) => void;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  searchPlaceholder?: string;
  noDataMessage?: string;
  className?: string;
  rowClassName?: (item: T, index: number) => string;
  onRowClick?: (item: T) => void;
  showSearch?: boolean;
  searchValue?: string;
  searchDebounce?: number;
  itemsPerPageOptions?: number[];
}

export function DataTable<T>({
  columns,
  data,
  loading,
  onPageChange,
  onItemsPerPageChange,
  onSortChange,
  onSearchChange,
  sortBy,
  sortOrder = 'asc',
  searchPlaceholder = 'Pesquisar...',
  noDataMessage = 'Nenhum dado encontrado',
  className,
  rowClassName,
  onRowClick,
  showSearch = true,
  searchValue: externalSearchValue,
  searchDebounce = 300,
  itemsPerPageOptions,
}: DataTableProps<T>) {
  // Estado interno para pesquisa (se não for controlado externamente)
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Determinar se o valor de pesquisa é controlado externamente ou internamente
  const isSearchControlled = externalSearchValue !== undefined;
  const searchValue = isSearchControlled ? externalSearchValue : internalSearchValue;
  
  // Manipulador para alteração de pesquisa
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Atualizar o estado interno se não for controlado externamente
    if (!isSearchControlled) {
      setInternalSearchValue(value);
    }
    
    // Limpar o timeout anterior se existir
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Configurar um novo timeout para debounce
    if (onSearchChange) {
      const timeout = setTimeout(() => {
        onSearchChange(value);
      }, searchDebounce);
      
      setSearchTimeout(timeout);
    }
  };
  
  // Limpar pesquisa
  const handleClearSearch = () => {
    if (!isSearchControlled) {
      setInternalSearchValue('');
    }
    
    if (onSearchChange) {
      onSearchChange('');
    }
  };
  
  // Manipulador para ordenação
  const handleSort = (column: ColumnDef<T>) => {
    if (!column.enableSorting || !onSortChange) return;
    
    const sortKey = column.sortKey || column.accessorKey;
    
    if (sortBy === sortKey) {
      // Alternar a ordem se a coluna já estiver selecionada
      onSortChange(sortKey, sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Definir nova coluna para ordenação com ordem padrão ascendente
      onSortChange(sortKey, 'asc');
    }
  };
  
  // Renderizar ícone de ordenação
  const renderSortIcon = (column: ColumnDef<T>) => {
    if (!column.enableSorting) return null;
    
    const sortKey = column.sortKey || column.accessorKey;
    
    if (sortBy === sortKey) {
      return sortOrder === 'asc' ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
    }
    
    return <ArrowUpDown className="ml-2 h-4 w-4" />;
  };
  
  // Renderizar esqueletos de carregamento
  const renderSkeletons = () => {
    const skeletonCount = data?.meta.itemsPerPage || 5;
    
    return Array.from({ length: skeletonCount }).map((_, index) => (
      <TableRow key={`skeleton-${index}`}>
        {columns.map((column, colIndex) => (
          <TableCell key={`skeleton-cell-${colIndex}`}>
            <Skeleton className="h-6 w-full" />
          </TableCell>
        ))}
      </TableRow>
    ));
  };
  
  // Renderizar mensagem de "nenhum dado"
  const renderNoData = () => (
    <TableRow>
      <TableCell colSpan={columns.length} className="h-24 text-center">
        {noDataMessage}
      </TableCell>
    </TableRow>
  );
  
  return (
    <div className={cn("space-y-4", className)}>
      {showSearch && onSearchChange && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={searchPlaceholder}
              className="pl-8 pr-10"
              value={searchValue}
              onChange={handleSearchChange}
            />
            {searchValue && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Limpar pesquisa</span>
              </Button>
            )}
          </div>
        </div>
      )}
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead
                  key={`header-${index}`}
                  className={cn(
                    column.enableSorting && onSortChange ? "cursor-pointer select-none" : "",
                    column.headerClassName
                  )}
                  onClick={() => column.enableSorting && onSortChange && handleSort(column)}
                >
                  <div className="flex items-center">
                    {column.header}
                    {column.enableSorting && onSortChange && renderSortIcon(column)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              renderSkeletons()
            ) : !data || data.data.length === 0 ? (
              renderNoData()
            ) : (
              data.data.map((item, index) => (
                <TableRow
                  key={`row-${index}`}
                  className={cn(
                    onRowClick && "cursor-pointer hover:bg-muted/50",
                    rowClassName && rowClassName(item, index)
                  )}
                  onClick={() => onRowClick && onRowClick(item)}
                >
                  {columns.map((column, colIndex) => (
                    <TableCell key={`cell-${index}-${colIndex}`} className={column.className}>
                      {column.cell ? column.cell(item) : (item as any)[column.accessorKey]}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {data && (
        <PaginatedData
          data={data}
          loading={loading}
          onPageChange={onPageChange}
          onItemsPerPageChange={onItemsPerPageChange}
          itemsPerPageOptions={itemsPerPageOptions}
        />
      )}
    </div>
  );
}
