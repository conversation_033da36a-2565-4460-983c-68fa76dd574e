import { useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '@/services/api';
import { LoginRequest, RegisterRequest, RefreshTokenRequest } from '@/types/api';
import { toast } from 'sonner';

export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: LoginRequest) => authService.login(data),
    onSuccess: (data) => {
      // Armazenar tokens
      localStorage.setItem('accessToken', data.accessToken);
      localStorage.setItem('refreshToken', data.refreshToken);
      
      // Atualizar cache de usuário
      queryClient.setQueryData(['userProfile'], data.user);
      
      toast.success('Login realizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha no login. Verifique suas credenciais.'
      );
    }
  });
};

export const useRegister = () => {
  return useMutation({
    mutationFn: (data: RegisterRequest) => authService.register(data),
    onSuccess: () => {
      toast.success('Conta criada com sucesso! Faça login para continuar.');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar conta. Por favor, tente novamente.'
      );
    }
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: RefreshTokenRequest) => authService.logout(data),
    onSuccess: () => {
      // Limpar tokens
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      
      // Limpar cache
      queryClient.clear();
      
      toast.success('Logout realizado com sucesso!');
    },
    onError: () => {
      // Mesmo em caso de erro, limpar localmente
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      queryClient.clear();
      
      toast.success('Logout realizado com sucesso!');
    }
  });
};
