
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FileText, RefreshCcw } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { TransactionFormValues, RefreshableField } from "../types/TransactionModalTypes";
import { paymentMethods, recurrenceOptions } from "../data/transactionModalData";

interface TransactionPaymentSectionProps {
  form: UseFormReturn<TransactionFormValues>;
  transactionType: "accounts_receivable" | "accounts_payable" | "transfer";
  refreshingFields: Record<RefreshableField, boolean>;
  onRefresh: (field: RefreshableField) => void;
}

export default function TransactionPaymentSection({ 
  form, 
  transactionType,
  refreshingFields,
  onRefresh
}: TransactionPaymentSectionProps) {
  if (transactionType === "transfer") {
    return null;
  }

  return (
    <>
      <FormField
        control={form.control}
        name="paymentMethod"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center justify-between">
              <FormLabel>Método de Pagamento</FormLabel>
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-7 w-7" 
                onClick={() => onRefresh("paymentMethod")}
                disabled={refreshingFields.paymentMethod}
              >
                <RefreshCcw className={`h-4 w-4 ${refreshingFields.paymentMethod ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <Select 
              onValueChange={field.onChange}
              value={field.value || ""}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o método de pagamento" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {paymentMethods.map((method) => (
                  <SelectItem key={method.id} value={method.id}>
                    {method.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="recurrence"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center justify-between">
              <FormLabel>Recorrência</FormLabel>
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-7 w-7" 
                onClick={() => onRefresh("recurrence")}
                disabled={refreshingFields.recurrence}
              >
                <RefreshCcw className={`h-4 w-4 ${refreshingFields.recurrence ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <Select 
              onValueChange={field.onChange}
              value={field.value || "none"}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a recorrência" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {recurrenceOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="invoiceNumber"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Fatura
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="Número da fatura" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
}
