// backend/src/routes/banks/banks.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateBankDto } from './dto/create-bank.dto';
import { UpdateBankDto } from './dto/update-bank.dto';
import { Bank } from '@prisma/client';

@Injectable()
export class BanksService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new bank
   */
  async create(createBankDto: CreateBankDto): Promise<Bank> {
    try {
      // Check if a bank with the same code already exists
      const existingBank = await this.prisma.bank.findUnique({
        where: { code: createBankDto.code },
      });

      if (existingBank) {
        throw new BadRequestException(`Bank with code ${createBankDto.code} already exists`);
      }

      // Create the bank
      return await this.prisma.bank.create({
        data: createBankDto,
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error creating bank:', error);
      throw new InternalServerErrorException('Could not create bank');
    }
  }

  /**
   * Find all banks with pagination
   */
  async findAll(
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: Bank[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    try {
      // Get total count
      const total = await this.prisma.bank.count();

      // Get banks with pagination
      const banks = await this.prisma.bank.findMany({
        skip,
        take: limit,
        orderBy: { name: 'asc' },
      });

      return {
        data: banks,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding banks:', error);
      throw new InternalServerErrorException('Could not retrieve banks');
    }
  }

  /**
   * Find one bank by id
   */
  async findOne(id: string): Promise<Bank> {
    try {
      const bank = await this.prisma.bank.findUnique({
        where: { id },
      });

      if (!bank) {
        throw new NotFoundException(`Bank with ID ${id} not found`);
      }

      return bank;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding bank with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve bank');
    }
  }

  /**
   * Update a bank
   */
  async update(id: string, updateBankDto: UpdateBankDto): Promise<Bank> {
    try {
      // Check if bank exists
      await this.findOne(id);

      // If updating code, check if the new code already exists for another bank
      if (updateBankDto.code) {
        const existingBank = await this.prisma.bank.findUnique({
          where: { code: updateBankDto.code },
        });

        if (existingBank && existingBank.id !== id) {
          throw new BadRequestException(`Bank with code ${updateBankDto.code} already exists`);
        }
      }

      // Update the bank
      return await this.prisma.bank.update({
        where: { id },
        data: updateBankDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error updating bank with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update bank');
    }
  }

  /**
   * Remove a bank
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if bank exists
      await this.findOne(id);

      // Check if bank is being used by any bank account
      const bankAccounts = await this.prisma.bankAccount.findMany({
        where: { bankId: id },
        take: 1,
      });

      if (bankAccounts.length > 0) {
        throw new BadRequestException(
          'Cannot delete bank because it is being used by one or more bank accounts'
        );
      }

      // Delete the bank
      await this.prisma.bank.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error removing bank with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete bank');
    }
  }
}
