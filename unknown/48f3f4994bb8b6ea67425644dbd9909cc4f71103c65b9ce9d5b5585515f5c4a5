#!/bin/bash
set -e

# Path to the seed SQL file (relative to the script location inside the container)
# Assuming the script runs from the /app directory where package.json is
SEED_SQL_FILE="./prisma/seed.sql" # Using the existing seed file

# Extrair informações da variável DATABASE_URL
if [ -z "$DATABASE_URL" ]; then
  echo "Erro: A variável DATABASE_URL não está definida."
  exit 1
fi

echo "Seeding database usando DATABASE_URL..."

# Função para executar comandos SQL
run_sql() {
  local sql_file="$1"
  local sql_command="$2"

  # Usar diretamente o DATABASE_URL
  if [ -n "$sql_file" ]; then
    psql "$DATABASE_URL" -f "$sql_file"
  else
    psql "$DATABASE_URL" -c "$sql_command"
  fi
  return $?
}

# Executar o script SQL
run_sql "$SEED_SQL_FILE"

# Verificar se o seed foi bem-sucedido
if [ $? -eq 0 ]; then
  echo "✅ Database seeding completed successfully!"

  # Verificar a integridade dos dados
  echo "Verifying data integrity..."
  run_sql "" "SELECT COUNT(*) FROM address_types, companies, users, profiles"

  echo "✅ Verification completed. Database seeded successfully!"
else
  echo "❌ Error occurred during database seeding. Check the error message above."
  echo "This might happen if the tables are not available or if there are SQL syntax errors."
  exit 1
fi

echo "Database seeding completed."
