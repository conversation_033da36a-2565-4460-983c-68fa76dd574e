// backend/src/routes/address-types/address-types.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateAddressTypeDto } from './dto/create-address-type.dto';
import { UpdateAddressTypeDto } from './dto/update-address-type.dto';
import { AddressType } from '@prisma/client';

@Injectable()
export class AddressTypesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new address type
   */
  async create(createAddressTypeDto: CreateAddressTypeDto): Promise<AddressType> {
    try {
      // Check if an address type with the same name already exists
      const existingAddressType = await this.prisma.addressType.findUnique({
        where: { name: createAddressTypeDto.name },
      });

      if (existingAddressType) {
        throw new ConflictException(`Address type with name ${createAddressTypeDto.name} already exists`);
      }

      // Create the address type
      return await this.prisma.addressType.create({
        data: createAddressTypeDto,
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      console.error('Error creating address type:', error);
      throw new InternalServerErrorException('Could not create address type');
    }
  }

  /**
   * Find all address types with pagination
   */
  async findAll(
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: AddressType[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    try {
      // Get total count
      const total = await this.prisma.addressType.count();

      // Get address types with pagination
      const addressTypes = await this.prisma.addressType.findMany({
        skip,
        take: limit,
        orderBy: { name: 'asc' },
      });

      return {
        data: addressTypes,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error finding address types:', error);
      throw new InternalServerErrorException('Could not retrieve address types');
    }
  }

  /**
   * Find one address type by id
   */
  async findOne(id: string): Promise<AddressType> {
    try {
      const addressType = await this.prisma.addressType.findUnique({
        where: { id },
      });

      if (!addressType) {
        throw new NotFoundException(`Address type with ID ${id} not found`);
      }

      return addressType;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding address type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve address type');
    }
  }

  /**
   * Update an address type
   */
  async update(id: string, updateAddressTypeDto: UpdateAddressTypeDto): Promise<AddressType> {
    try {
      // Check if address type exists
      await this.findOne(id);

      // If updating name, check if the new name already exists for another address type
      if (updateAddressTypeDto.name) {
        const existingAddressType = await this.prisma.addressType.findUnique({
          where: { name: updateAddressTypeDto.name },
        });

        if (existingAddressType && existingAddressType.id !== id) {
          throw new ConflictException(`Address type with name ${updateAddressTypeDto.name} already exists`);
        }
      }

      // Update the address type
      return await this.prisma.addressType.update({
        where: { id },
        data: updateAddressTypeDto,
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.error(`Error updating address type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update address type');
    }
  }

  /**
   * Remove an address type
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if address type exists
      await this.findOne(id);

      // Check if address type is being used by any address
      const addresses = await this.prisma.address.findFirst({
        where: { addressTypeId: id },
      });

      if (addresses) {
        throw new BadRequestException(
          'Cannot delete address type because it is being used by one or more addresses'
        );
      }

      // Delete the address type
      await this.prisma.addressType.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error removing address type with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete address type');
    }
  }
}
