import React from 'react';
import { But<PERSON> } from "@/components/ui/button";

interface ActionButtonsProps {
  isReadOnly: boolean;
  isEditing: boolean;
  id?: string;
  onBack: () => void;
  onSubmit: () => void;
  onEdit: (id: string) => void;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  isReadOnly,
  isEditing,
  id,
  onBack,
  onSubmit,
  onEdit,
}) => {
  return (
    <div className="flex justify-end space-x-2">
      <Button variant="outline" onClick={onBack}>
        Cancelar
      </Button>
      
      {!isReadOnly && (
        <Button onClick={onSubmit}>
          {isEditing ? "Salvar <PERSON>eraç<PERSON>" : "Criar"}
        </Button>
      )}
      
      {isReadOnly && id && (
        <Button onClick={() => onEdit(id)}>
          Editar
        </Button>
      )}
    </div>
  );
};