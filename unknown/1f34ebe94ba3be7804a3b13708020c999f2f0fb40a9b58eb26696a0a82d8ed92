
import { Button } from "@/components/ui/button";
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from "@/components/ui/pagination";

interface BankPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const BankPagination = ({ 
  currentPage, 
  totalPages, 
  onPageChange 
}: BankPaginationProps) => {
  return (
    <Pagination className="mt-4">
      <PaginationContent>
        <PaginationItem>
          {currentPage === 1 ? (
            <Button 
              variant="outline" 
              size="icon" 
              disabled 
              className="opacity-50 cursor-not-allowed"
            >
              <span className="sr-only">Página anterior</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="m15 18-6-6 6-6" />
              </svg>
            </Button>
          ) : (
            <PaginationPrevious onClick={() => onPageChange(Math.max(1, currentPage - 1))} />
          )}
        </PaginationItem>
        
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <PaginationItem key={page}>
            <PaginationLink 
              onClick={() => onPageChange(page)}
              isActive={currentPage === page}
            >
              {page}
            </PaginationLink>
          </PaginationItem>
        ))}
        
        <PaginationItem>
          {currentPage === totalPages ? (
            <Button 
              variant="outline" 
              size="icon" 
              disabled 
              className="opacity-50 cursor-not-allowed"
            >
              <span className="sr-only">Próxima página</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="m9 18 6-6-6-6" />
              </svg>
            </Button>
          ) : (
            <PaginationNext onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))} />
          )}
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};
