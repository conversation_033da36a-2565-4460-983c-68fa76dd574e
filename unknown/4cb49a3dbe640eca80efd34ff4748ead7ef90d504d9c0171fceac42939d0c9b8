
import { Transaction, TransferTransaction } from "@/types/transaction";

interface TransactionAccountInfoProps {
  transaction: Transaction;
}

export const TransactionAccountInfo = ({ transaction }: TransactionAccountInfoProps) => {
  // Normalizar o tipo para lidar com diferentes formatos da API
  const normalizedType = transaction.type?.toLowerCase?.() || '';
  
  if (normalizedType.includes('transfer')) {
    // Tentar obter informações de conta de origem e destino
    const sourceAccount = transaction.bankAccount || 'Conta não especificada';
    
    // Tentar obter a conta de destino de diferentes propriedades possíveis
    const destinationAccount = 
      (transaction as any).destinationAccount || 
      (transaction as any).destinationBankAccount || 
      'Conta de destino não especificada';
    
    return (
      <div className="flex flex-col">
        <span className="text-xs text-muted-foreground">De: {sourceAccount}</span>
        <span className="text-xs text-muted-foreground">Para: {destinationAccount}</span>
      </div>
    );
  }
  
  // Para transações normais, mostrar apenas a conta
  return <div>{transaction.bankAccount || 'Conta não especificada'}</div>;
};
