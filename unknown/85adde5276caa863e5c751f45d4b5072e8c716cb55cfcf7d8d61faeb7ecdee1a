import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsUUID } from 'class-validator';

export class IncomeStatementQueryDto {
  @ApiProperty({
    description: 'Data de início do período',
    example: '2025-01-01T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Data de fim do período',
    example: '2025-01-31T23:59:59Z',
  })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: 'ID da moeda (opcional, se não informado usa a moeda padrão)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  currencyId?: string;
}

export class IncomeStatementCategoryDto {
  @ApiProperty({
    description: 'ID da categoria',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nome da categoria',
    example: 'Vendas',
  })
  name: string;

  @ApiProperty({
    description: 'Valor total da categoria',
    example: 15000.75,
  })
  value: number;

  @ApiPropertyOptional({
    description: 'Subcategorias',
    type: [IncomeStatementCategoryDto],
  })
  subcategories?: IncomeStatementCategoryDto[];
}

export class IncomeStatementResponseDto {
  @ApiProperty({
    description: 'Data de início do período',
    example: '2025-01-01T00:00:00Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Data de fim do período',
    example: '2025-01-31T23:59:59Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Receitas agrupadas por categoria',
    type: [IncomeStatementCategoryDto],
  })
  revenues: IncomeStatementCategoryDto[];

  @ApiProperty({
    description: 'Despesas agrupadas por categoria',
    type: [IncomeStatementCategoryDto],
  })
  expenses: IncomeStatementCategoryDto[];

  @ApiProperty({
    description: 'Total de receitas',
    example: 25000.50,
  })
  totalRevenues: number;

  @ApiProperty({
    description: 'Total de despesas',
    example: 10000.25,
  })
  totalExpenses: number;

  @ApiProperty({
    description: 'Lucro líquido (receitas - despesas)',
    example: 15000.25,
  })
  netIncome: number;

  @ApiProperty({
    description: 'Código da moeda',
    example: 'BRL',
  })
  currencyCode: string;

  @ApiProperty({
    description: 'Símbolo da moeda',
    example: 'R$',
  })
  currencySymbol: string;
}
