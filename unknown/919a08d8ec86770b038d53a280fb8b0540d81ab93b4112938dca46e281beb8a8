
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Trash2, Eye } from "lucide-react";
import { Transaction } from "@/types/transaction";
import { TransactionTypeBadge } from "./TransactionTypeBadge";
import { TransactionAccountInfo } from "./TransactionAccountInfo";
import { TransactionAmountDisplay } from "./TransactionAmountDisplay";

interface TransactionMobileCardProps {
  transaction: Transaction;
  onEdit: (transaction: Transaction, mode?: "view" | "edit") => void;
}

export const TransactionMobileCard = ({ transaction, onEdit }: TransactionMobileCardProps) => {
  return (
    <div key={transaction.id} className="mb-4 p-3 border rounded-lg shadow-sm">
      <div className="flex justify-between items-start mb-2">
        <div className="font-medium">{transaction.description}</div>
        <div className="flex space-x-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => onEdit(transaction, "view")}>
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => onEdit(transaction, "edit")}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-y-2 text-sm">
        <div className="text-muted-foreground">Entidade:</div>
        <div>{transaction.entity || '-'}</div>
        
        <div className="text-muted-foreground">Data:</div>
        <div>
          {transaction.transactionDate ? 
            (typeof transaction.transactionDate === 'string' ? 
              format(new Date(transaction.transactionDate), 'MMM dd, yyyy') : 
              format(transaction.transactionDate, 'MMM dd, yyyy')
            ) : '-'}
        </div>
        
        <div className="text-muted-foreground">Conta:</div>
        <div><TransactionAccountInfo transaction={transaction} /></div>
        
        <div className="text-muted-foreground">Tipo:</div>
        <div><TransactionTypeBadge type={transaction.type} /></div>
        
        <div className="text-muted-foreground">Valor:</div>
        <div><TransactionAmountDisplay transaction={transaction} /></div>
      </div>
    </div>
  );
};
