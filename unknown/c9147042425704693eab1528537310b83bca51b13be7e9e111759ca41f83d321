# syntax=docker/dockerfile:1

# Stage 1: Base image com Node.js
FROM node:20-alpine AS base

# Define diretório de trabalho
WORKDIR /app

# Copia apenas o arquivo de lock para instalação de dependências
COPY package-lock.json package.json ./

# Instala dependências com cache
RUN --mount=type=cache,target=/root/.npm \
    npm ci --legacy-peer-deps

# Stage 2: Build da aplicação
FROM base AS builder

# Copia o código fonte da aplicação
COPY . .

# Adiciona dependências de compilação necessárias para módulos nativos
RUN apk add --no-cache python3 make g++

# Reconstrói o módulo bcrypt para garantir compatibilidade
RUN npm rebuild bcrypt --build-from-source

# Build da aplicação
RUN npm run build

# Stage 3: Imagem final para produção
FROM base AS final

# Copia a aplicação construída do estágio builder
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/node_modules /app/node_modules
COPY --from=builder /app/package.json /app/package.json

# Expõe a porta da aplicação
EXPOSE 3001

# Define o comando padrão para iniciar a aplicação
CMD ["npm", "run", "preview", "--", "--host", "0.0.0.0", "--port", "3001"]