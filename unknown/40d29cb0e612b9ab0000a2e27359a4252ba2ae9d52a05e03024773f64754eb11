
import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import FinancialSummary from "@/components/dashboard/FinancialSummary";
import ChartSection from "@/components/dashboard/ChartSection";
import EnhancedPeriodFilter from "@/components/dashboard/EnhancedPeriodFilter";
import AccountFilter from "@/components/dashboard/AccountFilter";
import { Separator } from "@/components/ui/separator";
import FinancialKPIs from "@/components/analytics/FinancialKPIs";
import ExpenseCategoryChart from "@/components/analytics/ExpenseCategoryChart";
import FinancialTrendsChart from "@/components/analytics/FinancialTrendsChart";
import BudgetAnalysis from "@/components/analytics/BudgetAnalysis";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ProjectPerformance from "@/components/analytics/ProjectPerformance";
import CategoryComparison from "@/components/analytics/CategoryComparison";
import ProfitabilityChart from "@/components/analytics/ProfitabilityChart";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Printer, AlertCircle, FileBarChart, BarChart3 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const Analytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("current-month");
  const [selectedAccountId, setSelectedAccountId] = useState("all");

  return (
    <Layout>
      <div className="container px-6 py-8 mx-auto">
        <div className="relative">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/20 to-blue-300/20 rounded-full blur-3xl -z-10"></div>
          <div className="absolute bottom-20 left-20 w-32 h-32 bg-gradient-to-tr from-amber-200/20 to-red-200/20 rounded-full blur-3xl -z-10"></div>
          
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8">
            <div className="relative z-10">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-indigo-500/80 to-purple-500/80 rounded-xl shadow-lg">
                  <FileBarChart className="h-6 w-6 text-white" />
                </div>
                <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-indigo-700 bg-clip-text text-transparent dark:from-indigo-400 dark:to-purple-300">Analytics</h1>
              </div>
              <p className="text-muted-foreground mt-1">
                Análise financeira detalhada e insights estratégicos para tomada de decisão
              </p>
            </div>
            <div className="flex space-x-2 mt-4 md:mt-0">
              <Button variant="outline" size="sm" className="flex items-center gap-1.5 bg-white/90 dark:bg-gray-800/90 shadow-sm hover:shadow-md transition-all">
                <Download className="h-4 w-4" />
                <span>Exportar</span>
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1.5 bg-white/90 dark:bg-gray-800/90 shadow-sm hover:shadow-md transition-all">
                <Printer className="h-4 w-4" />
                <span>Imprimir</span>
              </Button>
            </div>
          </div>

          <div className="flex flex-wrap gap-4 mb-8">
            <EnhancedPeriodFilter
              selectedPeriod={selectedPeriod}
              onPeriodChange={setSelectedPeriod}
            />
            <AccountFilter
              selectedAccountId={selectedAccountId}
              accounts={[
                { id: "1", name: "Conta Principal" },
                { id: "2", name: "Conta Secundária" },
                { id: "3", name: "Conta Investimentos" }
              ]}
              onAccountChange={setSelectedAccountId}
            />
          </div>

          <Card className="mb-6 border-yellow-200/80 bg-gradient-to-r from-amber-50 to-yellow-50/90 dark:from-yellow-950/30 dark:to-amber-950/20 dark:border-yellow-900/20 shadow-sm">
            <CardContent className="p-4 flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-500 shrink-0 mt-0.5" />
              <div>
                <p className="text-sm text-yellow-800 dark:text-yellow-400">
                  Os dados apresentados neste dashboard são baseados nas transações registradas no sistema.
                  Para insights mais precisos, certifique-se de que todas as transações estejam atualizadas.
                </p>
              </div>
            </CardContent>
          </Card>

          <FinancialSummary
            selectedPeriod={selectedPeriod}
            selectedAccountId={selectedAccountId}
          />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <FinancialKPIs 
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
            <ChartSection
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            <ExpenseCategoryChart
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
            <ProfitabilityChart 
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
            <BudgetAnalysis
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
          </div>

          <Tabs defaultValue="trends" className="mt-8">
            <TabsList className="grid grid-cols-3 max-w-[500px] bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-1 rounded-xl">
              <TabsTrigger value="trends" className="rounded-lg data-[state=active]:bg-gradient-to-br data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
                Tendências
              </TabsTrigger>
              <TabsTrigger value="projects" className="rounded-lg data-[state=active]:bg-gradient-to-br data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
                Projetos
              </TabsTrigger>
              <TabsTrigger value="categories" className="rounded-lg data-[state=active]:bg-gradient-to-br data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
                Categorias
              </TabsTrigger>
            </TabsList>
            <TabsContent value="trends" className="mt-6">
              <FinancialTrendsChart
                selectedPeriod={selectedPeriod}
                selectedAccountId={selectedAccountId}
              />
            </TabsContent>
            <TabsContent value="projects" className="mt-6">
              <ProjectPerformance
                selectedPeriod={selectedPeriod}
                selectedAccountId={selectedAccountId}
              />
            </TabsContent>
            <TabsContent value="categories" className="mt-6">
              <CategoryComparison
                selectedPeriod={selectedPeriod}
                selectedAccountId={selectedAccountId}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Analytics;
