import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { accountsPayableService } from '@/services/api';
import { CreateAccountPayableRequest, UpdateAccountPayableRequest } from '@/types/api';
import { toast } from 'sonner';
import { useCreateTransaction } from './useTransactions';

// Hook para listar contas a pagar com paginação e filtros
export const useAccountsPayable = (
  page = 1,
  limit = 10,
  filters?: {
    search?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string
  }
) => {
  const { search, status, startDate, endDate, categoryId, entityId } = filters || {};

  return useQuery({
    queryKey: ['accountsPayable', { page, limit, search, status, startDate, endDate, categoryId, entityId }],
    queryFn: () => accountsPayableService.getAccountsPayable(
      page, limit, search, status, startDate, endDate, categoryId, entityId
    ),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar uma conta a pagar específica por ID
export const useAccountPayable = (id: string) => {
  return useQuery({
    queryKey: ['accountsPayable', id],
    queryFn: () => accountsPayableService.getAccountPayableById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para criar uma nova conta a pagar
export const useCreateAccountPayable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAccountPayableRequest) => accountsPayableService.createAccountPayable(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accountsPayable'] });
      toast.success('Conta a pagar criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao criar conta a pagar. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma conta a pagar existente
export const useUpdateAccountPayable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateAccountPayableRequest }) =>
      accountsPayableService.updateAccountPayable(id, data),
    onSuccess: (updatedAccountPayable) => {
      queryClient.invalidateQueries({ queryKey: ['accountsPayable'] });
      queryClient.setQueryData(['accountsPayable', updatedAccountPayable.id], updatedAccountPayable);
      toast.success('Conta a pagar atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao atualizar conta a pagar. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma conta a pagar
export const useDeleteAccountPayable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => accountsPayableService.deleteAccountPayable(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['accountsPayable'] });
      queryClient.removeQueries({ queryKey: ['accountsPayable', id] });
      toast.success('Conta a pagar excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir conta a pagar. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para marcar uma conta a pagar como paga (DEPRECATED)
// Este hook foi mantido para compatibilidade, mas será removido em versões futuras
// Use o hook useCreateTransaction para registrar pagamentos
export const usePayAccountPayable = () => {
  const queryClient = useQueryClient();
  const { mutate: createTransaction } = useCreateTransaction();

  console.warn('O hook usePayAccountPayable está depreciado. Use o hook useCreateTransaction para registrar pagamentos.');

  return useMutation({
    mutationFn: async ({
      id,
      paymentDate,
      bankAccountId,
      amount,
      description,
      paymentMethodId,
      notes
    }: {
      id: string,
      paymentDate?: string,
      bankAccountId: string,
      amount?: number,
      description?: string,
      paymentMethodId?: string,
      notes?: string
    }) => {
      // Obter a conta a pagar para ter acesso aos dados necessários
      const accountPayable = await accountsPayableService.getAccountPayableById(id);

      // Criar uma transação do tipo expense vinculada à conta a pagar
      createTransaction({
        type: 'expense',
        amount: amount || accountPayable.amount,
        description: description || `Pagamento: ${accountPayable.description}`,
        transactionDate: paymentDate || new Date().toISOString().split('T')[0],
        bankAccountId,
        categoryId: accountPayable.categoryId,
        entityId: accountPayable.entityId,
        projectId: accountPayable.projectId,
        paymentMethodId: paymentMethodId || accountPayable.paymentMethodId,
        accountsPayableId: id,
        notes
      });

      return accountPayable; // Retornar a conta a pagar sem modificá-la
    },
    onSuccess: (accountPayable) => {
      queryClient.invalidateQueries({ queryKey: ['accountsPayable'] });
      queryClient.setQueryData(['accountsPayable', accountPayable.id], accountPayable);
      toast.success('Conta a pagar quitada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao quitar conta a pagar. Por favor, tente novamente.'
      );
    }
  });
};
