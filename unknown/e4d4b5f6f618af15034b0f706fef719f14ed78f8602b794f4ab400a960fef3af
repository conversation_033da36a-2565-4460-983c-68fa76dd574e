import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RbacService } from '../routes/rbac/rbac.service';

export const PERMISSIONS_KEY = 'permissions';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private rbacService: RbacService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Se não houver permissões definidas, permite o acesso
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    // Se não houver usuário ou companyId, nega o acesso
    if (!user || !user.userId || !user.companyId) {
      throw new ForbiddenException('Acesso negado: usuário não autenticado corretamente');
    }

    // Verificar se o usuário tem todas as permissões necessárias
    const userPermissions = await this.rbacService.getUserPermissions(user.userId, user.companyId);

    // Verificar se o usuário tem todas as permissões necessárias
    const hasAllRequiredPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAllRequiredPermissions) {
      throw new ForbiddenException('Acesso negado: você não tem as permissões necessárias para acessar este recurso');
    }

    return true;
  }
}
