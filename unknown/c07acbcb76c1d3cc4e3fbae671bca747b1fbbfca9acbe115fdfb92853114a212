#!/bin/bash
set -e

# Diretório do script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Verificar se estamos no Docker
if [ -f "/.dockerenv" ] || [ -f "/proc/1/cgroup" ] && grep -q "docker" /proc/1/cgroup; then
  DOCKER=1
else
  DOCKER=0
fi

# Caminho para o arquivo de saída
OUTPUT_FILE="${ROOT_DIR}/prisma/seed_exported.sql"

echo "=== Exportando dados do banco de dados para script de seed ==="
echo "Diretório raiz: ${ROOT_DIR}"
echo "Arquivo de saída: ${OUTPUT_FILE}"

# Executar o script de exportação
if [ "$DOCKER" -eq 1 ]; then
  echo "Executando no Docker..."
  cd "$ROOT_DIR" && node scripts/export-seed.js --docker --output="$OUTPUT_FILE"
else
  echo "Executando localmente..."
  cd "$ROOT_DIR" && node scripts/export-seed.js --output="$OUTPUT_FILE"
fi

# Verificar se o arquivo foi gerado
if [ -f "$OUTPUT_FILE" ]; then
  echo "✅ Script de seed gerado com sucesso em $OUTPUT_FILE"
  
  # Perguntar se deseja substituir o arquivo seed_new.sql
  read -p "Deseja substituir o arquivo seed_new.sql com o novo script? (s/N): " REPLACE
  if [[ "$REPLACE" =~ ^[Ss]$ ]]; then
    cp "$OUTPUT_FILE" "${ROOT_DIR}/prisma/seed_new.sql"
    echo "✅ Arquivo seed_new.sql substituído com sucesso!"
  fi
else
  echo "❌ Erro ao gerar o script de seed"
  exit 1
fi

echo "=== Exportação concluída ==="
