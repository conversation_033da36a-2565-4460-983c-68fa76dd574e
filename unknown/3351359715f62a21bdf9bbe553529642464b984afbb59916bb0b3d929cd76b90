import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CompanyDto, CompanySettingsDto } from '../models/company.model';
import { AccessControlUtil } from '../utils/access-control.util';
import { mapToCompanyDto } from '../utils/mappers.util';
import { CompanyRawResult } from '../utils/sql-transforms.util';
import { nullToUndefined } from '../utils/sql-transforms.util';

@Injectable()
export class CompanyQueryService {
  private readonly logger = new Logger(CompanyQueryService.name);

  constructor(
    private prisma: PrismaService,
    private accessControlUtil: AccessControlUtil,
  ) {}

  /**
   * Encontra uma empresa pelo ID
   * @param id ID da empresa
   * @param userId ID do usuário que está fazendo a consulta
   * @returns Dados da empresa
   */
  async findOne(id: string, userId: string): Promise<CompanyDto> {
    try {
      // Verificar se o usuário tem acesso à empresa
      await this.accessControlUtil.validateUserHasAccessToCompany(userId, id);

      const company = await this.prisma.$queryRaw<CompanyRawResult[]>`
        SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
      `;

      if (company.length === 0) {
        throw new NotFoundException('Empresa não encontrada');
      }

      return mapToCompanyDto(company[0]);
    } catch (error) {
      this.logger.error(`Erro ao buscar empresa: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista todas as empresas às quais um usuário tem acesso, com paginação
   * @param page Número da página
   * @param limit Limite de registros por página
   * @param userId ID do usuário
   * @returns Lista paginada de empresas
   */
  async findAll(
    page = 1,
    limit = 10,
    userId: string,
  ): Promise<{
    data: CompanyDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      // Buscar empresas às quais o usuário tem acesso
      const userCompanies = await this.prisma.$queryRaw<{ company_id: string }[]>`
        SELECT DISTINCT ucr.company_id 
        FROM user_company_roles ucr
        WHERE ucr.user_id = ${userId}::uuid AND ucr.deleted_at IS NULL
      `;

      if (userCompanies.length === 0) {
        return { data: [], total: 0, page, limit };
      }

      const companyIds = userCompanies.map((uc) => uc.company_id);
      const placeholders = companyIds.map((_, i) => `$${i + 1}::uuid`).join(',');

      // Contar total de empresas
      const countQuery = `
        SELECT COUNT(*) as total
        FROM companies c
        WHERE c.id IN (${placeholders})
        AND c.deleted_at IS NULL
      `;

      const totalResult = await this.prisma.$queryRawUnsafe<[{ total: string }]>(
        countQuery,
        ...companyIds,
      );

      const total = parseInt(totalResult[0].total, 10);

      // Buscar empresas com paginação
      const companiesQuery = `
        SELECT c.*
        FROM companies c
        WHERE c.id IN (${placeholders})
        AND c.deleted_at IS NULL
        ORDER BY c.name
        LIMIT ${limit} OFFSET ${skip}
      `;

      const companies = await this.prisma.$queryRawUnsafe<CompanyRawResult[]>(
        companiesQuery,
        ...companyIds,
      );

      const data = companies.map(mapToCompanyDto);

      return { data, total, page, limit };
    } catch (error) {
      this.logger.error(`Erro ao listar empresas: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Obtém as configurações de uma empresa
   * @param id ID da empresa
   * @param userId ID do usuário que está fazendo a consulta
   * @returns Configurações da empresa
   */
  async getSettings(id: string, userId: string): Promise<CompanySettingsDto> {
    try {
      // Verificar se o usuário tem acesso à empresa
      await this.accessControlUtil.validateUserHasAccessToCompany(userId, id);

      const company = await this.prisma.$queryRaw<CompanyRawResult[]>`
        SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
      `;

      if (company.length === 0) {
        throw new NotFoundException('Empresa não encontrada');
      }

      return {
        id: company[0].id,
        name: company[0].name,
        calendarType: company[0].calendar_type || 'standard',
        // Outras configurações conforme necessário
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar configurações da empresa: ${error.message}`, error.stack);
      throw error;
    }
  }
} 