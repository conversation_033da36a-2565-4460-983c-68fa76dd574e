# Makefile para gerenciamento do banco de dados com Prisma

.PHONY: help db-reset db-squash db-create db-deploy db-seed db-status db-push-force db-introspect db-generate db-custom db-export-seed db-apply-rbac-triggers

help:
	@echo "Comandos disponíveis:"
	@echo "  make db-reset [docker=1]         - Resetar o banco de dados (apaga todos os dados)"
	@echo "  make db-squash                   - Consolidar todas as migrações em uma única"
	@echo "  make db-create name=<nome>       - Criar uma nova migração"
	@echo "  make db-deploy [docker=1]        - Aplicar migrações pendentes"
	@echo "  make db-seed [docker=1]          - Executar o seeding do banco de dados"
	@echo "  make db-export-seed [docker=1]   - Exportar dados atuais para script de seed"
	@echo "  make db-status [docker=1]        - Verificar o status das migrações"
	@echo "  make db-push-force [docker=1]    - Forçar atualização do banco de dados (aceita perda de dados)"
	@echo "  make db-introspect [docker=1]    - Atualizar schema.prisma com base no banco de dados"
	@echo "  make db-generate [docker=1]      - Gerar cliente Prisma"
	@echo "  make db-apply-rbac-triggers [docker=1] - Aplicar triggers RBAC ao banco de dados"
	@echo "  make db-custom cmd=<comando>     - Executar um comando Prisma personalizado"

# Função para adicionar a flag --docker se docker=1
docker_flag = $(if $(filter 1,$(docker)),--docker,)

db-reset:
	@echo "🔄 Resetando o banco de dados..."
	@npm run db -- reset $(docker_flag)

db-squash:
	@echo "🔄 Consolidando migrações..."
	@npm run db -- squash

db-create:
	@if [ -z "$(name)" ]; then \
		npm run db -- create; \
	else \
		npm run db -- create $(name) $(docker_flag); \
	fi

db-deploy:
	@echo "🔄 Aplicando migrações pendentes..."
	@npm run db -- deploy $(docker_flag)

db-seed:
	@echo "🌱 Executando seeding do banco de dados..."
	@npm run db -- seed $(docker_flag)

db-status:
	@echo "🔍 Verificando status das migrações..."
	@npm run db -- status $(docker_flag)

db-push-force:
	@echo "⚠️ Forçando atualização do banco de dados..."
	@npm run db -- push-force $(docker_flag)

db-introspect:
	@echo "🔄 Atualizando schema.prisma com base no banco de dados..."
	@npm run db -- introspect $(docker_flag)

db-generate:
	@echo "🔄 Gerando cliente Prisma..."
	@npm run db -- generate $(docker_flag)

db-custom:
	@if [ -z "$(cmd)" ]; then \
		echo "❌ Erro: Nenhum comando especificado!"; \
		echo "Uso: make db-custom cmd=\"<comando-prisma>\""; \
		echo "Exemplo: make db-custom cmd=\"migrate dev --name add_user_field\""; \
		exit 1; \
	fi; \
	echo "🔄 Executando comando personalizado: $(cmd)..."; \
	npm run db -- custom $(cmd) $(docker_flag)

db-export-seed:
	@echo "🌱 Exportando dados atuais para script de seed..."
	@chmod +x scripts/export-seed.sh
	@scripts/export-seed.sh $(docker_flag)

db-apply-rbac-triggers:
	@echo "🔄 Aplicando triggers RBAC ao banco de dados..."
	@npm run db -- apply-rbac-triggers $(docker_flag)
