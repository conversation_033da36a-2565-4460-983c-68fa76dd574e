import api from './axios';
import {
  AccountPayable,
  CreateAccountPayableRequest,
  UpdateAccountPayableRequest,
  PaginatedResponse,
  ApiResponse,
  AccountsPayableSummary
} from '@/types/api';

export const accountsPayableService = {
  // Listar todas as contas a pagar com paginação
  getAccountsPayable: async (
    page = 1,
    limit = 10,
    search?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string
  ): Promise<PaginatedResponse<AccountPayable>> => {
    const params = { page, limit, search, status, startDate, endDate, categoryId, entityId };
    const response = await api.get<PaginatedResponse<AccountPayable>>('/accounts-payable', { params });
    return response.data;
  },

  // Obter conta a pagar por ID
  getAccountPayableById: async (id: string): Promise<AccountPayable> => {
    const response = await api.get<ApiResponse<AccountPayable>>(`/accounts-payable/${id}`);
    return response.data.data;
  },

  // Criar conta a pagar
  createAccountPayable: async (data: CreateAccountPayableRequest): Promise<AccountPayable> => {
    const response = await api.post<ApiResponse<AccountPayable>>('/accounts-payable', data);
    return response.data.data;
  },

  // Atualizar conta a pagar
  updateAccountPayable: async (id: string, data: UpdateAccountPayableRequest): Promise<AccountPayable> => {
    const response = await api.put<ApiResponse<AccountPayable>>(`/accounts-payable/${id}`, data);
    return response.data.data;
  },

  // Excluir conta a pagar
  deleteAccountPayable: async (id: string): Promise<void> => {
    await api.delete(`/accounts-payable/${id}`);
  },

  // Marcar como paga (DEPRECATED - usar transactionService.createTransaction)
  // Este método foi mantido para compatibilidade, mas será removido em versões futuras
  payAccountPayable: async (id: string, paymentData: {
    paymentDate?: string,
    bankAccountId: string,
    amount?: number,
    description?: string,
    paymentMethodId?: string,
    notes?: string
  }): Promise<AccountPayable> => {
    // Obter a conta a pagar atual
    const accountPayable = await accountsPayableService.getAccountPayableById(id);

    // Criar uma transação do tipo expense
    const { paymentDate, bankAccountId, amount, description, paymentMethodId, notes } = paymentData;

    // Aqui deveria chamar o transactionService.createTransaction, mas como não temos acesso a ele,
    // vamos apenas retornar a conta a pagar atual com uma mensagem de aviso
    console.warn('O endpoint /pay foi removido. Use transactionService.createTransaction para criar uma transação do tipo "expense" vinculada à conta a pagar.');

    // Retornar a conta a pagar sem modificá-la
    return accountPayable;
  },

  // Buscar o resumo de contas a pagar
  getAccountsPayableSummary: async (companyId?: string): Promise<ApiResponse<AccountsPayableSummary>> => {
    const params = companyId ? { companyId } : {};
    const response = await api.get<ApiResponse<AccountsPayableSummary>>('/accounts-payable/summary', { params });
    return response.data;
  }
};
