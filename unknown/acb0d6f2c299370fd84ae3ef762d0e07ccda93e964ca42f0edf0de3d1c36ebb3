import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';

export class CompanyDto {
  @ApiProperty({
    description: 'ID único da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({ description: 'Nome da empresa', example: 'Empresa ABC Ltda' })
  name: string;

  @ApiProperty({
    description: 'CNPJ da empresa',
    example: '12.345.678/0001-90',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '(11) 99999-9999',
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @ApiProperty({
    description: 'ID do endereço da empresa',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  addressId?: string;

  @ApiProperty({
    description: 'URL do logo da empresa',
    example: 'https://storage.com/logo.png',
    required: false,
  })
  logo?: string;

  @ApiProperty({
    description: 'Indica se a empresa está ativa',
    example: true,
    default: true,
  })
  active: boolean;

  @ApiProperty({
    description: 'Tipo de calendário utilizado pela empresa',
    example: 'standard',
    default: 'standard',
  })
  calendarType: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Data de exclusão',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  deletedAt?: Date;
}

export class CreateCompanyDto {
  @ApiProperty({ description: 'Nome da empresa', example: 'Empresa ABC Ltda' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'CNPJ da empresa',
    example: '12.345.678/0001-90',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2}$/, {
    message: 'CNPJ inválido. Use o formato: XX.XXX.XXX/XXXX-XX',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '(11) 99999-9999',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\(\d{2}\)\s\d{4,5}\-\d{4}$/, {
    message: 'Telefone inválido. Use o formato: (XX) XXXXX-XXXX',
  })
  phone?: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'ID do endereço da empresa',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  addressId?: string;

  @ApiProperty({
    description: 'URL do logo da empresa',
    example: 'https://storage.com/logo.png',
    required: false,
  })
  @IsString()
  @IsOptional()
  logo?: string;

  @ApiProperty({
    description: 'Indica se a empresa está ativa',
    example: true,
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    description: 'Tipo de calendário utilizado pela empresa',
    example: 'standard',
    default: 'standard',
    required: false,
  })
  @IsString()
  @IsOptional()
  calendarType?: string;
}

export class UpdateCompanyDto {
  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Empresa ABC Ltda',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'CNPJ da empresa',
    example: '12.345.678/0001-90',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2}$/, {
    message: 'CNPJ inválido. Use o formato: XX.XXX.XXX/XXXX-XX',
  })
  cnpj?: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '(11) 99999-9999',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\(\d{2}\)\s\d{4,5}\-\d{4}$/, {
    message: 'Telefone inválido. Use o formato: (XX) XXXXX-XXXX',
  })
  phone?: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'ID do endereço da empresa',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  addressId?: string;

  @ApiProperty({
    description: 'URL do logo da empresa',
    example: 'https://storage.com/logo.png',
    required: false,
  })
  @IsString()
  @IsOptional()
  logo?: string;

  @ApiProperty({
    description: 'Indica se a empresa está ativa',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    description: 'Tipo de calendário utilizado pela empresa',
    example: 'standard',
    required: false,
  })
  @IsString()
  @IsOptional()
  calendarType?: string;
}

export class CompanySettingsDto {
  @ApiProperty({
    description: 'ID único da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({ description: 'Nome da empresa', example: 'Empresa ABC Ltda' })
  name: string;

  @ApiProperty({
    description: 'Tipo de calendário utilizado pela empresa',
    example: 'standard',
  })
  calendarType: string;

  @ApiProperty({
    description: 'URL do logo da empresa',
    example: 'https://storage.com/logo.png',
    required: false,
  })
  logo?: string;
}
