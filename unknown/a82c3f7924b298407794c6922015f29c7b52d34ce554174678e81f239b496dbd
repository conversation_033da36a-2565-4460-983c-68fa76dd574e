import { Input } from "../ui/input";
import { cn } from "@/lib/utils";
import { forwardRef, useEffect, useRef, useState, KeyboardEvent } from "react";
import { parseCurrencyToNumber } from "@/utils/currencyUtils";

// Simplificando a interface para evitar conflitos de tipos
export interface MoneyInputProps {
  onValueChange?: (value: number) => void;
  value?: number;
  className?: string;
  id?: string;
  disabled?: boolean;
  placeholder?: string;
}

export const MoneyInput = forwardRef<HTMLInputElement, MoneyInputProps>(
  ({ onValueChange, value, className, ...props }, ref) => {
    // Valor interno como string para gerenciar a exibição
    const [displayValue, setDisplayValue] = useState("0,00");
    // Referência para o input
    const inputRef = useRef<HTMLInputElement>(null);

    // Atualiza o display quando o valor externo muda
    useEffect(() => {
      if (value !== undefined) {
        const valueStr = formatMonetaryValue(value);
        setDisplayValue(valueStr);
      }
    }, [value]);

    // Função para formatar valor numérico para exibição
    const formatMonetaryValue = (numValue: number): string => {
      return numValue.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).replace('R$', '').trim();
    };

    // Função para processar a entrada de dígitos
    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      // Permitir apenas números, backspace, delete, tab e algumas teclas de controle
      const allowedKeys = [
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
        'Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'
      ];
      
      // Se não for uma tecla permitida, impedir a entrada
      if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
        e.preventDefault();
        return;
      }

      // Processar backspace manualmente
      if (e.key === 'Backspace') {
        e.preventDefault();
        
        // Remove o último dígito da sequência numérica
        const numericValue = displayValue.replace(/\D/g, '');
        if (numericValue.length <= 1) {
          setDisplayValue('0,00');
          onValueChange?.(0);
        } else {
          const newNumericValue = numericValue.slice(0, -1);
          // Adicionar zeros à esquerda para garantir pelo menos 3 caracteres
          const paddedValue = newNumericValue.padStart(3, '0');
          // Calcular o valor real
          const realValue = Number(paddedValue) / 100;
          
          setDisplayValue(formatMonetaryValue(realValue));
          onValueChange?.(realValue);
        }
        return;
      }

      // Se for um dígito numérico, processar a entrada monetária
      if (e.key >= '0' && e.key <= '9') {
        e.preventDefault();
        
        // Pegar apenas os números do valor atual
        const numericValue = displayValue.replace(/\D/g, '');
        
        // Adicionar o novo dígito
        const newNumericValue = numericValue + e.key;
        
        // Limitar a um valor razoável
        if (newNumericValue.length > 12) return;
        
        // Calcular o valor real
        const realValue = Number(newNumericValue) / 100;
        
        setDisplayValue(formatMonetaryValue(realValue));
        onValueChange?.(realValue);
      }
    };

    // Efeito para colocar o cursor no final após cada mudança
    useEffect(() => {
      if (inputRef.current) {
        const length = displayValue.length;
        inputRef.current.setSelectionRange(length, length);
      }
    }, [displayValue]);

    return (
      <div className="relative">
        <Input
          ref={(element) => {
            // Manter ambas as referências
            inputRef.current = element;
            if (typeof ref === 'function') {
              ref(element);
            } else if (ref) {
              ref.current = element;
            }
          }}
          value={displayValue}
          onChange={() => {}} // Controlado completamente pelo onKeyDown
          onKeyDown={handleKeyDown}
          onClick={() => {
            // Ao clicar, posicionar o cursor no final
            if (inputRef.current) {
              const length = displayValue.length;
              inputRef.current.setSelectionRange(length, length);
            }
          }}
          className={cn("font-medium pl-8 h-10 flex items-center", className)}
          {...props}
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
          R$
        </div>
      </div>
    );
  }
);

MoneyInput.displayName = "MoneyInput";

// Componente somente para exibição, sem input
export const MoneyDisplay = ({ value = 0, className }: { value: number; className?: string }) => {
  return (
    <div className={cn("font-medium", className)}>
      {new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(value)}
    </div>
  );
}; 