
import { ReactNode, useState, useEffect } from "react";
import { Location, useLocation } from "react-router-dom";
import Sidebar from "./Sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import Navbar from "./Navbar";

interface LayoutProps {
  children: ReactNode;
  location?: Location;
}

export const Layout = ({ children, location }: LayoutProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const isMobile = useIsMobile();
  const currentLocation = useLocation();
  
  // Use provided location or current location from router
  const activeLocation = location || currentLocation;

  // Auto-hide sidebar on mobile
  useEffect(() => {
    if (isMobile) {
      setIsSidebarOpen(false);
    } else {
      setIsSidebarOpen(true);
    }
  }, [isMobile]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-background">
      <Sidebar isOpen={isSidebarOpen} />
      <div className={`flex-1 flex flex-col transition-all duration-300 ${!isSidebarOpen ? 'lg:ml-20' : 'lg:ml-64'} w-full`}>
        <Navbar 
          onToggleSidebar={toggleSidebar} 
          className=""
        />
        <main className="flex-1 overflow-auto bg-background p-4">
          {children}
        </main>
      </div>
    </div>
  );
};

// Helper function to get the page title based on the route
const getPageTitle = (pathname: string): string => {
  const routes: Record<string, string> = {
    "/": "Dashboard",
    "/bank-accounts": "Bank Accounts",
    "/customers": "Customers",
    "/suppliers": "Suppliers",
  };

  return routes[pathname] || "Dashboard";
};
