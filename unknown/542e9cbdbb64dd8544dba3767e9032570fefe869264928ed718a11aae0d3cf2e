import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useDataExport } from '@/hooks/useDataExport';
import { Download, FileText, FileSpreadsheet, File, FileJson, Loader2 } from 'lucide-react';

interface ExportOption {
  id: string;
  label: string;
  description?: string;
  checked?: boolean;
}

interface DataExportProps {
  endpoint: string;
  title?: string;
  description?: string;
  filename?: string;
  exportOptions?: ExportOption[];
  params?: Record<string, any>;
  buttonVariant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  buttonClassName?: string;
  onExportStart?: () => void;
  onExportComplete?: (blob: Blob) => void;
  onExportError?: (error: any) => void;
}

export function DataExport({
  endpoint,
  title = 'Exportar Dados',
  description = 'Selecione o formato de exportação e as opções desejadas',
  filename = 'export',
  exportOptions = [],
  params = {},
  buttonVariant = 'outline',
  buttonSize = 'default',
  buttonClassName,
  onExportStart,
  onExportComplete,
  onExportError,
}: DataExportProps) {
  const [open, setOpen] = useState(false);
  const [customFilename, setCustomFilename] = useState(filename);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, boolean>>(
    exportOptions.reduce((acc, option) => {
      acc[option.id] = option.checked ?? false;
      return acc;
    }, {} as Record<string, boolean>)
  );
  
  const { exporting, exportData, exportFormats, selectedFormat, setSelectedFormat } = useDataExport({
    endpoint,
    filename: customFilename,
    params,
    onSuccess: (blob) => {
      onExportComplete?.(blob);
      setOpen(false);
    },
    onError,
  });
  
  function onExport() {
    if (onExportStart) {
      onExportStart();
    }
    
    // Adicionar opções selecionadas aos parâmetros
    const exportParams = { ...params };
    Object.entries(selectedOptions).forEach(([key, value]) => {
      if (value) {
        exportParams[key] = true;
      }
    });
    
    exportData({
      filename: customFilename,
      params: exportParams,
    });
  }
  
  function onError(error: any) {
    if (onExportError) {
      onExportError(error);
    }
  }
  
  // Função para obter o ícone do formato de arquivo
  function getFormatIcon(format: string) {
    switch (format) {
      case 'csv':
        return <FileText className="h-4 w-4 mr-2" />;
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 mr-2" />;
      case 'pdf':
        return <File className="h-4 w-4 mr-2" />;
      case 'json':
        return <FileJson className="h-4 w-4 mr-2" />;
      default:
        return <FileText className="h-4 w-4 mr-2" />;
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={buttonClassName}
          disabled={exporting}
        >
          {exporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          Exportar
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="filename">Nome do arquivo</Label>
            <Input
              id="filename"
              value={customFilename}
              onChange={(e) => setCustomFilename(e.target.value)}
              placeholder="Nome do arquivo de exportação"
            />
          </div>
          
          <div className="grid gap-2">
            <Label>Formato</Label>
            <RadioGroup
              value={selectedFormat}
              onValueChange={(value) => setSelectedFormat(value as any)}
              className="grid grid-cols-2 gap-2"
            >
              {exportFormats.map((format) => (
                <div key={format} className="flex items-center space-x-2">
                  <RadioGroupItem value={format} id={`format-${format}`} />
                  <Label htmlFor={`format-${format}`} className="flex items-center cursor-pointer">
                    {getFormatIcon(format)}
                    {format.toUpperCase()}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
          
          {exportOptions.length > 0 && (
            <div className="grid gap-2">
              <Label>Opções de exportação</Label>
              <div className="grid gap-2">
                {exportOptions.map((option) => (
                  <div key={option.id} className="flex items-start space-x-2">
                    <Checkbox
                      id={option.id}
                      checked={selectedOptions[option.id]}
                      onCheckedChange={(checked) => {
                        setSelectedOptions({
                          ...selectedOptions,
                          [option.id]: !!checked,
                        });
                      }}
                    />
                    <div className="grid gap-1">
                      <Label htmlFor={option.id} className="cursor-pointer">
                        {option.label}
                      </Label>
                      {option.description && (
                        <p className="text-sm text-muted-foreground">
                          {option.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={onExport} disabled={exporting}>
            {exporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exportando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
