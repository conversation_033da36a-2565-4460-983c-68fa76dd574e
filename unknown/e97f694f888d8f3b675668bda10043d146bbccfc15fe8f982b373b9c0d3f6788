
import { Badge } from "@/components/ui/badge";
import { ArrowUp, ArrowDown, ArrowLeftRight } from "lucide-react";

interface TransactionTypeBadgeProps {
  type: string;
}

export const TransactionTypeBadge = ({ type }: TransactionTypeBadgeProps) => {
  // Normalizar o tipo para lidar com diferentes formatos da API
  const normalizedType = type?.toLowerCase?.() || '';
  
  if (normalizedType.includes('receivable') || normalizedType.includes('income') || normalizedType === 'income') {
    return (
      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
        <ArrowUp className="h-3 w-3" />
        <span>Receita</span>
      </Badge>
    );
  }
  
  if (normalizedType.includes('payable') || normalizedType.includes('expense') || normalizedType === 'expense') {
    return (
      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
        <ArrowDown className="h-3 w-3" />
        <span>Despesa</span>
      </Badge>
    );
  }
  
  if (normalizedType.includes('transfer')) {
    return (
      <Badge 
        variant="outline" 
        className="!bg-blue-50 !text-blue-700 !border-blue-200 flex items-center gap-1 !px-1.5 !py-0.5 text-xs"
      >
        <ArrowLeftRight className="h-3 w-3" />
        <span>Transferência</span>
      </Badge>
    );
  }
  
  return <Badge variant="outline">{type || 'Desconhecido'}</Badge>;
};
