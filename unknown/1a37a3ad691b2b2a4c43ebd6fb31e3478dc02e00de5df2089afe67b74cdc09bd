import { ApiProperty } from '@nestjs/swagger';

export class ZipCodeDto {
  @ApiProperty({ description: 'CEP', example: '01310-100' })
  zipCode: string;

  @ApiProperty({ description: 'Rua/Logradouro', example: 'Avenida Paulista' })
  street: string;

  @ApiProperty({ description: 'Bairro', example: 'Bela Vista' })
  neighborhood: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo' })
  city: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP' })
  state: string;

  @ApiProperty({
    description: 'Origem do registro',
    example: 'auto',
    enum: ['auto', 'manual']
  })
  registrationOrigin: string;

  @ApiProperty({
    description: 'Data de expiração do cache',
    example: '2023-01-01T00:00:00Z',
    required: false
  })
  expiresAt?: Date;

  @ApiProperty({ description: 'Data de criação', example: '2023-01-01T00:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização', example: '2023-01-01T00:00:00Z' })
  updatedAt: Date;
}
