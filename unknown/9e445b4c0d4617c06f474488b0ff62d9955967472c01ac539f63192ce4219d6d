import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Search, X } from 'lucide-react';

export interface QuickFilterOption {
  id: string;
  label: string;
  type: 'text' | 'select';
  options?: Array<{ label: string; value: string | number | boolean }>;
  placeholder?: string;
}

interface QuickFiltersProps {
  filters: QuickFilterOption[];
  values: Record<string, any>;
  onFilterChange: (id: string, value: any) => void;
  onClearFilters?: () => void;
  onSearch?: () => void;
  searchLabel?: string;
  className?: string;
  showClearButton?: boolean;
}

export function QuickFilters({
  filters,
  values,
  onFilterChange,
  onClearFilters,
  onSearch,
  searchLabel = 'Buscar',
  className,
  showClearButton = true,
}: QuickFiltersProps) {
  // Verifica se há algum filtro ativo
  const hasActiveFilters = Object.values(values).some(value => 
    value !== null && value !== undefined && value !== ''
  );
  
  // Conta quantos filtros estão ativos
  const activeFilterCount = Object.values(values).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length;
  
  return (
    <div className={cn("flex flex-wrap items-center gap-3 p-4 bg-background border rounded-lg", className)}>
      {filters.map((filter) => (
        <div key={filter.id} className="flex-1 min-w-[200px]">
          {filter.type === 'text' && (
            <div className="relative">
              <Input
                placeholder={filter.placeholder || filter.label}
                value={values[filter.id] || ''}
                onChange={(e) => onFilterChange(filter.id, e.target.value)}
                className="w-full"
              />
              {values[filter.id] && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() => onFilterChange(filter.id, '')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
          
          {filter.type === 'select' && (
            <Select
              value={values[filter.id] !== null && values[filter.id] !== undefined ? String(values[filter.id]) : ''}
              onValueChange={(value) => onFilterChange(filter.id, value === '' ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.placeholder || filter.label} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos</SelectItem>
                {filter.options?.map((option) => (
                  <SelectItem key={String(option.value)} value={String(option.value)}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      ))}
      
      <div className="flex items-center gap-2">
        {onSearch && (
          <Button onClick={onSearch} className="whitespace-nowrap">
            <Search className="mr-2 h-4 w-4" />
            {searchLabel}
          </Button>
        )}
        
        {showClearButton && hasActiveFilters && onClearFilters && (
          <Button variant="outline" onClick={onClearFilters} className="whitespace-nowrap">
            <X className="mr-2 h-4 w-4" />
            Limpar
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
