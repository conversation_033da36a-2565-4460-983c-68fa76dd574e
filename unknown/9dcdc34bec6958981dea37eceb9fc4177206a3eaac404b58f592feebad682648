
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 50%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 210 100% 50%;
  }

  .dark {
    --background: 215 31% 11%;
    --foreground: 210 15% 95%;

    --card: 215 28% 17%;
    --card-foreground: 210 15% 94%;

    --popover: 215 28% 17%;
    --popover-foreground: 210 15% 94%;

    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 215 25% 25%;
    --secondary-foreground: 210 15% 94%;

    --muted: 215 25% 20%;
    --muted-foreground: 210 15% 70%;

    --accent: 215 28% 22%;
    --accent-foreground: 210 100% 75%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 25% 25%;
    --input: 215 25% 25%;
    --ring: 210 100% 50%;

    --sidebar-background: 215 30% 13%;
    --sidebar-foreground: 210 15% 85%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 25%;
    --sidebar-accent-foreground: 210 100% 75%;
    --sidebar-border: 215 25% 20%;
    --sidebar-ring: 210 100% 50%;
  }

  * {
    @apply border-border;
  }

  html, body {
    @apply bg-background text-foreground antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  .glass-card {
    @apply bg-white dark:bg-dark-card border border-border shadow-sm;
  }

  .input-glass {
    @apply bg-white/30 dark:bg-white/5 backdrop-blur-sm border border-white/30 dark:border-white/10 focus:border-primary/50 focus:ring-1 focus:ring-primary/30;
  }

  .button-glass {
    @apply bg-white/30 dark:bg-white/5 backdrop-blur-sm hover:bg-white/40 dark:hover:bg-white/10 transition-all duration-300;
  }

  .nav-item {
    @apply flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-secondary transition-all duration-300 text-foreground/80 hover:text-foreground font-medium;
  }

  .nav-item-active {
    @apply bg-primary/10 text-primary;
  }

  .section-animate {
    @apply opacity-0;
    animation: fade-in-up 0.5s ease-out forwards;
  }

  .chart-container {
    @apply rounded-xl overflow-hidden p-4 bg-white/50 dark:bg-dark-panel-light/50 backdrop-blur-sm border border-white/30 dark:border-white/5;
  }

  .card-hover {
    @apply transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg;
  }

  .sidebar-item {
    @apply flex items-center gap-2 p-2 rounded-lg text-sidebar-foreground transition-all duration-200 mb-1 hover:bg-sidebar-accent w-full;
  }

  .sidebar-item-active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground font-medium;
  }

  .sidebar-section {
    @apply pb-4 mb-4 border-b border-sidebar-border;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-in-delay-1 {
  animation-delay: 0.1s;
}

.fade-in-delay-2 {
  animation-delay: 0.2s;
}

.fade-in-delay-3 {
  animation-delay: 0.3s;
}

.fade-in-delay-4 {
  animation-delay: 0.4s;
}

.fade-in-delay-5 {
  animation-delay: 0.5s;
}
