/**
 * Utilitários para transformação de dados SQL
 */

/**
 * Converte valores null para undefined
 * @param value Valor a ser verificado
 * @returns O valor original ou undefined se for null
 */
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

/**
 * Interface para resultados brutos de usuários do SQL
 */
export interface UserRawResult {
  id: string;
  email: string;
  password?: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  username?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  avatar_url?: string;
  preferences?: any;
  is_active?: boolean;
}

/**
 * Interface para resultados brutos de perfis do SQL
 */
export interface ProfileRawResult {
  id: string;
  username: string;
  first_name: string;
  last_name: string;
  phone: string;
  avatar_url: string;
  preferences: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

/**
 * Interface para resultados brutos de empresas do SQL
 */
export interface CompanyRawResult {
  id: string;
  name: string;
  cnpj: string;
  phone: string | null;
  email: string | null;
  address_id: string | null;
  logo: string | null;
  active: boolean | null;
  calendar_type: string | null;
  created_at: Date | null;
  updated_at: Date | null;
  deleted_at: Date | null;
}

/**
 * Interface para resultados brutos de papéis do SQL
 */
export interface RoleRawResult {
  id: string;
  company_id: string;
  name: string;
  description: string | null;
  is_system_role: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
} 