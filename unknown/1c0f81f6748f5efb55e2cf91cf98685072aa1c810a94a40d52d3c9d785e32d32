#!/bin/bash

# Script para copiar arquivos SQL do diretório src/scripts para dist/scripts
# Este script deve ser executado dentro do contêiner

# Criar diretório de destino se não existir
mkdir -p /app/dist/scripts

# Copiar arquivos SQL
cp -f /app/src/scripts/*.sql /app/dist/scripts/

# Verificar se a cópia foi bem-sucedida
if [ $? -eq 0 ]; then
  echo "Arquivos SQL copiados com sucesso para /app/dist/scripts/"
  ls -la /app/dist/scripts/
else
  echo "Erro ao copiar arquivos SQL"
  exit 1
fi
