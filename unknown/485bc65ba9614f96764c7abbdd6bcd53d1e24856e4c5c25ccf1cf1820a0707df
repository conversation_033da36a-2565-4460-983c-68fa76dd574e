import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { RolePermissionsService } from './role-permissions.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { AssignPermissionsDto, RemovePermissionsDto } from './dto/role-permission.dto';
import { PermissionDto } from '../permissions/dto/permission.dto';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('role-permissions')
@Controller('companies/:companyId/roles/:roleId/permissions')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RolePermissionsController {
  constructor(private readonly rolePermissionsService: RolePermissionsService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar permissões de um papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'roleId', description: 'ID do papel' })
  @ApiResponse({
    status: 200,
    description: 'Lista de permissões do papel',
    type: [PermissionDto],
  })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  async getRolePermissions(
    @Param('companyId') companyId: string,
    @Param('roleId') roleId: string,
  ): Promise<PermissionDto[]> {
    return this.rolePermissionsService.getRolePermissions(companyId, roleId);
  }

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atribuir permissões a um papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'roleId', description: 'ID do papel' })
  @ApiBody({ type: AssignPermissionsDto })
  @ApiResponse({
    status: 201,
    description: 'Permissões atribuídas com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Papel ou permissões não encontrados' })
  @HttpCode(HttpStatus.CREATED)
  async assignPermissions(
    @Param('companyId') companyId: string,
    @Param('roleId') roleId: string,
    @Body() assignPermissionsDto: AssignPermissionsDto,
  ): Promise<{ message: string }> {
    await this.rolePermissionsService.assignPermissions(
      companyId,
      roleId,
      assignPermissionsDto,
    );
    return { message: 'Permissões atribuídas com sucesso' };
  }

  @Delete()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover permissões de um papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'roleId', description: 'ID do papel' })
  @ApiBody({ type: RemovePermissionsDto })
  @ApiResponse({
    status: 204,
    description: 'Permissões removidas com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removePermissions(
    @Param('companyId') companyId: string,
    @Param('roleId') roleId: string,
    @Body() removePermissionsDto: RemovePermissionsDto,
  ): Promise<void> {
    await this.rolePermissionsService.removePermissions(
      companyId,
      roleId,
      removePermissionsDto,
    );
  }
}
