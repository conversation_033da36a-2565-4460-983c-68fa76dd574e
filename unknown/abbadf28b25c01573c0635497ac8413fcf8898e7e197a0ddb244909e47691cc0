import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import {
  RoleDto,
  CreateRoleDto,
  UpdateRoleDto,
  RoleListDto,
} from './dto/role.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);

  constructor(private prisma: PrismaService) {}

  async create(createRoleDto: CreateRoleDto): Promise<RoleDto> {
    try {
      // Verificar se a empresa existe
      const company = await this.prisma.company.findUnique({
        where: { id: createRoleDto.companyId },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se já existe um papel com o mesmo nome na empresa
      const existingRole = await this.prisma.role.findFirst({
        where: {
          companyId: createRoleDto.companyId,
          name: createRoleDto.name,
          deletedAt: null,
        },
      });

      if (existingRole) {
        throw new ConflictException(
          `Já existe um papel com o nome ${createRoleDto.name} nesta empresa`,
        );
      }

      // Criar o papel
      const role = await this.prisma.role.create({
        data: {
          id: uuidv4(),
          companyId: createRoleDto.companyId,
          name: createRoleDto.name,
          description: createRoleDto.description,
          isAdmin: createRoleDto.isAdmin || false,
        },
      });

      return {
        id: role.id,
        companyId: role.companyId,
        name: role.name,
        description: role.description || undefined,
        isAdmin: role.isAdmin,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(`Erro ao criar papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAll(
    companyId: string,
    page = 1,
    limit = 10,
  ): Promise<RoleListDto> {
    try {
      const skip = (page - 1) * limit;

      // Contar total de papéis
      const total = await this.prisma.role.count({
        where: {
          companyId,
          deletedAt: null,
        },
      });

      // Buscar papéis paginados
      const roles = await this.prisma.role.findMany({
        where: {
          companyId,
          deletedAt: null,
        },
        skip,
        take: limit,
        orderBy: { name: 'asc' },
      });

      return {
        items: roles.map((role) => ({
          id: role.id,
          companyId: role.companyId,
          name: role.name,
          description: role.description || undefined,
          isAdmin: role.isAdmin,
          createdAt: role.createdAt,
          updatedAt: role.updatedAt,
        })),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Erro ao listar papéis: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findOne(id: string): Promise<RoleDto> {
    try {
      const role = await this.prisma.role.findFirst({
        where: {
          id,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado');
      }

      return {
        id: role.id,
        companyId: role.companyId,
        name: role.name,
        description: role.description || undefined,
        isAdmin: role.isAdmin,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleDto> {
    try {
      // Verificar se o papel existe
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id,
          deletedAt: null,
        },
      });

      if (!existingRole) {
        throw new NotFoundException('Papel não encontrado');
      }

      // Verificar se já existe outro papel com o mesmo nome na empresa
      if (updateRoleDto.name) {
        const duplicateRole = await this.prisma.role.findFirst({
          where: {
            companyId: existingRole.companyId,
            name: updateRoleDto.name,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (duplicateRole) {
          throw new ConflictException(
            `Já existe um papel com o nome ${updateRoleDto.name} nesta empresa`,
          );
        }
      }

      // Atualizar o papel
      const updatedRole = await this.prisma.role.update({
        where: { id },
        data: {
          name: updateRoleDto.name,
          description: updateRoleDto.description,
          isAdmin: updateRoleDto.isAdmin,
        },
      });

      return {
        id: updatedRole.id,
        companyId: updatedRole.companyId,
        name: updatedRole.name,
        description: updatedRole.description || undefined,
        isAdmin: updatedRole.isAdmin,
        createdAt: updatedRole.createdAt,
        updatedAt: updatedRole.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(
        `Erro ao atualizar papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Verificar se o papel existe
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id,
          deletedAt: null,
        },
        include: {
          userCompanyRoles: true,
        },
      });

      if (!existingRole) {
        throw new NotFoundException('Papel não encontrado');
      }

      // Verificar se o papel está sendo usado
      if (existingRole.userCompanyRoles.length > 0) {
        throw new ConflictException(
          'Este papel está em uso e não pode ser removido',
        );
      }

      // Remover as permissões do papel
      await this.prisma.rolePermission.deleteMany({
        where: { roleId: id },
      });

      // Soft delete do papel
      await this.prisma.role.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(`Erro ao remover papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Método para criar um papel de administrador padrão para uma empresa
  async createDefaultAdminRole(companyId: string): Promise<RoleDto> {
    try {
      // Verificar se a empresa existe
      const company = await this.prisma.company.findUnique({
        where: { id: companyId },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se já existe um papel de administrador para esta empresa
      const existingAdminRole = await this.prisma.role.findFirst({
        where: {
          companyId,
          name: 'Administrador',
          deletedAt: null,
        },
      });

      if (existingAdminRole) {
        return {
          id: existingAdminRole.id,
          companyId: existingAdminRole.companyId,
          name: existingAdminRole.name,
          description: existingAdminRole.description || undefined,
          isAdmin: existingAdminRole.isAdmin,
          createdAt: existingAdminRole.createdAt,
          updatedAt: existingAdminRole.updatedAt,
        };
      }

      // Criar o papel de administrador
      return this.create({
        companyId,
        name: 'Administrador',
        description: 'Acesso completo ao sistema',
        isAdmin: true,
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao criar papel de administrador padrão: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
