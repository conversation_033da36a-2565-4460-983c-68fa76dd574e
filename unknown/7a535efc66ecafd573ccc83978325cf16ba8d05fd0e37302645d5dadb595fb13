import { Module } from '@nestjs/common';
import { UserCompanyRolesController, UserRolesController } from './user-company-roles.controller';
import { UserCompanyRolesService } from './user-company-roles.service';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [UserCompanyRolesController, UserRolesController],
  providers: [UserCompanyRolesService],
  exports: [UserCompanyRolesService],
})
export class UserCompanyRolesModule {}
