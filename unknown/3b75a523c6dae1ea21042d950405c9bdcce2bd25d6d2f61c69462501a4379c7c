// backend/src/routes/currencies/currencies.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { CurrenciesService } from './currencies.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('currencies')
@Controller('currencies')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
export class CurrenciesController {
  constructor(private readonly currenciesService: CurrenciesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova moeda' })
  @ApiBody({ type: CreateCurrencyDto })
  @ApiResponse({
    status: 201,
    description: 'Moeda criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Moeda já existe' })
  create(@Body() createCurrencyDto: CreateCurrencyDto) {
    return this.currenciesService.create(createCurrencyDto);
  }

  @Get()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as moedas' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number })
  @ApiResponse({ status: 200, description: 'Lista de moedas' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite de itens por página inválido');
    }

    return this.currenciesService.findAll({
      page: pageNumber,
      limit: limitNumber,
    });
  }

  @Get(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter uma moeda pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da moeda', type: String })
  @ApiResponse({ status: 200, description: 'Moeda encontrada' })
  @ApiResponse({ status: 404, description: 'Moeda não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.currenciesService.findOne(id);
  }

  @Put(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma moeda' })
  @ApiParam({ name: 'id', description: 'ID da moeda', type: String })
  @ApiBody({ type: UpdateCurrencyDto })
  @ApiResponse({ status: 200, description: 'Moeda atualizada com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Moeda não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Código já existe' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCurrencyDto: UpdateCurrencyDto,
  ) {
    return this.currenciesService.update(id, updateCurrencyDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma moeda' })
  @ApiParam({ name: 'id', description: 'ID da moeda', type: String })
  @ApiResponse({ status: 204, description: 'Moeda removida com sucesso' })
  @ApiResponse({ status: 400, description: 'Não é possível remover a moeda' })
  @ApiResponse({ status: 404, description: 'Moeda não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.currenciesService.remove(id);
  }
}
