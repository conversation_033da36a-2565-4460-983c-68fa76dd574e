import {
  IsBoolean,
  <PERSON><PERSON>otEmpty,
  <PERSON>Optional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Profile {
  @ApiProperty({ description: 'ID único do perfil (mesmo do usuário)' })
  id: string;

  @ApiProperty({ description: 'Nome de usuário único' })
  username: string;

  @ApiProperty({ description: 'Primeiro nome' })
  firstName?: string;

  @ApiProperty({ description: 'Sobrenome' })
  lastName?: string;

  @ApiProperty({ description: 'Telefone' })
  phone?: string;

  @ApiProperty({ description: 'URL da foto de perfil' })
  avatarUrl?: string;

  @ApiProperty({ description: 'Preferências do usuário em formato JSON' })
  preferences?: any;

  @ApiProperty({ description: 'Status de ativação do perfil' })
  isActive: boolean;

  @ApiProperty({ description: 'Data de criação' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização' })
  updatedAt: Date;

  @ApiProperty({ description: 'Data de exclusão (soft delete)' })
  deletedAt?: Date;
}

export class CreateProfileDto {
  @ApiProperty({
    description: 'Nome de usuário único',
    example: 'joaosilva',
  })
  @IsString({ message: 'Nome de usuário deve ser uma string' })
  @IsNotEmpty({ message: 'Nome de usuário não pode ser vazio' })
  username: string;

  @ApiProperty({
    description: 'Primeiro nome',
    example: 'João',
    required: false,
  })
  @IsString({ message: 'Primeiro nome deve ser uma string' })
  @IsOptional()
  firstName?: string;

  @ApiProperty({
    description: 'Sobrenome',
    example: 'Silva',
    required: false,
  })
  @IsString({ message: 'Sobrenome deve ser uma string' })
  @IsOptional()
  lastName?: string;

  @ApiProperty({
    description: 'Telefone',
    example: '(11) 98765-4321',
    required: false,
  })
  @IsString({ message: 'Telefone deve ser uma string' })
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'URL da foto de perfil',
    example: 'https://exemplo.com/avatar.jpg',
    required: false,
  })
  @IsString({ message: 'URL da foto deve ser uma string' })
  @IsOptional()
  avatarUrl?: string;

  @ApiProperty({
    description: 'Preferências do usuário em formato JSON',
    example: { theme: 'dark', notifications: true },
    required: false,
  })
  @IsOptional()
  preferences?: any;

  @ApiProperty({
    description: 'Status de ativação do perfil',
    example: true,
    default: true,
    required: false,
  })
  @IsBoolean({ message: 'Status de ativação deve ser um booleano' })
  @IsOptional()
  isActive?: boolean = true;
}

export class UpdateProfileDto {
  @ApiProperty({
    description: 'Nome de usuário único',
    example: 'joaosilva',
    required: false,
  })
  @IsString({ message: 'Nome de usuário deve ser uma string' })
  @IsOptional()
  username?: string;

  @ApiProperty({
    description: 'Primeiro nome',
    example: 'João',
    required: false,
  })
  @IsString({ message: 'Primeiro nome deve ser uma string' })
  @IsOptional()
  firstName?: string;

  @ApiProperty({
    description: 'Sobrenome',
    example: 'Silva',
    required: false,
  })
  @IsString({ message: 'Sobrenome deve ser uma string' })
  @IsOptional()
  lastName?: string;

  @ApiProperty({
    description: 'Telefone',
    example: '(11) 98765-4321',
    required: false,
  })
  @IsString({ message: 'Telefone deve ser uma string' })
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'URL da foto de perfil',
    example: 'https://exemplo.com/avatar.jpg',
    required: false,
  })
  @IsString({ message: 'URL da foto deve ser uma string' })
  @IsOptional()
  avatarUrl?: string;

  @ApiProperty({
    description: 'Preferências do usuário em formato JSON',
    example: { theme: 'dark', notifications: true },
    required: false,
  })
  @IsOptional()
  preferences?: any;

  @ApiProperty({
    description: 'Status de ativação do perfil',
    example: true,
    required: false,
  })
  @IsBoolean({ message: 'Status de ativação deve ser um booleano' })
  @IsOptional()
  isActive?: boolean;
}

export class ProfileDto {
  @ApiProperty({ description: 'ID único do perfil (mesmo do usuário)' })
  id: string;

  @ApiProperty({ description: 'Nome de usuário único' })
  username: string;

  @ApiProperty({ description: 'Primeiro nome' })
  firstName?: string;

  @ApiProperty({ description: 'Sobrenome' })
  lastName?: string;

  @ApiProperty({ description: 'Telefone' })
  phone?: string;

  @ApiProperty({ description: 'URL da foto de perfil' })
  avatarUrl?: string;

  @ApiProperty({ description: 'Preferências do usuário em formato JSON' })
  preferences?: any;

  @ApiProperty({ description: 'Status de ativação do perfil' })
  isActive: boolean;
}
