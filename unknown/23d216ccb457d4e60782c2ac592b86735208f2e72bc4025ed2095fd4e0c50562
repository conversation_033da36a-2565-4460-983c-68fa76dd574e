# Especificações e Funcionalidades - Sistema de Gestão Financeira

## 1. Visão Geral

O Sistema de Gestão Financeira é uma aplicação web completa para controle financeiro empresarial, permitindo o gerenciamento de contas a pagar, contas a receber, entidades, transações e fluxo de caixa. O sistema foi desenvolvido com foco na experiência do usuário, oferecendo visualizações intuitivas, filtros contextuais e um dashboard informativo para acompanhamento dos indicadores financeiros.

## 2. Tecnologias Utilizadas

### 2.1. Frontend
- **React**: Framework principal para desenvolvimento da interface
- **TypeScript**: Linguagem de programação tipada
- **Vite**: Ferramenta de build e desenvolvimento
- **React Router DOM**: Gerenciamento de rotas
- **TailwindCSS**: Framework de estilização
- **Shadcn/UI**: Biblioteca de componentes
- **Lucide React**: Biblioteca de ícones
- **React Hook Form**: Gerenciamento de formulários
- **Zod**: Validação de dados
- **React Query**: Gerenciamento de cache e requisições
- **Recharts**: Biblioteca para criação de gráficos
- **Date-fns**: Manipulação de datas
- **Sonner**: Biblioteca para notificações toast

### 2.2. Backend
- **Supabase**: Plataforma de backend como serviço
  - Autenticação
  - Banco de dados PostgreSQL
  - Armazenamento de arquivos

## 3. Arquitetura do Sistema

### 3.1. Estrutura de Pastas
- **components/**: Componentes reutilizáveis
  - **accounts/**: Componentes relacionados a contas
  - **auth/**: Componentes de autenticação
  - **dashboard/**: Componentes do painel principal
  - **entity/**: Componentes de entidades (fornecedores/clientes)
  - **filter/**: Componentes de filtros
  - **layout/**: Componentes de estrutura da página
  - **profile/**: Componentes de perfil de usuário
  - **transaction/**: Componentes de transações
  - **ui/**: Componentes UI básicos
- **context/**: Contextos React para gerenciamento de estado
- **pages/**: Páginas da aplicação
- **services/**: Serviços de integração com APIs
- **utils/**: Funções utilitárias
- **hooks/**: Hooks personalizados
- **types/**: Tipos e interfaces TypeScript

### 3.2. Gerenciamento de Estado
O sistema utiliza Context API do React para gerenciamento de estado global:
- **auth-context**: Autenticação e gerenciamento de usuários
- **company-context**: Gerenciamento de empresas
- **calendar-context**: Gerenciamento de períodos e datas
- **filter-context**: Gerenciamento de filtros
- **finance-context**: Gerenciamento de dados financeiros
- **entity-context**: Gerenciamento de entidades

## 4. Funcionalidades Principais

### 4.1. Sistema de Filtro Global no Header

O sistema implementa um filtro global no cabeçalho que afeta todas as páginas do sistema:

#### 4.1.1. Filtro de Empresa
- Permite selecionar a empresa ativa no sistema
- Todos os dados exibidos são filtrados com base na empresa selecionada
- Os usuários só podem acessar empresas às quais têm permissão

#### 4.1.2. Filtro de Período
- Oferece seleção de períodos padrão:
  - Este Mês (padrão)
  - Próximo Mês
  - Mês Anterior
  - Esta Semana
  - Próxima Semana
  - Últimos 3 Meses
  - Este Ano
  - Hoje
- Suporte a períodos personalizados para empresas agrícolas
- Quando um filtro avançado de período está ativo, o filtro global exibe "Filtro Avançado"

### 4.2. Dashboard

O Dashboard oferece uma visão geral da situação financeira:

#### 4.2.1. Cards de Resumo
- Saldo Total: Exibe o saldo total da empresa
- A Receber: Valor total a receber com indicador de variação
- A Pagar: Valor total a pagar com indicador de variação
- Saldo Líquido: Saldo após pagamento de contas

#### 4.2.2. Gráficos
- **Fluxo de Caixa**: Gráfico de barras comparando receitas e despesas ao longo do tempo
- **Distribuição por Categoria**: Gráfico de pizza mostrando distribuição de despesas por categoria
- **Histórico de Saldo**: Gráfico de linha mostrando evolução do saldo ao longo do tempo

#### 4.2.3. Lista de Transações Recentes
- Exibe as transações mais recentes com status, valor e data

### 4.3. Contas a Pagar (Payables)

A página de contas a pagar permite gerenciar despesas e pagamentos:

#### 4.3.1. Lista de Contas
- Exibe todas as contas a pagar com:
  - Descrição
  - Fornecedor
  - Data de vencimento
  - Valor
  - Status (Pendente, Pago, Atrasado, Parcial)

#### 4.3.2. Filtros
- **Filtro de Pesquisa**: Busca por descrição ou fornecedor
- **Filtro de Status**: Filtra por status da conta
- **Filtro Avançado de Período**: Permite filtrar contas por períodos específicos:
  - Últimos 6 Meses
  - Último Ano
  - Últimos 2 Anos
  - Todo Histórico
  - Período personalizado (datas específicas)
  
#### 4.3.3. Funcionalidades de Pagamento
- **Pagamento Total**: Registra pagamento completo da conta
- **Pagamento Parcial**: Permite registrar pagamentos parciais
- **Adição de Juros/Descontos**: Permite adicionar juros ou descontos ao valor final

#### 4.3.4. Detalhes e Edição
- Visualização de detalhes de transações
- Edição de informações de contas
- Histórico de pagamentos parciais

### 4.4. Contas a Receber (Receivables)

Similar à página de contas a pagar, mas para gerenciamento de receitas:

#### 4.4.1. Lista de Contas
- Exibe todas as contas a receber com:
  - Descrição
  - Cliente
  - Data de vencimento
  - Valor
  - Status (Pendente, Recebido, Atrasado, Parcial)

#### 4.4.2. Filtros
- **Filtro de Pesquisa**: Busca por descrição ou cliente
- **Filtro de Status**: Filtra por status da conta
- **Filtro Avançado de Período**: Mesmo comportamento que em Contas a Pagar

#### 4.4.3. Funcionalidades de Recebimento
- **Recebimento Total**: Registra recebimento completo da conta
- **Recebimento Parcial**: Permite registrar recebimentos parciais
- **Adição de Juros/Descontos**: Permite adicionar juros ou descontos ao valor final

### 4.5. Mecanismo Avançado de Filtro de Período

O sistema implementa um mecanismo sofisticado de filtro de período:

#### 4.5.1. Funcionamento
- O filtro no header define um período global que se aplica a todas as páginas
- Os filtros avançados em páginas específicas sobrepõem o filtro global
- Quando um filtro avançado está ativo, o header exibe "Filtro Avançado"
- O sistema registra callbacks para limpar filtros específicos

#### 4.5.2. Integração com Datas
- Conversão entre formatos de data brasileiros e internacionais
- Verificação se uma data está dentro de um intervalo específico
- Suporte a períodos especiais como "Todo Histórico"

### 4.6. Gerenciamento de Entidades

O sistema permite cadastro e gerenciamento de entidades:
- Fornecedores
- Clientes

### 4.7. Transações e Transferências

- Registro de novas despesas
- Registro de novas receitas
- Transferências entre contas
- Visualização e edição de transações existentes

### 4.8. Configurações

#### 4.8.1. Configurações da Empresa
- Dados cadastrais
- Configuração do calendário (padrão ou períodos)
- Gerenciamento de contas bancárias

#### 4.8.2. Gerenciamento de Usuários
- Cadastro de usuários
- Atribuição de permissões
- Vinculação de usuários a empresas

### 4.9. Sistema de Calendário Dual (Padrão e Periódico)

O sistema oferece uma funcionalidade flexível de calendário para atender diferentes modelos de negócio:

#### 4.9.1. Tipos de Calendário
- **Calendário Padrão**: 
  - Utiliza períodos convencionais baseados no calendário civil (mês, semana, ano)
  - Ideal para empresas comerciais, industriais e de serviços com ciclos regulares
  - Oferece opções como "Este Mês", "Mês Anterior", "Este Ano", etc.

- **Calendário Periódico**:
  - Permite definir períodos customizados com datas de início e fim específicas
  - Ideal para empresas agrícolas que operam com base em períodos ou ciclos produtivos
  - Cada período possui nome personalizado e intervalo de datas definido

#### 4.9.2. Configuração no Cadastro de Empresas
- Durante o cadastro ou edição de uma empresa, é possível selecionar o tipo de calendário:
  - Opção "Padrão" (standard): Ativa o sistema de períodos convencionais
  - Opção "Periódico" (periodic): Ativa o sistema de períodos customizados
- A propriedade `calendarType` na entidade Company define o tipo de calendário utilizado
- Essa configuração é armazenada no banco de dados na coluna `calendar_type` da tabela `companies`

#### 4.9.3. Gerenciamento de Períodos Customizados
- Para empresas com `calendarType = "periodic"`, o sistema oferece uma interface de gerenciamento de períodos
- Interface disponível na seção "Configurações > Empresa > Períodos"
- Funcionalidades:
  - Cadastro de novos períodos (nome, data inicial, data final)
  - Edição de períodos existentes
  - Exclusão de períodos
  - Listagem de períodos cadastrados por empresa

#### 4.9.4. Impacto no Sistema
- O tipo de calendário afeta diretamente o comportamento do filtro global no header:
  - Calendário Padrão: Exibe opções como "Este Mês", "Mês Anterior", etc.
  - Calendário Periódico: Exibe lista de períodos cadastrados para a empresa
- Quando a empresa é alterada, o sistema:
  1. Detecta o tipo de calendário da nova empresa selecionada
  2. Atualiza o filtro de período no header de acordo com o tipo
  3. Para calendário padrão, define "Este Mês" como período padrão
  4. Para calendário periódico, carrega e define o primeiro período disponível
- O contexto `calendar-context` coordena essa lógica, adaptando-se dinamicamente ao tipo de calendário
- Quando um filtro periódico está ativo, todas as análises financeiras, relatórios e listagens são limitados ao intervalo de datas do período selecionado

#### 4.9.5. Persistência e Transição
- Ao alternar entre empresas com diferentes tipos de calendário, o sistema:
  - Salva os estados anteriores para rápida restauração
  - Carrega automaticamente os períodos apropriados
  - Mantém a consistência dos dados exibidos
- A transição entre tipos de calendário é tratada pelo método `handleSetCurrentPeriod` no contexto de calendário
- O sistema verifica a compatibilidade entre o tipo de período e o tipo de calendário da empresa

#### 4.9.6. Integração com Filtros Avançados
- O filtro avançado de período (AdvancedPeriodFilter) funciona independentemente do tipo de calendário
- Quando um filtro avançado é aplicado, o sistema marca o período global como "none"
- No header, o sistema exibe "Filtro Avançado" indicando que um filtro específico está sobrepondo o filtro global

### 4.10. Formulários e Campos de Transações

O sistema oferece formulários completos para o gerenciamento de contas, com campos especializados e funcionalidades avançadas.

#### 4.10.1. Formulário de Nova Despesa/Contas a Pagar

##### Campos Básicos
- **Descrição**: Nome/descrição da despesa
- **Fornecedor**: Seletor de entidade com autocomplete para escolha do fornecedor
- **Valor (R$)**: Campo monetário com formatação automática para moeda brasileira
- **Data de Vencimento**: Campo de data para definir quando a despesa vence
- **Categoria**: Seletor com opções pré-definidas (Alimentação, Transporte, Serviços, etc.)
- **Conta**: Seletor de conta bancária onde a despesa será debitada
- **Marcar como Paga**: Checkbox para indicar se a despesa já foi paga no momento do cadastro

##### Campos de Parcelamento
- **Dividir em Parcelas**: Checkbox para ativar o parcelamento da despesa
- **Número de Parcelas**: Campo numérico para definir quantas parcelas (2-36)
- **Frequência**: Seletor com opções como Mensal, Quinzenal, Semanal
- **Data da Primeira Parcela**: Campo de data para definir quando a primeira parcela vence

##### Campos de Valores Adicionais
- **Juros**: Campo monetário para adicionar juros ao valor original
- **Desconto**: Campo monetário para aplicar desconto ao valor original
- **Valor Final**: Campo de exibição que mostra o cálculo (Valor + Juros - Desconto)

#### 4.10.2. Formulário de Nova Receita/Contas a Receber

##### Campos Básicos
- **Descrição**: Nome/descrição da receita
- **Cliente**: Seletor de entidade com autocomplete para escolha do cliente
- **Valor (R$)**: Campo monetário com formatação automática para moeda brasileira
- **Data de Vencimento**: Campo de data para definir quando a receita vence
- **Categoria**: Seletor com opções pré-definidas (Vendas, Serviços, Investimentos, etc.)
- **Conta**: Seletor de conta bancária onde a receita será creditada
- **Marcar como Recebida**: Checkbox para indicar se a receita já foi recebida no momento do cadastro

##### Campos de Parcelamento
- Idêntico ao formulário de despesas, permitindo parcelar receitas

##### Campos de Valores Adicionais
- Idêntico ao formulário de despesas, permitindo adicionar juros ou descontos

#### 4.10.3. Lógica de Parcelamento

O sistema implementa uma lógica sofisticada para o parcelamento de transações:

1. **Ativação do Parcelamento**:
   - Ao ativar a opção "Dividir em Parcelas", o sistema exibe campos adicionais
   - O parcelamento pode ser aplicado tanto a despesas quanto a receitas

2. **Cálculo de Datas de Vencimento**:
   - O sistema calcula automaticamente as datas de vencimento de cada parcela
   - Utiliza a "Data da Primeira Parcela" como referência inicial
   - Aplica a "Frequência" selecionada para determinar os intervalos:
     - **Mensal**: Adiciona 1 mês a cada parcela
     - **Quinzenal**: Adiciona 15 dias a cada parcela
     - **Semanal**: Adiciona 7 dias a cada parcela

3. **Geração de Parcelas**:
   - O valor total é dividido igualmente pelo número de parcelas
   - Pequenas diferenças de arredondamento são ajustadas na última parcela
   - Cada parcela é registrada como uma transação individual vinculada à transação principal
   - As parcelas compartilham metadados como categoria, entidade e referência

4. **Visualização e Gerenciamento**:
   - As parcelas são exibidas nas listagens com indicação de qual parcela é (ex: "2/12")
   - O sistema rastreia o status de pagamento de cada parcela individualmente
   - É possível ver o histórico completo de parcelas na tela de detalhes da transação

#### 4.10.4. Funcionalidades de Pagamento/Recebimento

O sistema oferece diferentes modos de registro de pagamentos/recebimentos:

1. **Marcar como Paga/Recebida no Cadastro**:
   - Durante o cadastro inicial, é possível marcar a transação como já paga/recebida
   - Nesse caso, o sistema gera automaticamente uma transação financeira correspondente
   - Os campos de data e conta bancária são utilizados para registrar a movimentação

2. **Pagamento/Recebimento Parcial**:
   - Na listagem de contas, é possível registrar um pagamento/recebimento parcial
   - Um modal é exibido com os seguintes campos:
     - **Valor do Pagamento/Recebimento**: Quanto está sendo pago/recebido
     - **Juros**: Valor adicional de juros (não afeta o valor principal)
     - **Desconto**: Valor de desconto aplicado (não afeta o valor principal)
     - **Valor Final**: Cálculo automático (Valor + Juros - Desconto)
     - **Conta**: Seletor de conta bancária onde o pagamento/recebimento será processado
     - **Data**: Data em que o pagamento/recebimento foi realizado
   - O sistema valida se o valor não excede o saldo pendente
   - Após confirmação, o status da conta é atualizado para "Parcial"

3. **Pagamento/Recebimento Total**:
   - Semelhante ao pagamento parcial, mas com o valor total pendente pré-preenchido
   - Após confirmação, o status da conta é atualizado para "Pago/Recebido"

4. **Geração Automática de Transações**:
   - Ao registrar um pagamento/recebimento (parcial ou total), o sistema:
     1. Atualiza o status da conta e o valor pago/recebido
     2. Cria automaticamente uma transação de débito/crédito na conta bancária selecionada
     3. Associa a transação bancária à conta a pagar/receber para rastreabilidade
   - Essas transações geradas são visíveis na tela de Transações e no extrato da conta bancária

5. **Estorno de Pagamento/Recebimento**:
   - Na tela de detalhes, é possível estornar um pagamento/recebimento já registrado
   - O sistema reverte a transação bancária e atualiza o status da conta
   - Um registro do estorno é mantido para fins de auditoria

## 5. Segurança e Controle de Acesso

- Autenticação via Supabase
- Sistema de roles e permissões
- Segregação de dados por empresa
- Proteção de rotas por nível de acesso

## 6. Reatividade e Desempenho

- Uso de React Query para cache e revalidação de dados
- Filtros otimizados com debounce para evitar requisições desnecessárias
- Carregamento sob demanda de dados para tabelas extensas

## 7. Experiência do Usuário

- Interface responsiva para dispositivos móveis e desktop
- Tema claro/escuro
- Notificações via toast para feedback do usuário
- Validação de formulários em tempo real
- Confirmações para ações críticas 