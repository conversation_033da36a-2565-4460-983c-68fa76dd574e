import { FilterOption } from '@/hooks/useFilters';
import { QuickFilterOption } from '@/components/filter/QuickFilters';

// Definição dos filtros rápidos
export const quickFilters: QuickFilterOption[] = [
  {
    id: 'description',
    label: 'Descrição',
    type: 'text',
    placeholder: 'Buscar por descrição',
  },
  {
    id: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { label: 'Pendente', value: 'PENDING' },
      { label: 'Concluído', value: 'COMPLETED' },
      { label: 'Cancelado', value: 'CANCELLED' },
    ],
    placeholder: 'Todos os status',
  },
];

// Definição dos filtros avançados
export const transactionFilters: FilterOption[] = [
  {
    id: 'type',
    label: 'Tipo',
    type: 'select',
    options: [
      { label: 'Receita', value: 'INCOME' },
      { label: 'Despesa', value: 'EXPENSE' },
    ],
    placeholder: 'Todos os tipos',
  },
  {
    id: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { label: 'Pendente', value: 'PENDING' },
      { label: 'Concluído', value: 'COMPLETED' },
      { label: 'Cancelado', value: 'CANCELLED' },
    ],
    placeholder: 'Todos os status',
  },
  {
    id: 'dateRange',
    label: 'Período',
    type: 'dateRange',
    defaultValue: { from: null, to: null },
  },
  {
    id: 'amountRange',
    label: 'Valor',
    type: 'numberRange',
    defaultValue: { from: null, to: null },
  },
  {
    id: 'category',
    label: 'Categoria',
    type: 'text',
    placeholder: 'Filtrar por categoria',
  },
  {
    id: 'accountId',
    label: 'Conta',
    type: 'select',
    options: [], // Será preenchido dinamicamente
    placeholder: 'Todas as contas',
  },
  {
    id: 'entityId',
    label: 'Entidade',
    type: 'select',
    options: [], // Será preenchido dinamicamente
    placeholder: 'Todas as entidades',
  },
  {
    id: 'projectId',
    label: 'Projeto',
    type: 'select',
    options: [], // Será preenchido dinamicamente
    placeholder: 'Todos os projetos',
  },
]; 