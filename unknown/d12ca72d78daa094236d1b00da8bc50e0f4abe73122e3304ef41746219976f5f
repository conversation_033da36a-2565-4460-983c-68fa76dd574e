
import { cn } from "@/lib/utils";
import { CSSProperties, ReactNode } from "react";

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  hoverEffect?: boolean;
  style?: CSSProperties;
}

const GlassCard = ({ children, className, hoverEffect = false, style }: GlassCardProps) => {
  return (
    <div 
      className={cn(
        "bg-white dark:bg-dark-card rounded-2xl p-6 animate-fade-in border border-border dark:border-gray-700/50 shadow-sm", 
        hoverEffect && "card-hover", 
        className
      )}
      style={{
        ...style,
        backgroundImage: "var(--tw-gradient-stops)"
      }}
    >
      {children}
    </div>
  );
};

export default GlassCard;
