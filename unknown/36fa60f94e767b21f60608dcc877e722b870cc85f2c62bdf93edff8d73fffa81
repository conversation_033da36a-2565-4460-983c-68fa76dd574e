
import { Bank } from "@/types/bank";
import { <PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import IconButton from "@/components/ui-custom/IconButton";
import { BankPagination } from "./BankPagination";

interface BankListProps {
  banks: Bank[];
  onEditBank: (bank: Bank) => void;
  onDeleteBank: (bank: Bank) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  searchTerm: string;
}

export const BankList = ({
  banks,
  onEditBank,
  onDeleteBank,
  currentPage,
  totalPages,
  onPageChange,
  searchTerm
}: BankListProps) => {
  return (
    <div className="bg-white dark:bg-dark-card rounded-lg border shadow">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Logo</TableHead>
            <TableHead>Código</TableHead>
            <TableHead>Nome do Banco</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {banks.length > 0 ? (
            banks.map((bank) => (
              <TableRow key={bank.id}>
                <TableCell>
                  <div className="w-10 h-10 bg-blue-100 text-blue-700 rounded-md flex items-center justify-center">
                    {bank.logo ? (
                      <img 
                        src={bank.logo} 
                        alt={`Logo ${bank.name}`}
                        className="w-full h-full object-contain rounded-md"
                      />
                    ) : (
                      bank.name.charAt(0)
                    )}
                  </div>
                </TableCell>
                <TableCell className="font-medium">{bank.code}</TableCell>
                <TableCell>{bank.name}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <IconButton 
                      icon={<Pencil className="h-4 w-4" />}
                      variant="ghost"
                      size="sm"
                      label="Editar"
                      onClick={() => onEditBank(bank)}
                    />
                    <IconButton 
                      icon={<Trash className="h-4 w-4" />}
                      variant="ghost"
                      size="sm"
                      label="Excluir"
                      onClick={() => onDeleteBank(bank)}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                {searchTerm 
                  ? "Nenhum banco encontrado com os critérios de busca."
                  : "Nenhum banco cadastrado. Clique em 'Novo Banco' para adicionar."}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      
      {totalPages > 1 && (
        <BankPagination 
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
        />
      )}
    </div>
  );
};
