import api from './axios';
import {
  AccountReceivable,
  CreateAccountReceivableRequest,
  UpdateAccountReceivableRequest,
  PaginatedResponse,
  ApiResponse
} from '@/types/api';

export const accountsReceivableService = {
  // Listar todas as contas a receber com paginação
  getAccountsReceivable: async (
    page = 1,
    limit = 10,
    search?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string
  ): Promise<PaginatedResponse<AccountReceivable>> => {
    const params = { page, limit, search, status, startDate, endDate, categoryId, entityId };
    const response = await api.get<PaginatedResponse<AccountReceivable>>('/accounts-receivable', { params });
    return response.data;
  },

  // Obter conta a receber por ID
  getAccountReceivableById: async (id: string): Promise<AccountReceivable> => {
    const response = await api.get<ApiResponse<AccountReceivable>>(`/accounts-receivable/${id}`);
    return response.data.data;
  },

  // Criar conta a receber
  createAccountReceivable: async (data: CreateAccountReceivableRequest): Promise<AccountReceivable> => {
    const response = await api.post<ApiResponse<AccountReceivable>>('/accounts-receivable', data);
    return response.data.data;
  },

  // Atualizar conta a receber
  updateAccountReceivable: async (id: string, data: UpdateAccountReceivableRequest): Promise<AccountReceivable> => {
    const response = await api.put<ApiResponse<AccountReceivable>>(`/accounts-receivable/${id}`, data);
    return response.data.data;
  },

  // Excluir conta a receber
  deleteAccountReceivable: async (id: string): Promise<void> => {
    await api.delete(`/accounts-receivable/${id}`);
  },

  // Marcar como recebida (DEPRECATED - usar transactionService.createTransaction)
  // Este método foi mantido para compatibilidade, mas será removido em versões futuras
  receiveAccountReceivable: async (id: string, receiveData: {
    receiveDate?: string,
    bankAccountId: string,
    amount?: number,
    description?: string,
    paymentMethodId?: string,
    notes?: string
  }): Promise<AccountReceivable> => {
    // Obter a conta a receber atual
    const accountReceivable = await accountsReceivableService.getAccountReceivableById(id);

    console.warn('O endpoint /receive foi removido. Use transactionService.createTransaction para criar uma transação do tipo "income" vinculada à conta a receber.');

    // Retornar a conta a receber sem modificá-la
    return accountReceivable;
  }
};
