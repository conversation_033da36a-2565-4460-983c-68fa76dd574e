#!/bin/bash
set -e

echo "=== Iniciando configuração do banco de dados ==="

# Gerar o cliente Prisma
echo "Gerando cliente Prisma..."
npx prisma generate

# Verificar se o banco de dados está acessível
echo "Verificando conexão com o banco de dados..."
MAX_RETRIES=30
RETRY_INTERVAL=2
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if psql $DATABASE_URL -c "SELECT 1" > /dev/null 2>&1; then
    echo "Conexão com o banco de dados estabelecida com sucesso!"
    break
  fi

  echo "Não foi possível conectar ao banco de dados. Tentando novamente em $RETRY_INTERVAL segundos..."
  sleep $RETRY_INTERVAL
  RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
  echo "Falha ao conectar ao banco de dados após $MAX_RETRIES tentativas."
  exit 1
fi

# Verificar se as tabelas de roles e user_company_roles já existem
ROLES_TABLE_EXISTS=$(psql $DATABASE_URL -t -c "SELECT EXISTS(SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'roles')" | tr -d '[:space:]')
UCR_TABLE_EXISTS=$(psql $DATABASE_URL -t -c "SELECT EXISTS(SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles')" | tr -d '[:space:]')

# Aplicar migrações do Prisma apenas se as tabelas não existirem
if [ "$ROLES_TABLE_EXISTS" = "f" ] && [ "$UCR_TABLE_EXISTS" = "f" ]; then
  echo "Tabelas roles e user_company_roles não existem. Aplicando migrações do Prisma..."
  npx prisma migrate deploy
else
  echo "Tabelas roles e/ou user_company_roles já existem. Pulando migrações do Prisma."
  # Usar db push para sincronizar o esquema sem aplicar migrações
  npx prisma db push --skip-generate
fi

# Verificar novamente se as tabelas existem após as migrações
ROLES_TABLE_EXISTS=$(psql $DATABASE_URL -t -c "SELECT EXISTS(SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'roles')" | tr -d '[:space:]')
UCR_TABLE_EXISTS=$(psql $DATABASE_URL -t -c "SELECT EXISTS(SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles')" | tr -d '[:space:]')

# Executar script SQL para criar tabelas apenas se ainda não existirem
if [ "$ROLES_TABLE_EXISTS" = "f" ] || [ "$UCR_TABLE_EXISTS" = "f" ]; then
  echo "Executando script SQL para criar tabelas que não existem..."
  psql $DATABASE_URL -f /app/src/scripts/create-roles-table.sql
else
  echo "Tabelas roles e user_company_roles já existem. Pulando criação de tabelas via SQL."
fi

# Executar script SQL de seed para popular as tabelas
echo "Executando script SQL de seed para popular as tabelas..."
if [ -f "/app/prisma/seed_complete.sql" ]; then
  echo "Usando script de seed completo..."
  psql $DATABASE_URL -f /app/prisma/seed_complete.sql
else
  echo "Script de seed completo não encontrado, usando script padrão..."
  psql $DATABASE_URL -f /app/prisma/seed.sql
fi

# Aplicar triggers RBAC
echo "Aplicando triggers RBAC..."
if [ -f "/app/prisma/migrations/20250407_add_rbac_triggers.sql" ]; then
  echo "Aplicando triggers RBAC..."
  psql $DATABASE_URL -f /app/prisma/migrations/20250407_add_rbac_triggers.sql
  echo "Triggers RBAC aplicados com sucesso!"
else
  echo "Arquivo de triggers RBAC não encontrado. Pulando esta etapa."
fi

# Verificar e criar papéis padrão
echo "Configurando papéis padrão..."
cd /app && npx ts-node /app/src/scripts/setup-roles.ts

# Associar usuários às empresas
echo "Associando usuários às empresas..."
cd /app && npx ts-node /app/src/scripts/associate-users-companies.ts

echo "=== Configuração do banco de dados concluída com sucesso ==="
