import { Module } from '@nestjs/common';
import { RecurringSchedulesService } from './recurring-schedules.service';
import { RecurringSchedulesController } from './recurring-schedules.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { RbacModule } from '../rbac/rbac.module';

@Module({
  imports: [PrismaModule, RbacModule],
  controllers: [RecurringSchedulesController],
  providers: [RecurringSchedulesService],
  exports: [RecurringSchedulesService],
})
export class RecurringSchedulesModule {}
