import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsUUID, IsDateString, IsEnum, IsString, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { TransactionType } from './create-transaction.dto';

export class FindAllTransactionsDto {
  @ApiPropertyOptional({ 
    description: 'Page number (pagination)', 
    default: 1, 
    minimum: 1 
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({ 
    description: 'Items per page (pagination)', 
    default: 10, 
    minimum: 1 
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({ 
    description: 'Filter by transaction type', 
    enum: TransactionType 
  })
  @IsOptional()
  @IsEnum(TransactionType)
  type?: TransactionType;

  @ApiPropertyOptional({ 
    description: 'Filter by start date (inclusive)', 
    example: '2025-01-01T00:00:00Z' 
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by end date (inclusive)', 
    example: '2025-12-31T23:59:59Z' 
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by bank account ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  bankAccountId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by category ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by entity ID', 
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;

  @ApiPropertyOptional({ 
    description: 'Search term (searches in description)', 
    example: 'invoice' 
  })
  @IsOptional()
  @IsString()
  search?: string;
}
