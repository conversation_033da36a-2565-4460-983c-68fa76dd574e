import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import {
  UserCompanyRoleDto,
  CreateUserCompanyRoleDto,
  UserCompanyRoleListDto,
  AssignRolesDto,
  RemoveRolesDto,
} from './dto/user-company-role.dto';
import { RoleDto } from '../roles/dto/role.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UserCompanyRolesService {
  private readonly logger = new Logger(UserCompanyRolesService.name);

  constructor(private prisma: PrismaService) {}

  async create(
    createUserCompanyRoleDto: CreateUserCompanyRoleDto,
  ): Promise<UserCompanyRoleDto> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: createUserCompanyRoleDto.userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: createUserCompanyRoleDto.companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se o papel existe
      const role = await this.prisma.role.findFirst({
        where: {
          id: createUserCompanyRoleDto.roleId,
          companyId: createUserCompanyRoleDto.companyId,
          deletedAt: null,
        },
      });

      if (!role) {
        throw new NotFoundException('Papel não encontrado ou não pertence à empresa');
      }

      // Verificar se já existe uma associação idêntica
      const existingAssociation = await this.prisma.userCompanyRole.findFirst({
        where: {
          userId: createUserCompanyRoleDto.userId,
          companyId: createUserCompanyRoleDto.companyId,
          roleId: createUserCompanyRoleDto.roleId,
          deletedAt: null,
        },
      });

      if (existingAssociation) {
        throw new ConflictException('Esta associação já existe');
      }

      // Criar a associação
      const userCompanyRole = await this.prisma.userCompanyRole.create({
        data: {
          id: uuidv4(),
          userId: createUserCompanyRoleDto.userId,
          companyId: createUserCompanyRoleDto.companyId,
          roleId: createUserCompanyRoleDto.roleId,
        },
      });

      return {
        id: userCompanyRole.id,
        userId: userCompanyRole.userId,
        companyId: userCompanyRole.companyId,
        roleId: userCompanyRole.roleId,
        createdAt: userCompanyRole.createdAt,
        updatedAt: userCompanyRole.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(
        `Erro ao criar associação usuário-empresa-papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(
    companyId?: string,
    userId?: string,
    page = 1,
    limit = 10,
  ): Promise<UserCompanyRoleListDto> {
    try {
      const skip = (page - 1) * limit;

      // Construir o filtro
      const where: any = { deletedAt: null };
      if (companyId) where.companyId = companyId;
      if (userId) where.userId = userId;

      // Contar total de associações
      const total = await this.prisma.userCompanyRole.count({ where });

      // Buscar associações paginadas
      const userCompanyRoles = await this.prisma.userCompanyRole.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      });

      return {
        items: userCompanyRoles.map((ucr) => ({
          id: ucr.id,
          userId: ucr.userId,
          companyId: ucr.companyId,
          roleId: ucr.roleId,
          createdAt: ucr.createdAt,
          updatedAt: ucr.updatedAt,
        })),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Erro ao listar associações usuário-empresa-papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: string): Promise<UserCompanyRoleDto> {
    try {
      const userCompanyRole = await this.prisma.userCompanyRole.findFirst({
        where: {
          id,
          deletedAt: null,
        },
      });

      if (!userCompanyRole) {
        throw new NotFoundException('Associação usuário-empresa-papel não encontrada');
      }

      return {
        id: userCompanyRole.id,
        userId: userCompanyRole.userId,
        companyId: userCompanyRole.companyId,
        roleId: userCompanyRole.roleId,
        createdAt: userCompanyRole.createdAt,
        updatedAt: userCompanyRole.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar associação usuário-empresa-papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Verificar se a associação existe
      const userCompanyRole = await this.prisma.userCompanyRole.findFirst({
        where: {
          id,
          deletedAt: null,
        },
      });

      if (!userCompanyRole) {
        throw new NotFoundException('Associação usuário-empresa-papel não encontrada');
      }

      // Soft delete da associação
      await this.prisma.userCompanyRole.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao remover associação usuário-empresa-papel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getUserRoles(
    companyId: string,
    userId: string,
  ): Promise<RoleDto[]> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Buscar os papéis do usuário na empresa
      const roles = await this.prisma.role.findMany({
        where: {
          companyId,
          deletedAt: null,
          userCompanyRoles: {
            some: {
              userId,
              companyId,
              deletedAt: null,
            },
          },
        },
      });

      return roles.map((role) => ({
        id: role.id,
        companyId: role.companyId,
        name: role.name,
        description: role.description || undefined,
        isAdmin: role.isAdmin,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
      }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar papéis do usuário: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async assignRoles(
    companyId: string,
    userId: string,
    assignRolesDto: AssignRolesDto,
  ): Promise<void> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se todos os papéis existem e pertencem à empresa
      const roles = await this.prisma.role.findMany({
        where: {
          id: { in: assignRolesDto.roleIds },
          companyId,
          deletedAt: null,
        },
      });

      if (roles.length !== assignRolesDto.roleIds.length) {
        throw new NotFoundException(
          'Um ou mais papéis não foram encontrados ou não pertencem à empresa',
        );
      }

      // Usar transação para garantir consistência
      await this.prisma.$transaction(async (prisma) => {
        // Criar as associações entre usuário, empresa e papéis
        for (const roleId of assignRolesDto.roleIds) {
          // Verificar se a associação já existe
          const existingAssociation = await prisma.userCompanyRole.findFirst({
            where: {
              userId,
              companyId,
              roleId,
              deletedAt: null,
            },
          });

          if (!existingAssociation) {
            await prisma.userCompanyRole.create({
              data: {
                id: uuidv4(),
                userId,
                companyId,
                roleId,
              },
            });
          }
        }
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao atribuir papéis ao usuário: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async removeRoles(
    companyId: string,
    userId: string,
    removeRolesDto: RemoveRolesDto,
  ): Promise<void> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Usar transação para garantir consistência
      await this.prisma.$transaction(async (prisma) => {
        // Soft delete das associações entre usuário, empresa e papéis
        for (const roleId of removeRolesDto.roleIds) {
          await prisma.userCompanyRole.updateMany({
            where: {
              userId,
              companyId,
              roleId,
              deletedAt: null,
            },
            data: {
              deletedAt: new Date(),
            },
          });
        }
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao remover papéis do usuário: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Método para verificar se um usuário está associado a uma empresa
  async isUserAssociatedWithCompany(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    try {
      const association = await this.prisma.userCompanyRole.findFirst({
        where: {
          userId,
          companyId,
          deletedAt: null,
        },
      });

      return !!association;
    } catch (error) {
      this.logger.error(
        `Erro ao verificar associação usuário-empresa: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
