
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Building2, Lock, Settings as SettingsIcon, Users } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

const Settings = () => {
  const navigate = useNavigate();
  
  // Automatically redirect to companies tab when landing on /settings
  useEffect(() => {
    navigate("/settings/companies");
  }, [navigate]);
  
  return (
    <Layout>
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-8">
          <SettingsIcon className="h-8 w-8 text-[#007FFF] mr-3" />
          <h1 className="text-3xl font-bold">Configurações</h1>
        </div>
        
        <Tabs defaultValue="companies" className="w-full" onValueChange={(value) => navigate(`/settings/${value}`)}>
          <TabsList className="w-full justify-start mb-6 border-b rounded-none bg-transparent p-0 h-auto">
            <TabsTrigger 
              value="companies" 
              className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent data-[state=active]:border-[#007FFF] data-[state=active]:text-[#007FFF] data-[state=active]:font-medium"
            >
              <Building2 className="h-5 w-5" />
              <span>Empresas</span>
            </TabsTrigger>
            
            <TabsTrigger 
              value="users" 
              className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent data-[state=active]:border-[#007FFF] data-[state=active]:text-[#007FFF] data-[state=active]:font-medium"
            >
              <Users className="h-5 w-5" />
              <span>Usuários</span>
            </TabsTrigger>
            
            <TabsTrigger 
              value="security" 
              className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent data-[state=active]:border-[#007FFF] data-[state=active]:text-[#007FFF] data-[state=active]:font-medium"
            >
              <Lock className="h-5 w-5" />
              <span>Segurança</span>
            </TabsTrigger>
            
            <TabsTrigger 
              value="general" 
              className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent data-[state=active]:border-[#007FFF] data-[state=active]:text-[#007FFF] data-[state=active]:font-medium"
            >
              <SettingsIcon className="h-5 w-5" />
              <span>Geral</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="p-8 flex flex-col items-center justify-center">
          <p className="text-lg text-gray-500">Redirecionando para configurações de empresas...</p>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
