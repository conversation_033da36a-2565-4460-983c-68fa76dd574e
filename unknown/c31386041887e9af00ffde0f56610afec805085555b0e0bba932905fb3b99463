import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TokenPayload {
  sub: string;
  email: string;
  companyId?: string; // ID da empresa do usuário (opcional)
}

export class AuthTokens {
  @ApiProperty({
    description: 'Token JWT para autenticação',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token para renovar o token JWT',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  refreshToken: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token para renovar o token JWT',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  @IsString({ message: 'Refresh token deve ser uma string' })
  @IsNotEmpty({ message: 'Refresh token não pode ser vazio' })
  refreshToken: string;
}
