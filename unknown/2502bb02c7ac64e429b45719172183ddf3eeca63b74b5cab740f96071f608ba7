
import { Badge } from "@/components/ui/badge";

interface AccountsReceivableStatusBadgeProps {
  status: string;
}

export default function AccountsReceivableStatusBadge({
  status,
}: AccountsReceivableStatusBadgeProps) {
  switch (status) {
    case 'pending':
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Pendente</Badge>;
    case 'partial':
      return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Parcial</Badge>;
    case 'received':
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Recebido</Badge>;
    case 'overdue':
      return <Badge className="bg-[#ea384c] text-white border-0">Em Atraso</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}
