// backend/src/guards/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../services/prisma.service';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Se não houver papéis definidos, permite o acesso
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    // Se não houver usuário ou companyId, nega o acesso
    if (!user || !user.userId || !user.companyId) {
      throw new ForbiddenException('Acesso negado: usuário não autenticado corretamente');
    }

    // Verificar se o usuário tem algum dos papéis necessários
    const userRoles = await this.prisma.$queryRaw<{ name: string }[]>`
      SELECT r.name
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${user.userId}::uuid
      AND ucr.company_id = ${user.companyId}::uuid
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    const userRoleNames = userRoles.map(role => role.name);

    // Verificar se o usuário tem pelo menos um dos papéis necessários
    const hasRequiredRole = requiredRoles.some(role => userRoleNames.includes(role));

    if (!hasRequiredRole) {
      throw new ForbiddenException('Acesso negado: você não tem permissão para acessar este recurso');
    }

    return true;
  }
}
