import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID } from 'class-validator';

export class SystemPermissionDto {
  @ApiProperty({
    description: 'ID único da permissão do sistema',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Código único da permissão',
    example: 'accounts_payable.create',
  })
  code: string;

  @ApiProperty({
    description: 'Nome da permissão',
    example: 'Criar contas a pagar',
  })
  name: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Módulo do sistema ao qual a permissão pertence',
    example: 'finance',
  })
  module: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateSystemPermissionDto {
  @ApiProperty({
    description: 'Código único da permissão',
    example: 'accounts_payable.create',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'Nome da permissão',
    example: 'Criar contas a pagar',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Módulo do sistema ao qual a permissão pertence',
    example: 'finance',
  })
  @IsString()
  @IsNotEmpty()
  module: string;
}

export class UpdateSystemPermissionDto {
  @ApiProperty({
    description: 'Nome da permissão',
    example: 'Criar contas a pagar',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Módulo do sistema ao qual a permissão pertence',
    example: 'finance',
    required: false,
  })
  @IsString()
  @IsOptional()
  module?: string;
}

export class SystemPermissionListDto {
  @ApiProperty({
    description: 'Lista de permissões do sistema',
    type: [SystemPermissionDto],
  })
  items: SystemPermissionDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
