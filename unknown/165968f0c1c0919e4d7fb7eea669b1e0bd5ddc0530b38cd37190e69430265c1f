// backend/src/routes/projects/projects.service.ts
import { Injectable, NotFoundException, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateProjectDto, UpdateProjectDto } from './dto';
import { Prisma, Project } from '@prisma/client';
import { AuthenticatedUser } from '../../interfaces/authenticated-user.interface';

@Injectable()
export class ProjectsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Criar um novo projeto
   */
  async create(createProjectDto: CreateProjectDto, user: AuthenticatedUser): Promise<Project> {
    const { companyId } = user;

    try {
      // Converter o DTO para o formato esperado pelo Prisma
      const { status, startDate, endDate, ...rest } = createProjectDto;

      return await this.prisma.project.create({
        data: {
          ...rest,
          status: status as string, // Converter enum para string
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          company: { connect: { id: companyId } },
        },
      });
    } catch (error) {
      console.error('Error creating project:', error);
      throw new InternalServerErrorException('Could not create project');
    }
  }

  /**
   * Buscar todos os projetos com filtros e paginação
   */
  async findAll(
    user: AuthenticatedUser,
    {
      page = 1,
      limit = 10,
      status,
      name,
      startDateFrom,
      startDateTo,
      endDateFrom,
      endDateTo,
      budgetMin,
      budgetMax
    }: {
      page?: number;
      limit?: number;
      status?: string;
      name?: string;
      startDateFrom?: string;
      startDateTo?: string;
      endDateFrom?: string;
      endDateTo?: string;
      budgetMin?: number;
      budgetMax?: number;
    } = {}
  ): Promise<{ data: Project[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
    const skip = (page - 1) * limit;

    // Preparar filtros de data e orçamento
    let startDateFilter: any = {};
    let endDateFilter: any = {};
    let budgetFilter: any = {};

    if (startDateFrom) {
      startDateFilter.gte = new Date(startDateFrom);
    }

    if (startDateTo) {
      startDateFilter.lte = new Date(startDateTo);
    }

    if (endDateFrom) {
      endDateFilter.gte = new Date(endDateFrom);
    }

    if (endDateTo) {
      endDateFilter.lte = new Date(endDateTo);
    }

    if (budgetMin !== undefined) {
      budgetFilter.gte = budgetMin;
    }

    if (budgetMax !== undefined) {
      budgetFilter.lte = budgetMax;
    }

    // Construir filtros de consulta
    const where: Prisma.ProjectWhereInput = {
      companyId,
      deletedAt: null,
      ...(status && { status }),
      ...(name && {
        name: {
          contains: name,
          mode: 'insensitive' // Busca case-insensitive
        }
      }),
      ...(Object.keys(startDateFilter).length > 0 && { startDate: startDateFilter }),
      ...(Object.keys(endDateFilter).length > 0 && { endDate: endDateFilter }),
      ...(Object.keys(budgetFilter).length > 0 && { budget: budgetFilter }),
    };

    try {
      const [data, total] = await Promise.all([
        this.prisma.project.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        this.prisma.project.count({ where }),
      ]);

      return {
        data,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw new InternalServerErrorException('Could not fetch projects');
    }
  }

  /**
   * Buscar um projeto pelo ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<Project> {
    const { companyId } = user;

    try {
      const project = await this.prisma.project.findFirst({
        where: {
          id,
          companyId,
          deletedAt: null,
        },
      });

      if (!project) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      return project;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error fetching project with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not fetch project');
    }
  }

  /**
   * Atualizar um projeto
   */
  async update(id: string, updateProjectDto: UpdateProjectDto, user: AuthenticatedUser): Promise<Project> {
    const { companyId } = user;

    // Verificar se o projeto existe e pertence à empresa do usuário
    await this.findOne(id, user);

    try {
      // Converter o DTO para o formato esperado pelo Prisma
      const { status, startDate, endDate, ...rest } = updateProjectDto;

      return await this.prisma.project.update({
        where: { id },
        data: {
          ...rest,
          ...(status && { status: status as string }),
          ...(startDate && { startDate: new Date(startDate) }),
          ...(endDate && { endDate: new Date(endDate) }),
        },
      });
    } catch (error) {
      console.error(`Error updating project with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update project');
    }
  }

  /**
   * Remover um projeto (soft delete)
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // Verificar se o projeto existe e pertence à empresa do usuário
    await this.findOne(id, user);

    try {
      await this.prisma.project.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error removing project with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not remove project');
    }
  }

  /**
   * Buscar projetos por nome
   */
  async findByName(
    name: string,
    user: AuthenticatedUser,
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: Project[]; total: number; page: number; limit: number }> {
    return this.findAll(user, { page, limit, name });
  }

  /**
   * Buscar projetos por período
   */
  async findByPeriod(
    startDateFrom: string,
    startDateTo: string,
    user: AuthenticatedUser,
    { page = 1, limit = 10 }: { page?: number; limit?: number } = {}
  ): Promise<{ data: Project[]; total: number; page: number; limit: number }> {
    return this.findAll(user, { page, limit, startDateFrom, startDateTo });
  }
}
