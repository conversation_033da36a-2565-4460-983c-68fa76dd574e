import {
  Controller,
  Get,
  Post,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { RbacService } from './rbac.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('rbac')
@Controller('rbac')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RbacController {
  constructor(private readonly rbacService: RbacService) {}

  @Post('setup')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configurar RBAC para uma empresa' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiResponse({
    status: 201,
    description: 'RBAC configurado com sucesso',
  })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @HttpCode(HttpStatus.CREATED)
  async setupRbac(
    @Query('companyId') companyId: string,
  ): Promise<{ message: string }> {
    return this.rbacService.setupRbacForCompany(companyId);
  }

  @Get('check-permission')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Verificar se um usuário tem uma permissão específica' })
  @ApiQuery({ name: 'userId', required: true, description: 'ID do usuário' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiQuery({ name: 'permission', required: true, description: 'Ação da permissão' })
  @ApiResponse({
    status: 200,
    description: 'Resultado da verificação',
  })
  async checkPermission(
    @Query('userId') userId: string,
    @Query('companyId') companyId: string,
    @Query('permission') permission: string,
  ): Promise<{ hasPermission: boolean }> {
    const hasPermission = await this.rbacService.hasPermission(
      userId,
      companyId,
      permission,
    );
    return { hasPermission };
  }

  @Get('user-permissions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter todas as permissões de um usuário em uma empresa' })
  @ApiQuery({ name: 'userId', required: true, description: 'ID do usuário' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiResponse({
    status: 200,
    description: 'Lista de permissões do usuário',
  })
  @ApiResponse({ status: 404, description: 'Usuário ou empresa não encontrado' })
  async getUserPermissions(
    @Query('userId') userId: string,
    @Query('companyId') companyId: string,
  ): Promise<{ permissions: string[] }> {
    const permissions = await this.rbacService.getUserPermissions(
      userId,
      companyId,
    );
    return { permissions };
  }
}
