
import { Button } from "@/components/ui/button";
import { FilterX } from "lucide-react";
import EnhancedPeriodFilter from "@/components/dashboard/EnhancedPeriodFilter";
import AccountFilter from "@/components/dashboard/AccountFilter";
import EntitySearchFilter from "@/components/transactions/EntitySearchFilter";
import AccountsReceivableStatusFilter from "./AccountsReceivableStatusFilter";
import AccountsReceivableSearch from "./AccountsReceivableSearch";

interface AccountsReceivableTableFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filter: string;
  onFilterChange: (filter: string) => void;
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  selectedAccountId: string;
  onAccountChange: (accountId: string) => void;
  selectedEntityId: string;
  onEntityChange: (entityId: string) => void;
  accounts: Array<{ id: string; name: string }>;
  onClearFilters: () => void;
  onClearAllFilters: () => void;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  onDateRangeChange?: (startDate?: Date, endDate?: Date) => void;
}

export default function AccountsReceivableTableFilters({
  searchTerm,
  setSearchTerm,
  filter,
  onFilterChange,
  selectedPeriod,
  onPeriodChange,
  selectedAccountId,
  onAccountChange,
  selectedEntityId,
  onEntityChange,
  accounts,
  onClearFilters,
  onClearAllFilters,
  customDateRange,
  onDateRangeChange,
}: AccountsReceivableTableFiltersProps) {
  return (
    <div className="p-4 border-b">
      <div className="flex flex-wrap items-center gap-2 mb-2">
        <AccountsReceivableStatusFilter 
          filter={filter}
          onFilterChange={onFilterChange}
        />
        
        <div className="flex-1"></div>
        
        <EnhancedPeriodFilter 
          selectedPeriod={selectedPeriod} 
          onPeriodChange={onPeriodChange}
          startDate={customDateRange?.startDate}
          endDate={customDateRange?.endDate}
          onDateRangeChange={onDateRangeChange}
        />
        
        <AccountFilter 
          selectedAccountId={selectedAccountId} 
          accounts={accounts} 
          onAccountChange={onAccountChange} 
        />
        
        <EntitySearchFilter
          selectedEntityId={selectedEntityId}
          onEntityChange={onEntityChange}
          entityType="customer"
        />
        
        <AccountsReceivableSearch
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          onClearFilters={onClearFilters}
        />
        
        <Button 
          variant="outline" 
          size="sm"
          className="h-9 gap-2 bg-white shadow-sm"
          onClick={onClearAllFilters}
        >
          <FilterX className="h-4 w-4" strokeWidth={2.5} />
          <span>Limpar</span>
        </Button>
      </div>
    </div>
  );
}
