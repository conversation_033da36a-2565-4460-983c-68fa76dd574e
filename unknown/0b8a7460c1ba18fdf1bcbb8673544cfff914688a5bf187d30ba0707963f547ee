import api from './axios';
import { 
  CustomPeriod, 
  CreateCustomPeriodRequest, 
  UpdateCustomPeriodRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const customPeriodService = {
  // Listar todos os períodos personalizados com paginação
  getCustomPeriods: async (
    page = 1, 
    limit = 10, 
    search?: string,
    companyId?: string
  ): Promise<PaginatedResponse<CustomPeriod>> => {
    const params = { page, limit, search, companyId };
    const response = await api.get<PaginatedResponse<CustomPeriod>>('/custom-periods', { params });
    return response.data;
  },
  
  // Obter período personalizado por ID
  getCustomPeriodById: async (id: string): Promise<CustomPeriod> => {
    const response = await api.get<ApiResponse<CustomPeriod>>(`/custom-periods/${id}`);
    return response.data.data;
  },
  
  // Criar período personalizado
  createCustomPeriod: async (data: CreateCustomPeriodRequest): Promise<CustomPeriod> => {
    const response = await api.post<ApiResponse<CustomPeriod>>('/custom-periods', data);
    return response.data.data;
  },
  
  // Atualizar período personalizado
  updateCustomPeriod: async (id: string, data: UpdateCustomPeriodRequest): Promise<CustomPeriod> => {
    const response = await api.put<ApiResponse<CustomPeriod>>(`/custom-periods/${id}`, data);
    return response.data.data;
  },
  
  // Excluir período personalizado
  deleteCustomPeriod: async (id: string): Promise<void> => {
    await api.delete(`/custom-periods/${id}`);
  }
};
