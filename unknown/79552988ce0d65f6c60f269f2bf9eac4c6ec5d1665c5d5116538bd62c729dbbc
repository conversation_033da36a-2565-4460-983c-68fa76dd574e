
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Account {
  id: string;
  name: string;
}

interface TransactionAccountSelectorProps {
  accounts: Account[];
  selectedAccount: string;
  onAccountChange: (value: string) => void;
}

export default function TransactionAccountSelector({
  accounts,
  selectedAccount,
  onAccountChange
}: TransactionAccountSelectorProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="bankAccount">Conta bancária</Label>
      <Select value={selectedAccount} onValueChange={onAccountChange}>
        <SelectTrigger id="bankAccount" className="w-full">
          <SelectValue placeholder="Selecione uma conta bancária" />
        </SelectTrigger>
        <SelectContent>
          {accounts.map((account) => (
            <SelectItem key={account.id} value={account.id}>
              {account.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
