import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Building2, Calendar, Check, Lock, Pencil, Plus, Settings as SettingsIcon, Trash, Users, X, MapPin, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CompanyModal } from "@/components/settings/CompanyModal";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import IconButton from "@/components/ui-custom/IconButton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useCompanies, useCreateCompany, useUpdateCompany, useDeleteCompany } from "@/hooks/api/useCompanies";
import { Company, CreateCompanyRequest, UpdateCompanyRequest } from "@/types/api";
import { toast } from "sonner";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

const CompanySettings = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState<Company | undefined>(undefined);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState<Company | null>(null);
  
  // Fetch companies using React Query
  const { data: companiesData, isLoading, error } = useCompanies();
  
  // Mutations for CRUD operations
  const createCompanyMutation = useCreateCompany();
  const updateCompanyMutation = useUpdateCompany();
  const deleteCompanyMutation = useDeleteCompany();
  
  const handleAddCompany = () => {
    setCurrentCompany(undefined);
    setIsModalOpen(true);
  };

  const handleEditCompany = (company: Company) => {
    setCurrentCompany(company);
    setIsModalOpen(true);
  };

  const handleManagePeriods = (companyId: string) => {
    navigate(`/settings/companies/${companyId}/periods`);
  };

  const handleManageAddresses = () => {
    navigate('/addresses');
  };
  
  const handleDeleteCompany = (company: Company) => {
    setCompanyToDelete(company);
    setIsDeleteDialogOpen(true);
  };
  
  const confirmDeleteCompany = () => {
    if (companyToDelete) {
      deleteCompanyMutation.mutate(companyToDelete.id, {
        onSuccess: () => {
          toast.success(`Empresa ${companyToDelete.name} excluída com sucesso`);
          setIsDeleteDialogOpen(false);
          setCompanyToDelete(null);
        },
        onError: (error: any) => {
          toast.error(`Erro ao excluir empresa: ${error.message || 'Tente novamente mais tarde'}`);
        }
      });
    }
  };

  const handleSaveCompany = (companyData: CreateCompanyRequest | UpdateCompanyRequest) => {
    if (currentCompany) {
      // Update existing company
      updateCompanyMutation.mutate({
        id: currentCompany.id,
        data: companyData as UpdateCompanyRequest
      }, {
        onSuccess: () => {
          toast.success(`Empresa ${companyData.name} atualizada com sucesso`);
          setIsModalOpen(false);
          setCurrentCompany(undefined);
        },
        onError: (error: any) => {
          toast.error(`Erro ao atualizar empresa: ${error.message || 'Tente novamente mais tarde'}`);
        }
      });
    } else {
      // Create new company
      createCompanyMutation.mutate(companyData as CreateCompanyRequest, {
        onSuccess: () => {
          toast.success(`Empresa ${companyData.name} criada com sucesso`);
          setIsModalOpen(false);
        },
        onError: (error: any) => {
          toast.error(`Erro ao criar empresa: ${error.message || 'Tente novamente mais tarde'}`);
        }
      });
    }
  };

  return (
    <>
      <Layout>
        <div className="container mx-auto py-6">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <SettingsIcon className="h-8 w-8 text-primary mr-3" />
              <h1 className="text-3xl font-bold">Configurações</h1>
            </div>
            <Button onClick={handleAddCompany} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Nova Empresa
            </Button>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center gap-4">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <p className="text-lg">Carregando empresas...</p>
              </div>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
              <div className="flex flex-col items-center gap-4">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <h2 className="text-xl font-semibold text-red-700">Erro ao carregar empresas</h2>
                <p className="text-red-600">
                  Não foi possível carregar a lista de empresas. Por favor, tente novamente mais tarde.
                </p>
                <Button 
                  className="mt-4" 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                >
                  Tentar novamente
                </Button>
              </div>
            </div>
          ) : companiesData?.items.length === 0 ? (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div className="flex flex-col items-center gap-4">
                <Building2 className="h-12 w-12 text-blue-500" />
                <h2 className="text-xl font-semibold text-blue-700">Nenhuma empresa cadastrada</h2>
                <p className="text-blue-600">
                  Você ainda não possui empresas cadastradas. Clique no botão "Nova Empresa" para começar.
                </p>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-dark-card rounded-lg border shadow dark:border-dark-card-light">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Empresa</TableHead>
                    <TableHead>CNPJ</TableHead>
                    <TableHead>Contato</TableHead>
                    <TableHead>E-mail</TableHead>
                    <TableHead>Endereço</TableHead>
                    <TableHead>Calendário</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {companiesData?.items?.map((company) => (
                    <TableRow key={company.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-9 w-9 bg-primary/10 text-primary">
                            <AvatarImage src={company.logo || ''} alt={company.name} />
                            <AvatarFallback>
                              {company.name ? company.name.charAt(0).toUpperCase() : 'C'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{company.name}</div>
                            {company.active && <div className="text-xs rounded bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 px-2 py-0.5 w-fit">Atual</div>}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{company.cnpj || 'N/A'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{company.phone || 'N/A'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{company.email || 'N/A'}</div>
                      </TableCell>
                      <TableCell>
                        {company.address ? (
                          <div className="text-sm">
                            <div className="font-medium">{company.address.street}, {company.address.number || 'S/N'}</div>
                            <div className="text-gray-500 dark:text-gray-400">{company.address.city}/{company.address.state}</div>
                          </div>
                        ) : company.addressId ? (
                          <div className="text-sm">
                            <div className="font-medium text-blue-600">Endereço vinculado</div>
                            <div className="text-gray-500 dark:text-gray-400">Clique em editar para ver detalhes</div>
                          </div>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400 text-sm italic">Não cadastrado</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={company.calendarType === 'padrão' ? 'default' : 'outline'}>
                          {company.calendarType === 'padrão' ? 'Padrão' : 'Períodos'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={company.active ? 'success' : 'destructive'}>
                          {company.active ? 'Ativo' : 'Inativo'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <IconButton 
                            icon={<Pencil className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Editar"
                            onClick={() => handleEditCompany(company)}
                          />
                          <IconButton 
                            icon={<Calendar className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Períodos"
                            onClick={() => handleManagePeriods(company.id)}
                          />
                          <IconButton 
                            icon={<MapPin className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Endereços"
                            onClick={handleManageAddresses}
                          />
                          <IconButton 
                            icon={<Trash className="h-4 w-4" />}
                            variant="ghost"
                            size="sm"
                            label="Excluir"
                            onClick={() => handleDeleteCompany(company)}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </Layout>

      <CompanyModal 
        open={isModalOpen} 
        onOpenChange={setIsModalOpen}
        onSave={handleSaveCompany}
        company={currentCompany}
        isLoading={createCompanyMutation.isPending || updateCompanyMutation.isPending}
      />
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a empresa "{companyToDelete?.name}"? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteCompanyMutation.isPending}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteCompany}
              disabled={deleteCompanyMutation.isPending}
              className="bg-red-500 hover:bg-red-600"
            >
              {deleteCompanyMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Sim, excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CompanySettings;
