
import { useState, useMemo, useEffect } from "react";
import TransactionsTable from "./TransactionsTable";
import TransactionFilters from "./TransactionFilters";
import { Transaction } from "@/types/transaction";

interface FilteredTransactionsTableProps {
  onEdit: (transaction: Transaction) => void;
  initialPeriod?: string;
  initialAccountId?: string;
  initialCustomDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  transactionType?: string;
}

export default function FilteredTransactionsTable({ 
  onEdit, 
  initialPeriod,
  initialAccountId,
  initialCustomDateRange,
  transactionType
}: FilteredTransactionsTableProps) {
  // State for filters
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState(initialPeriod || "current-month");
  const [selectedAccountId, setSelectedAccountId] = useState(initialAccountId || "all");
  const [selectedEntityId, setSelectedEntityId] = useState("all");
  const [customDateRange, setCustomDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>(initialCustomDateRange || {});

  // Update filters when props change
  useEffect(() => {
    if (initialPeriod) setSelectedPeriod(initialPeriod);
    if (initialAccountId) setSelectedAccountId(initialAccountId);
    if (initialCustomDateRange) setCustomDateRange(initialCustomDateRange);
  }, [initialPeriod, initialAccountId, initialCustomDateRange]);

  // Helper functions for filters
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    if (period !== "custom") {
      setCustomDateRange({});
    }
  };

  const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
    setSelectedPeriod("custom");
    setCustomDateRange({ startDate, endDate });
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedPeriod(initialPeriod || "current-month");
    setSelectedAccountId(initialAccountId || "all");
    setSelectedEntityId("all");
    setCustomDateRange(initialCustomDateRange || {});
  };

  return (
    <div>
      <TransactionFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedPeriod={selectedPeriod}
        onPeriodChange={handlePeriodChange}
        selectedAccountId={selectedAccountId}
        onAccountChange={setSelectedAccountId}
        selectedEntityId={selectedEntityId}
        onEntityChange={setSelectedEntityId}
        onClearFilters={handleClearFilters}
        customDateRange={customDateRange}
        onDateRangeChange={handleDateRangeChange}
      />
      <TransactionsTable 
        onEdit={onEdit}
        searchTerm={searchTerm}
        selectedPeriod={selectedPeriod}
        selectedAccountId={selectedAccountId}
        selectedEntityId={selectedEntityId}
        customDateRange={customDateRange}
        transactionType={transactionType}
      />
    </div>
  );
}
