-- Verificar se a tabela de papéis já existe e criar se não existir
DO $$
BEGIN
    -- Criar a tabela de papéis se não existir
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'roles') THEN
        CREATE TABLE roles (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            company_id UUID NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            deleted_at TIMESTAMP WITH TIME ZONE
        );
        
        -- Adicionar chave estrangeira apenas se não existir
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fk_company') THEN
            ALTER TABLE roles ADD CONSTRAINT fk_company 
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
        END IF;
    END IF;
    
    -- <PERSON><PERSON>r índice para company_id se não existir
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_roles_company_id') THEN
        CREATE INDEX idx_roles_company_id ON roles(company_id);
    END IF;
    
    -- Adicionar restrição de unicidade para nome do papel dentro da mesma empresa
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_unique_role_name_per_company') THEN
        CREATE UNIQUE INDEX idx_unique_role_name_per_company ON roles(company_id, name);
    END IF;

    -- Criar a tabela de associações se não existir
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles') THEN
        CREATE TABLE user_company_roles (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL,
            company_id UUID NOT NULL,
            role_id UUID NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            deleted_at TIMESTAMP WITH TIME ZONE
        );
    END IF;
    
    -- Adicionar chaves estrangeiras apenas se não existirem
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_company_roles_user_id_fkey') AND 
       EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles') THEN
        ALTER TABLE user_company_roles ADD CONSTRAINT user_company_roles_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_company_roles_company_id_fkey') AND 
       EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles') THEN
        ALTER TABLE user_company_roles ADD CONSTRAINT user_company_roles_company_id_fkey 
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_company_roles_role_id_fkey') AND 
       EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_company_roles') THEN
        ALTER TABLE user_company_roles ADD CONSTRAINT user_company_roles_role_id_fkey 
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE;
    END IF;
    
    -- Criar índices para melhorar a performance das consultas
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_company_roles_user_id') THEN
        CREATE INDEX idx_user_company_roles_user_id ON user_company_roles(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_company_roles_company_id') THEN
        CREATE INDEX idx_user_company_roles_company_id ON user_company_roles(company_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_company_roles_role_id') THEN
        CREATE INDEX idx_user_company_roles_role_id ON user_company_roles(role_id);
    END IF;
    
    -- Adicionar restrição de unicidade para associações
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_unique_user_company_role') THEN
        CREATE UNIQUE INDEX idx_unique_user_company_role ON user_company_roles(user_id, company_id, role_id);
    END IF;
END
$$;
