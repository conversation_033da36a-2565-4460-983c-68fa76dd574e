// backend/src/routes/accounts-receivable/dto/create-account-receivable.dto.ts
import {
  IsString,
  IsUUID,
  IsDateString,
  IsNumber,
  IsPositive,
  IsOptional,
  MaxLength,
  Min,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAccountReceivableDto {
  @ApiProperty({ description: 'Descrição da conta a receber', maxLength: 255 })
  @IsString()
  @MaxLength(255)
  description: string;

  @ApiProperty({ description: 'ID da entidade (cliente)', example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  entityId: string;

  @ApiProperty({ description: 'Data de vencimento', example: '2025-12-31' })
  @IsDateString()
  dueDate: string; // Use string for input, transform/validate as Date in service if needed

  @ApiProperty({ description: 'Valor da conta a receber', example: 1500.50, minimum: 0.01 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Type(() => Number) // Ensure transformation from string if needed
  amount: number;

  @ApiProperty({ description: 'ID da moeda', example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  currencyId: string;

  @ApiPropertyOptional({ description: 'ID da categoria', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({ description: 'ID do projeto', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  projectId?: string;

  @ApiPropertyOptional({ description: 'ID do método de pagamento', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string;

  @ApiPropertyOptional({ description: 'ID da conta bancária', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  bankAccountId?: string;

  @ApiPropertyOptional({ description: 'ID do tipo de recorrência', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  recurrenceTypeId?: string;

  @ApiPropertyOptional({ description: 'Número da fatura/nota fiscal', maxLength: 100, example: 'NF-12345' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  invoiceNumber?: string;

  @ApiPropertyOptional({ description: 'Observações adicionais' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({ description: 'Valor de juros', minimum: 0, example: 10.50 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  interestAmount?: number;

  @ApiPropertyOptional({ description: 'Valor de desconto', minimum: 0, example: 5.25 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  discountAmount?: number;

  @ApiPropertyOptional({ description: 'Número total de parcelas', minimum: 1, example: 3 })
  @IsOptional()
  @IsInt()
  @Min(1)
  installments?: number;

  @ApiPropertyOptional({ description: 'Número da parcela atual', minimum: 1, example: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  installmentNumber?: number;

  @ApiPropertyOptional({ description: 'ID da conta a receber pai (para parcelas)', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  // companyId is added in the service based on the authenticated user
  // status is managed internally
  // receivedAmount is managed internally (likely via transactions)
}
