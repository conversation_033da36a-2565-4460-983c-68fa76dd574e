import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID } from 'class-validator';

export class PermissionDto {
  @ApiProperty({
    description: 'ID único da permissão',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({
    description: 'Ação da permissão',
    example: 'accounts_payable.create',
  })
  action: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'ID da permissão do sistema relacionada',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  systemPermissionId?: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreatePermissionDto {
  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({
    description: 'Ação da permissão',
    example: 'accounts_payable.create',
  })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID da permissão do sistema relacionada',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  systemPermissionId?: string;
}

export class UpdatePermissionDto {
  @ApiProperty({
    description: 'Ação da permissão',
    example: 'accounts_payable.create',
    required: false,
  })
  @IsString()
  @IsOptional()
  action?: string;

  @ApiProperty({
    description: 'Descrição da permissão',
    example: 'Permite criar novas contas a pagar',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID da permissão do sistema relacionada',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  systemPermissionId?: string;
}

export class PermissionListDto {
  @ApiProperty({
    description: 'Lista de permissões',
    type: [PermissionDto],
  })
  items: PermissionDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
