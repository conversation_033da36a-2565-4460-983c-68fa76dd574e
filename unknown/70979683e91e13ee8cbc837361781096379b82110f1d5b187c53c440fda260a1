import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { roleService } from '@/services/api';
import { CreateRoleRequest, UpdateRoleRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar papéis com paginação
export const useRoles = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['roles', { page, limit, search }],
    queryFn: () => roleService.getRoles(page, limit, search),
    keepPreviousData: true,
    staleTime: 10 * 60 * 1000, // 10 minutos (dados não mudam com frequência)
  });
};

// Hook para buscar um papel específico por ID
export const useRole = (id: string) => {
  return useQuery({
    queryKey: ['roles', id],
    queryFn: () => roleService.getRoleById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para criar um novo papel
export const useCreateRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateRoleRequest) => roleService.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast.success('Papel criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar papel. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um papel existente
export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateRoleRequest }) => 
      roleService.updateRole(id, data),
    onSuccess: (updatedRole) => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.setQueryData(['roles', updatedRole.id], updatedRole);
      toast.success('Papel atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar papel. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um papel
export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => roleService.deleteRole(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.removeQueries({ queryKey: ['roles', id] });
      toast.success('Papel excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir papel. Por favor, tente novamente.'
      );
    }
  });
};
