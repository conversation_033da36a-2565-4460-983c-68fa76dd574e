# Plano de Implementação - Sistema de Gestão Financeira Multi-Tenant

Este documento detalha o plano completo para o desenvolvimento de um sistema financeiro multi-tenant, utilizando **NestJS** no backend e **Next.js** no frontend. O plano está dividido em fases lógicas, com cada etapa numerada sequencialmente de **Step 1** a **Step 81**, incluindo instruções claras, arquivos associados e dependências. O objetivo é fornecer um guia passo a passo para garantir que o sistema seja construído de forma segura, escalável e eficiente.

---

## Fase 1: Configuração Inicial e Segurança

### 1.1 Configuração do Backend

- [ ] **Step 1: Inicializar Projeto NestJS**  
  - **Descrição**: Criar um novo projeto NestJS com TypeScript, configurar a estrutura básica de pastas e inicializar o controle de versão com Git.  
  - **Arquivos**: `backend/`, `backend/src/`, `backend/package.json`, `.gitignore`  
  - **Instruções**: Execute `nest new backend` no terminal e inicialize o Git com `git init`. Adicione dependências iniciais como `@nestjs/core`.

- [ ] **Step 2: Configurar PostgreSQL e Prisma**  
  - **Descrição**: Configurar o banco de dados PostgreSQL usando Docker e integrar o Prisma ORM com um schema inicial para o modelo `Tenant`.  
  - **Arquivos**: `docker-compose.yml`, `backend/prisma/schema.prisma`, `backend/.env`  
  - **Instruções**: Instale o Prisma com `npm install prisma @prisma/client`, inicialize com `npx prisma init` e configure a conexão no `.env`. Exemplo de schema:  
    ```prisma
    model Tenant {
      id        Int      @id @default(autoincrement())
      name      String
      createdAt DateTime @default(now())
      updatedAt DateTime @updatedAt
    }
    ```

- [ ] **Step 3: Configurar Docker para Desenvolvimento**  
  - **Descrição**: Criar Dockerfiles para backend e frontend, e configurar um `docker-compose.yml` para o ambiente de desenvolvimento com hot-reload.  
  - **Arquivos**: `backend/Dockerfile`, `frontend/Dockerfile`, `docker-compose.yml`  
  - **Instruções**: Inclua volumes para suportar hot-reload e defina serviços para o backend, frontend e PostgreSQL.

- [ ] **Step 4: Implementar Logs e Auditoria no Backend**  
  - **Descrição**: Configurar logs e auditoria para registrar ações de usuários, eventos do sistema e erros.  
  - **Arquivos**: `backend/src/common/services/logger.service.ts`, `backend/src/common/middlewares/audit.middleware.ts`  
  - **Instruções**: Utilize uma biblioteca como Winston ou Pino para logs e crie um middleware para registrar requisições.

- [ ] **Step 5: Configurar Segurança Básica no Backend**  
  - **Descrição**: Adicionar medidas de segurança como rate limiting, CORS e Helmet para proteger o backend.  
  - **Arquivos**: `backend/src/main.ts`, `backend/src/app.module.ts`  
  - **Instruções**: Instale `@nestjs/throttler`, configure Helmet para headers de segurança e defina CORS para origens permitidas.

- [ ] **Step 6: Implementar Middleware de Tratamento de Erros**  
  - **Descrição**: Criar um middleware global para capturar e tratar erros de forma padronizada.  
  - **Arquivos**: `backend/src/common/middlewares/error.middleware.ts`  
  - **Instruções**: Implemente um middleware que retorne respostas JSON consistentes para erros (ex.: 400, 500).

- [ ] **Step 7: Implementar Endpoint de Health Check**  
  - **Descrição**: Criar um endpoint para verificar a saúde da aplicação e a conexão com o banco de dados.  
  - **Arquivos**: `backend/src/health/health.module.ts`, `backend/src/health/health.controller.ts`, `backend/src/health/health.service.ts`  
  - **Instruções**: Use o módulo `@nestjs/terminus` para verificar a conexão com o Prisma.

### 1.2 Configuração do Frontend

- [ ] **Step 8: Inicializar Projeto Next.js**  
  - **Descrição**: Criar um projeto Next.js com TypeScript e configurar Tailwind CSS e shadcn/ui para estilização.  
  - **Arquivos**: `frontend/`, `frontend/package.json`, `frontend/tailwind.config.js`  
  - **Instruções**: Execute `npx create-next-app@latest frontend --typescript` e siga as instruções para configurar Tailwind e shadcn/ui.

---

## Fase 2: Gestão de Tenants e Usuários

### 2.1 Backend: Tenants

- [ ] **Step 9: Implementar Entidade e Módulo Tenant**  
  - **Descrição**: Definir a entidade `Tenant` e criar o módulo correspondente no NestJS.  
  - **Arquivos**: `backend/src/tenants/entities/tenant.entity.ts`, `backend/src/tenants/tenants.module.ts`, `backend/src/tenants/dto/create-tenant.dto.ts`, `backend/src/tenants/dto/update-tenant.dto.ts`  
  - **Dependências**: Step 2  
  - **Instruções**: Use o schema do Prisma como base para a entidade.

- [ ] **Step 10: Implementar Serviço de Tenants**  
  - **Descrição**: Criar um serviço para operações CRUD de tenants usando o Prisma.  
  - **Arquivos**: `backend/src/tenants/tenants.service.ts`  
  - **Dependências**: Step 9  
  - **Instruções**: Implemente métodos como `create`, `findAll`, `findOne`, `update` e `remove`.

- [ ] **Step 11: Implementar Controller de Tenants**  
  - **Descrição**: Criar endpoints REST para gerenciamento de tenants.  
  - **Arquivos**: `backend/src/tenants/tenants.controller.ts`  
  - **Dependências**: Step 10  
  - **Instruções**: Defina rotas como `POST /tenants`, `GET /tenants`, `GET /tenants/:id`, etc.

### 2.2 Frontend: Gerenciamento de Tenants

- [ ] **Step 12: Implementar Páginas de Gerenciamento de Tenants**  
  - **Descrição**: Criar páginas para listar, criar, editar e excluir tenants no frontend.  
  - **Arquivos**: `frontend/pages/tenants/index.tsx`, `frontend/pages/tenants/create.tsx`, `frontend/pages/tenants/[id].tsx`  
  - **Dependências**: Step 8, Step 11  
  - **Instruções**: Use componentes reutilizáveis e conecte-se aos endpoints do backend via fetch/axios.

### 2.3 Backend: Usuários e Autenticação

- [ ] **Step 13: Implementar Entidade e Módulo User**  
  - **Descrição**: Definir a entidade `User` e criar o módulo associado.  
  - **Arquivos**: `backend/src/users/entities/user.entity.ts`, `backend/src/users/users.module.ts`, `backend/src/users/dto/create-user.dto.ts`, `backend/src/users/dto/update-user.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Adicione campos como `email`, `password` e `tenantId` ao schema do Prisma.

- [ ] **Step 14: Implementar Serviço de Usuários para Registro**  
  - **Descrição**: Criar um serviço para registro de usuários com hash de senha.  
  - **Arquivos**: `backend/src/users/users.service.ts`  
  - **Dependências**: Step 13  
  - **Instruções**: Use `bcrypt` para criptografar senhas antes de salvar no banco.

- [ ] **Step 15: Implementar Módulo de Autenticação**  
  - **Descrição**: Configurar autenticação baseada em JWT.  
  - **Arquivos**: `backend/src/auth/auth.module.ts`, `backend/src/auth/auth.service.ts`, `backend/src/auth/dto/login.dto.ts`, `backend/src/auth/strategies/jwt.strategy.ts`  
  - **Dependências**: Step 14  
  - **Instruções**: Instale `@nestjs/jwt` e configure a chave secreta no `.env`. Defina a expiração do token para 1 hora.

- [ ] **Step 16: Implementar Controller de Autenticação**  
  - **Descrição**: Criar endpoints para registro e login de usuários.  
  - **Arquivos**: `backend/src/auth/auth.controller.ts`  
  - **Dependências**: Step 15  
  - **Instruções**: Defina `POST /auth/register` e `POST /auth/login`, retornando um token JWT.

### 2.4 Frontend: Autenticação e Gerenciamento de Usuários

- [ ] **Step 17: Implementar Páginas de Autenticação**  
  - **Descrição**: Criar páginas de login e registro no frontend.  
  - **Arquivos**: `frontend/pages/login.tsx`, `frontend/pages/register.tsx`, `frontend/components/auth/LoginForm.tsx`, `frontend/components/auth/RegisterForm.tsx`  
  - **Dependências**: Step 8, Step 16  
  - **Instruções**: Conecte os formulários aos endpoints de autenticação e armazene o token JWT no localStorage.

- [ ] **Step 18: Configurar Rotas Protegidas no Frontend**  
  - **Descrição**: Implementar proteção de rotas para páginas que requerem autenticação.  
  - **Arquivos**: `frontend/components/ProtectedRoute.tsx`, `frontend/pages/*.tsx`  
  - **Dependências**: Step 17  
  - **Instruções**: Crie um componente `ProtectedRoute` que verifique o token JWT antes de renderizar a página.

- [ ] **Step 19: Implementar Páginas de Gerenciamento de Usuários**  
  - **Descrição**: Criar páginas para listar, criar, editar e excluir usuários.  
  - **Arquivos**: `frontend/pages/users/index.tsx`, `frontend/pages/users/create.tsx`, `frontend/pages/users/[id].tsx`  
  - **Dependências**: Step 18, Step 14  
  - **Instruções**: Use um template CRUD reutilizável conectado ao backend.

---

## Fase 3: Endereços e CEPs

### 3.1 Backend: Endereços

- [ ] **Step 20: Implementar Entidade e Módulo Address**  
  - **Descrição**: Definir a entidade `Address` e criar o módulo correspondente.  
  - **Arquivos**: `backend/src/addresses/entities/address.entity.ts`, `backend/src/addresses/addresses.module.ts`, `backend/src/addresses/dto/create-address.dto.ts`, `backend/src/addresses/dto/update-address.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Adicione campos como `street`, `city`, `zipCode` e `tenantId`.

- [ ] **Step 21: Implementar CEP Service e Cache**  
  - **Descrição**: Criar um serviço para consulta de CEPs com cache para otimizar chamadas externas.  
  - **Arquivos**: `backend/src/addresses/cep.service.ts`  
  - **Dependências**: Step 20  
  - **Instruções**: Use Axios para consultar o ViaCEP e Redis ou cache em memória para armazenar resultados.

- [ ] **Step 22: Implementar Serviço de Endereços**  
  - **Descrição**: Criar um serviço para operações CRUD de endereços integrado ao serviço de CEP.  
  - **Arquivos**: `backend/src/addresses/addresses.service.ts`  
  - **Dependências**: Step 21  
  - **Instruções**: Implemente métodos CRUD com validação de CEPs.

- [ ] **Step 23: Implementar Controller de Endereços**  
  - **Descrição**: Criar endpoints REST para gerenciamento de endereços e consulta de CEP.  
  - **Arquivos**: `backend/src/addresses/addresses.controller.ts`  
  - **Dependências**: Step 22  
  - **Instruções**: Defina rotas como `POST /addresses`, `GET /addresses/cep/:cep`.

### 3.2 Frontend: Gerenciamento de Endereços

- [ ] **Step 24: Implementar Páginas de Gerenciamento de Endereços**  
  - **Descrição**: Criar páginas para listar, criar e editar endereços, com suporte a consulta de CEP.  
  - **Arquivos**: `frontend/pages/addresses/index.tsx`, `frontend/pages/addresses/create.tsx`, `frontend/pages/addresses/[id].tsx`, `frontend/components/addresses/AddressForm.tsx`  
  - **Dependências**: Step 8, Step 23  
  - **Instruções**: Adicione um campo de busca de CEP que preenche automaticamente o formulário.

---

## Fase 4: Entidades (Clientes/Fornecedores)

### 4.1 Backend: Entidades

- [ ] **Step 25: Implementar Entidade e Módulo Entity**  
  - **Descrição**: Definir a entidade `Entity` para representar clientes e fornecedores.  
  - **Arquivos**: `backend/src/entities/entities/entity.entity.ts`, `backend/src/entities/entities.module.ts`, `backend/src/entities/dto/create-entity.dto.ts`, `backend/src/entities/dto/update-entity.dto.ts`  
  - **Dependências**: Step 2, Step 20  
  - **Instruções**: Inclua campos como `name`, `type` (client/supplier) e `tenantId`.

- [ ] **Step 26: Implementar Serviço de Entidades**  
  - **Descrição**: Criar um serviço para operações CRUD de entidades.  
  - **Arquivos**: `backend/src/entities/entities.service.ts`  
  - **Dependências**: Step 25  
  - **Instruções**: Adicione validações específicas por tipo de entidade.

- [ ] **Step 27: Implementar Controller de Entidades**  
  - **Descrição**: Criar endpoints REST para gerenciamento de entidades.  
  - **Arquivos**: `backend/src/entities/entities.controller.ts`  
  - **Dependências**: Step 26  
  - **Instruções**: Defina rotas com filtros por tipo (`GET /entities?type=client`).

### 4.2 Frontend: Gerenciamento de Entidades

- [ ] **Step 28: Implementar Páginas de Gerenciamento de Entidades**  
  - **Descrição**: Criar páginas para listar, criar e editar entidades no frontend.  
  - **Arquivos**: `frontend/pages/entities/index.tsx`, `frontend/pages/entities/create.tsx`, `frontend/pages/entities/[id].tsx`  
  - **Dependências**: Step 8, Step 27  
  - **Instruções**: Use um template CRUD com filtros por tipo.

---

## Fase 5: Categorias

### 5.1 Backend: Categorias

- [ ] **Step 29: Implementar Entidade e Módulo Category**  
  - **Descrição**: Definir a entidade `Category` para categorização financeira.  
  - **Arquivos**: `backend/src/categories/entities/category.entity.ts`, `backend/src/categories/categories.module.ts`, `backend/src/categories/dto/create-category.dto.ts`, `backend/src/categories/dto/update-category.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Suporte a hierarquia com campo `parentId`.

- [ ] **Step 30: Implementar Serviço de Categorias**  
  - **Descrição**: Criar um serviço para operações CRUD de categorias, incluindo hierarquia.  
  - **Arquivos**: `backend/src/categories/categories.service.ts`  
  - **Dependências**: Step 29  
  - **Instruções**: Implemente métodos para gerenciar a estrutura hierárquica.

- [ ] **Step 31: Implementar Controller de Categorias**  
  - **Descrição**: Criar endpoints REST para gerenciamento de categorias.  
  - **Arquivos**: `backend/src/categories/categories.controller.ts`  
  - **Dependências**: Step 30  
  - **Instruções**: Defina rotas para CRUD e consulta de hierarquia.

### 5.2 Frontend: Gerenciamento de Categorias

- [ ] **Step 32: Implementar Páginas de Gerenciamento de Categorias**  
  - **Descrição**: Criar páginas para listar, criar e editar categorias com visualização hierárquica.  
  - **Arquivos**: `frontend/pages/categories/index.tsx`, `frontend/pages/categories/create.tsx`, `frontend/pages/categories/[id].tsx`, `frontend/components/categories/CategoryTree.tsx`  
  - **Dependências**: Step 8, Step 31  
  - **Instruções**: Use um componente de árvore para exibir a hierarquia.

---

## Fase 6: Projetos

### 6.1 Backend: Projetos

- [ ] **Step 33: Implementar Entidade e Módulo Project**  
  - **Descrição**: Definir a entidade `Project` para gerenciamento de projetos financeiros.  
  - **Arquivos**: `backend/src/projects/entities/project.entity.ts`, `backend/src/projects/projects.module.ts`, `backend/src/projects/dto/create-project.dto.ts`, `backend/src/projects/dto/update-project.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Inclua campos como `name`, `description` e `tenantId`.

- [ ] **Step 34: Implementar Serviço de Projetos**  
  - **Descrição**: Criar um serviço para operações CRUD de projetos.  
  - **Arquivos**: `backend/src/projects/projects.service.ts`  
  - **Dependências**: Step 33  
  - **Instruções**: Implemente métodos CRUD básicos.

- [ ] **Step 35: Implementar Controller de Projetos**  
  - **Descrição**: Criar endpoints REST para gerenciamento de projetos.  
  - **Arquivos**: `backend/src/projects/projects.controller.ts`  
  - **Dependências**: Step 34  
  - **Instruções**: Defina rotas CRUD padrão.

### 6.2 Frontend: Gerenciamento de Projetos

- [ ] **Step 36: Implementar Páginas de Gerenciamento de Projetos**  
  - **Descrição**: Criar páginas para listar, criar e editar projetos.  
  - **Arquivos**: `frontend/pages/projects/index.tsx`, `frontend/pages/projects/create.tsx`, `frontend/pages/projects/[id].tsx`  
  - **Dependências**: Step 8, Step 35  
  - **Instruções**: Use um template CRUD reutilizável.

---

## Fase 7: Bancos e Contas Bancárias

### 7.1 Backend: Bancos

- [ ] **Step 37: Implementar Entidade e Módulo Bank**  
  - **Descrição**: Definir a entidade `Bank` para instituições bancárias.  
  - **Arquivos**: `backend/src/banks/entities/bank.entity.ts`, `backend/src/banks/banks.module.ts`, `backend/src/banks/dto/create-bank.dto.ts`, `backend/src/banks/dto/update-bank.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Inclua campos como `name` e `code`.

- [ ] **Step 38: Implementar Serviço de Bancos**  
  - **Descrição**: Criar um serviço para operações CRUD de bancos.  
  - **Arquivos**: `backend/src/banks/banks.service.ts`  
  - **Dependências**: Step 37  
  - **Instruções**: Implemente métodos CRUD.

- [ ] **Step 39: Implementar Controller de Bancos**  
  - **Descrição**: Criar endpoints REST para gerenciamento de bancos.  
  - **Arquivos**: `backend/src/banks/banks.controller.ts`  
  - **Dependências**: Step 38  
  - **Instruções**: Defina rotas CRUD.

### 7.2 Backend: Contas Bancárias

- [ ] **Step 40: Implementar Entidade e Módulo BankAccount**  
  - **Descrição**: Definir a entidade `BankAccount` para contas bancárias.  
  - **Arquivos**: `backend/src/bank-accounts/entities/bank-account.entity.ts`, `backend/src/bank-accounts/bank-accounts.module.ts`, `backend/src/bank-accounts/dto/create-bank-account.dto.ts`, `backend/src/bank-accounts/dto/update-bank-account.dto.ts`  
  - **Dependências**: Step 2, Step 37  
  - **Instruções**: Inclua campos como `accountNumber`, `bankId` e `tenantId`.

- [ ] **Step 41: Implementar Serviço de Contas Bancárias**  
  - **Descrição**: Criar um serviço para operações CRUD de contas bancárias.  
  - **Arquivos**: `backend/src/bank-accounts/bank-accounts.service.ts`  
  - **Dependências**: Step 40  
  - **Instruções**: Implemente métodos CRUD com validações.

- [ ] **Step 42: Implementar Controller de Contas Bancárias**  
  - **Descrição**: Criar endpoints REST para gerenciamento de contas bancárias.  
  - **Arquivos**: `backend/src/bank-accounts/bank-accounts.controller.ts`  
  - **Dependências**: Step 41  
  - **Instruções**: Defina rotas CRUD.

### 7.3 Frontend: Gerenciamento de Bancos e Contas Bancárias

- [ ] **Step 43: Implementar Páginas de Gerenciamento de Bancos**  
  - **Descrição**: Criar páginas para listar, criar e editar bancos.  
  - **Arquivos**: `frontend/pages/banks/index.tsx`, `frontend/pages/banks/create.tsx`, `frontend/pages/banks/[id].tsx`  
  - **Dependências**: Step 8, Step 39  
  - **Instruções**: Use template CRUD.

- [ ] **Step 44: Implementar Páginas de Gerenciamento de Contas Bancárias**  
  - **Descrição**: Criar páginas para listar, criar e editar contas bancárias.  
  - **Arquivos**: `frontend/pages/bank-accounts/index.tsx`, `frontend/pages/bank-accounts/create.tsx`, `frontend/pages/bank-accounts/[id].tsx`  
  - **Dependências**: Step 8, Step 42, Step 43  
  - **Instruções**: Inclua dropdown para seleção de bancos.

---

## Fase 8: Contas a Pagar e Receber

### 8.1 Backend: Contas

- [ ] **Step 45: Implementar Entidade e Módulo Bill**  
  - **Descrição**: Definir a entidade `Bill` para contas a pagar e receber.  
  - **Arquivos**: `backend/src/bills/entities/bill.entity.ts`, `backend/src/bills/bills.module.ts`, `backend/src/bills/dto/create-bill.dto.ts`, `backend/src/bills/dto/update-bill.dto.ts`  
  - **Dependências**: Step 2, Step 25, Step 29, Step 33, Step 40  
  - **Instruções**: Inclua campos como `amount`, `dueDate`, `entityId`, `categoryId`, `projectId`, `bankAccountId`.

- [ ] **Step 46: Implementar Serviço de Contas**  
  - **Descrição**: Criar um serviço para operações CRUD de contas, incluindo lógica de parcelamento.  
  - **Arquivos**: `backend/src/bills/bills.service.ts`  
  - **Dependências**: Step 45  
  - **Instruções**: Implemente métodos para criar contas recorrentes ou parceladas.

- [ ] **Step 47: Implementar Controller de Contas**  
  - **Descrição**: Criar endpoints REST para gerenciamento de contas.  
  - **Arquivos**: `backend/src/bills/bills.controller.ts`  
  - **Dependências**: Step 46  
  - **Instruções**: Defina rotas para CRUD e pagamento de contas.

### 8.2 Frontend: Gerenciamento de Contas

- [ ] **Step 48: Implementar Páginas de Gerenciamento de Contas**  
  - **Descrição**: Criar páginas para listar, criar, editar e pagar contas.  
  - **Arquivos**: `frontend/pages/bills/index.tsx`, `frontend/pages/bills/create.tsx`, `frontend/pages/bills/[id].tsx`, `frontend/components/bills/BillForm.tsx`, `frontend/components/bills/PaymentForm.tsx`  
  - **Dependências**: Step 8, Step 47, Step 28, Step 32, Step 36, Step 44  
  - **Instruções**: Inclua opções para parcelamento e pagamento parcial.

---

## Fase 9: Transações

### 9.1 Backend: Transações

- [ ] **Step 49: Implementar Entidade e Módulo Transaction**  
  - **Descrição**: Definir a entidade `Transaction` para registrar transações financeiras.  
  - **Arquivos**: `backend/src/transactions/entities/transaction.entity.ts`, `backend/src/transactions/transactions.module.ts`, `backend/src/transactions/dto/create-transaction.dto.ts`, `backend/src/transactions/dto/update-transaction.dto.ts`  
  - **Dependências**: Step 2, Step 40, Step 45  
  - **Instruções**: Inclua campos como `amount`, `date`, `billId`, `bankAccountId`.

- [ ] **Step 50: Implementar Serviço de Transações**  
  - **Descrição**: Criar um serviço para operações CRUD de transações, com atualização de saldos.  
  - **Arquivos**: `backend/src/transactions/transactions.service.ts`  
  - **Dependências**: Step 49  
  - **Instruções**: Atualize automaticamente o saldo da conta bancária ao criar uma transação.

- [ ] **Step 51: Implementar Controller de Transações**  
  - **Descrição**: Criar endpoints REST para gerenciamento de transações.  
  - **Arquivos**: `backend/src/transactions/transactions.controller.ts`  
  - **Dependências**: Step 50  
  - **Instruções**: Defina rotas CRUD e associe transações a contas.

### 9.2 Frontend: Gerenciamento de Transações

- [ ] **Step 52: Implementar Páginas de Gerenciamento de Transações**  
  - **Descrição**: Criar páginas para listar, criar e editar transações.  
  - **Arquivos**: `frontend/pages/transactions/index.tsx`, `frontend/pages/transactions/create.tsx`, `frontend/pages/transactions/[id].tsx`  
  - **Dependências**: Step 8, Step 51, Step 44, Step 48  
  - **Instruções**: Inclua dropdowns para seleção de contas e transações associadas.

---

## Fase 10: Papéis e Permissões

### 10.1 Backend: Papéis e Permissões

- [ ] **Step 53: Implementar Entidades e Módulos Role e Permission**  
  - **Descrição**: Definir as entidades `Role` e `Permission` para controle de acesso.  
  - **Arquivos**: `backend/src/roles/entities/role.entity.ts`, `backend/src/roles/entities/permission.entity.ts`, `backend/src/roles/roles.module.ts`, `backend/src/roles/dto/create-role.dto.ts`, `backend/src/roles/dto/update-role.dto.ts`, `backend/src/roles/dto/create-permission.dto.ts`  
  - **Dependências**: Step 2, Step 9  
  - **Instruções**: Crie relacionamentos entre `Role` e `Permission`.

- [ ] **Step 54: Implementar Serviços de Papéis e Permissões**  
  - **Descrição**: Criar serviços para operações CRUD de papéis e permissões.  
  - **Arquivos**: `backend/src/roles/roles.service.ts`, `backend/src/roles/permissions.service.ts`  
  - **Dependências**: Step 53  
  - **Instruções**: Implemente métodos para associar permissões a papéis.

- [ ] **Step 55: Implementar Controllers de Papéis e Permissões**  
  - **Descrição**: Criar endpoints REST para gerenciamento de papéis e permissões.  
  - **Arquivos**: `backend/src/roles/roles.controller.ts`, `backend/src/roles/permissions.controller.ts`  
  - **Dependências**: Step 54  
  - **Instruções**: Defina rotas CRUD para ambos.

- [ ] **Step 56: Implementar Guards para RBAC**  
  - **Descrição**: Criar guards para controle de acesso baseado em papéis.  
  - **Arquivos**: `backend/src/common/guards/rbac.guard.ts`, `backend/src/common/decorators/roles.decorator.ts`  
  - **Dependências**: Step 55  
  - **Instruções**: Use `@Roles` para proteger endpoints sensíveis.

### 10.2 Frontend: Gerenciamento de Papéis e Permissões

- [ ] **Step 57: Implementar Páginas de Gerenciamento de Papéis**  
  - **Descrição**: Criar páginas para listar, criar e editar papéis.  
  - **Arquivos**: `frontend/pages/roles/index.tsx`, `frontend/pages/roles/create.tsx`, `frontend/pages/roles/[id].tsx`  
  - **Dependências**: Step 8, Step 55  
  - **Instruções**: Inclua seletores de permissões.

- [ ] **Step 58: Implementar Páginas de Gerenciamento de Permissões**  
  - **Descrição**: Criar páginas para listar, criar e editar permissões.  
  - **Arquivos**: `frontend/pages/permissions/index.tsx`, `frontend/pages/permissions/create.tsx`, `frontend/pages/permissions/[id].tsx`  
  - **Dependências**: Step 8, Step 55  
  - **Instruções**: Use template CRUD.

---

## Fase 11: Notificações

### 11.1 Backend: Notificações

- [ ] **Step 59: Implementar Entidade e Módulo Notification**  
  - **Descrição**: Definir a entidade `Notification` para alertas e mensagens.  
  - **Arquivos**: `backend/src/notifications/entities/notification.entity.ts`, `backend/src/notifications/notifications.module.ts`, `backend/src/notifications/dto/create-notification.dto.ts`, `backend/src/notifications/dto/update-notification.dto.ts`  
  - **Dependências**: Step 2, Step 13  
  - **Instruções**: Inclua campos como `message`, `userId`, `read`.

- [ ] **Step 60: Implementar Serviço de E-mail**  
  - **Descrição**: Criar um serviço para envio de e-mails de notificação.  
  - **Arquivos**: `backend/src/notifications/email.service.ts`  
  - **Dependências**: Step 59  
  - **Instruções**: Use Nodemailer ou SendGrid para envio de e-mails.

- [ ] **Step 61: Implementar Serviço de Notificações**  
  - **Descrição**: Criar um serviço para gerenciar notificações no sistema.  
  - **Arquivos**: `backend/src/notifications/notifications.service.ts`  
  - **Dependências**: Step 60  
  - **Instruções**: Implemente métodos para criar notificações e marcar como lidas.

- [ ] **Step 62: Implementar Controller de Notificações**  
  - **Descrição**: Criar endpoints REST para gerenciamento de notificações.  
  - **Arquivos**: `backend/src/notifications/notifications.controller.ts`  
  - **Dependências**: Step 61  
  - **Instruções**: Defina rotas para listar e marcar notificações como lidas.

- [ ] **Step 63: Implementar Eventos para Notificações Automáticas**  
  - **Descrição**: Configurar eventos para disparar notificações automaticamente (ex.: vencimento de contas).  
  - **Arquivos**: `backend/src/common/events/events.module.ts`, `backend/src/common/events/events.service.ts`  
  - **Dependências**: Step 47, Step 51, Step 62  
  - **Instruções**: Use um sistema de eventos ou cron jobs para verificar condições de disparo.

### 11.2 Frontend: Gerenciamento de Notificações

- [ ] **Step 64: Implementar Componentes de Notificações**  
  - **Descrição**: Criar componentes para exibir e interagir com notificações.  
  - **Arquivos**: `frontend/components/notifications/NotificationBell.tsx`, `frontend/components/notifications/NotificationList.tsx`  
  - **Dependências**: Step 8, Step 62  
  - **Instruções**: Adicione um sino de notificações no cabeçalho com contador de não lidas.

---

## Fase 12: Dashboard e Relatórios

### 12.1 Backend: Dashboard e Relatórios

- [ ] **Step 65: Implementar Serviço de Dashboard**  
  - **Descrição**: Criar um serviço para agregar dados para o dashboard.  
  - **Arquivos**: `backend/src/dashboard/dashboard.service.ts`  
  - **Dependências**: Step 47, Step 51  
  - **Instruções**: Implemente consultas para resumo financeiro, contas a vencer, etc.

- [ ] **Step 66: Implementar Controller de Dashboard**  
  - **Descrição**: Criar endpoints para fornecer dados ao dashboard.  
  - **Arquivos**: `backend/src/dashboard/dashboard.controller.ts`  
  - **Dependências**: Step 65  
  - **Instruções**: Defina uma rota `GET /dashboard` que retorne dados agregados.

- [ ] **Step 67: Implementar Serviço de Relatórios**  
  - **Descrição**: Criar um serviço para gerar relatórios financeiros.  
  - **Arquivos**: `backend/src/reports/reports.service.ts`  
  - **Dependências**: Step 47, Step 51  
  - **Instruções**: Implemente lógica para gerar relatórios de fluxo de caixa, contas a pagar/receber, etc.

- [ ] **Step 68: Implementar Controller de Relatórios**  
  - **Descrição**: Criar endpoints para geração de relatórios.  
  - **Arquivos**: `backend/src/reports/reports.controller.ts`  
  - **Dependências**: Step 67  
  - **Instruções**: Defina rotas como `GET /reports/cash-flow` com parâmetros de filtro.

### 12.2 Frontend: Dashboard e Relatórios

- [ ] **Step 69: Implementar Página de Dashboard**  
  - **Descrição**: Criar uma página de dashboard com visão geral financeira.  
  - **Arquivos**: `frontend/pages/dashboard.tsx`, `frontend/components/dashboard/FinancialSummary.tsx`, `frontend/components/dashboard/RecentTransactions.tsx`  
  - **Dependências**: Step 8, Step 66  
  - **Instruções**: Use gráficos e tabelas para exibir dados agregados.

- [ ] **Step 70: Implementar Páginas de Relatórios**  
  - **Descrição**: Criar páginas para visualização e geração de relatórios.  
  - **Arquivos**: `frontend/pages/reports/index.tsx`, `frontend/pages/reports/cash-flow.tsx`, `frontend/pages/reports/payables.tsx`, `frontend/pages/reports/receivables.tsx`  
  - **Dependências**: Step 8, Step 68  
  - **Instruções**: Inclua filtros dinâmicos e opções de exportação.

---

## Fase 13: Refinamento da Experiência do Usuário

- [ ] **Step 71: Implementar Tema Visual Consistente**  
  - **Descrição**: Refinar a UI da aplicação para consistência visual e suporte a temas claro/escuro.  
  - **Arquivos**: `frontend/styles/`, `frontend/components/ui/`, `frontend/themes/`  
  - **Dependências**: Conclusão das interfaces principais  
  - **Instruções**: Use Tailwind CSS para criar um tema configurável.

- [ ] **Step 72: Implementar Recursos de Acessibilidade**  
  - **Descrição**: Garantir que o frontend seja acessível para todos os usuários.  
  - **Arquivos**: Componentes com problemas de acessibilidade  
  - **Dependências**: Step 71  
  - **Instruções**: Adicione suporte a leitores de tela, navegação por teclado e contraste adequado.

- [ ] **Step 73: Implementar Internacionalização (i18n)**  
  - **Descrição**: Adicionar suporte a múltiplos idiomas no frontend.  
  - **Arquivos**: `frontend/locales/`, `frontend/i18n.ts`  
  - **Dependências**: Step 71  
  - **Instruções**: Use `next-i18next` para gerenciar traduções em inglês e português.

---

## Fase 14: Recursos Avançados

- [ ] **Step 74: Implementar Exportação de Dados**  
  - **Descrição**: Adicionar funcionalidade para exportar dados em Excel/CSV.  
  - **Arquivos**: `backend/src/common/services/export.service.ts`, `frontend/components/common/ExportButton.tsx`  
  - **Dependências**: Conclusão das funcionalidades principais  
  - **Instruções**: Use uma biblioteca como `xlsx` para gerar arquivos exportáveis.

- [ ] **Step 75: Implementar Importação de Dados**  
  - **Descrição**: Permitir a importação de dados via upload de arquivos.  
  - **Arquivos**: `backend/src/common/services/import.service.ts`, `frontend/components/common/ImportButton.tsx`  
  - **Dependências**: Step 74  
  - **Instruções**: Valide os dados importados antes de salvar no banco.

- [ ] **Step 76: Implementar Suporte Offline no Frontend**  
  - **Descrição**: Adicionar suporte offline limitado para o frontend.  
  - **Arquivos**: `frontend/service-worker.js`, `frontend/contexts/OfflineContext.tsx`  
  - **Dependências**: Step 71  
  - **Instruções**: Use service workers para cache e sincronização de dados.

---

## Fase 15: Testes, Documentação e Deploy

### 15.1 Testes

- [ ] **Step 77: Implementar Testes Unitários, de Integração e E2E**  
  - **Descrição**: Desenvolver testes completos para backend e frontend.  
  - **Arquivos**: `backend/*.spec.ts`, `frontend/tests/`  
  - **Dependências**: Conclusão das funcionalidades principais  
  - **Instruções**: Use Jest para backend e Playwright para testes E2E, com cobertura mínima de 80%.

### 15.2 Documentação e Infraestrutura

- [ ] **Step 78: Configurar Documentação da API com Swagger**  
  - **Descrição**: Gerar documentação interativa da API com Swagger.  
  - **Arquivos**: `backend/src/main.ts`  
  - **Dependências**: Conclusão dos controllers  
  - **Instruções**: Instale `@nestjs/swagger` e configure o Swagger UI para todos os endpoints.

- [ ] **Step 79: Configurar Docker para Produção**  
  - **Descrição**: Preparar Dockerfiles otimizados para deploy em produção.  
  - **Arquivos**: `backend/Dockerfile.prod`, `frontend/Dockerfile.prod`, `docker-compose.prod.yml`  
  - **Dependências**: Step 3  
  - **Instruções**: Remova hot-reload e otimize as imagens para desempenho.

- [ ] **Step 80: Configurar CI/CD com GitHub Actions**  
  - **Descrição**: Implementar pipeline de integração e deploy contínuos.  
  - **Arquivos**: `.github/workflows/ci.yml`, `.github/workflows/deploy.yml`  
  - **Dependências**: Step 77  
  - **Instruções**: Configure testes automáticos e deploy para um ambiente de produção.

- [ ] **Step 81: Implantar Versão de Produção**  
  - **Descrição**: Realizar o deploy final do sistema em produção.  
  - **Arquivos**: Scripts de deploy  
  - **Dependências**: Todas as etapas anteriores  
  - **Instruções**: Execute o deploy com um plano de migração de dados e rollback em caso de falhas.
