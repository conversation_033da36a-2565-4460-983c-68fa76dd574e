
import { format } from "date-fns";
import {
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Trash2, Eye } from "lucide-react";
import { Transaction } from "@/types/transaction";
import { TransactionTypeBadge } from "./TransactionTypeBadge";
import { TransactionAccountInfo } from "./TransactionAccountInfo";
import { TransactionAmountDisplay } from "./TransactionAmountDisplay";

interface TransactionTableBodyProps {
  transactions: Transaction[];
  onEdit: (transaction: Transaction, mode?: "view" | "edit") => void;
}

export const TransactionTableBody = ({ transactions, onEdit }: TransactionTableBodyProps) => {
  // Garantir que transactions seja sempre um array
  const safeTransactions = Array.isArray(transactions) ? transactions : [];
  
  if (safeTransactions.length === 0) {
    return (
      <TableBody>
        <TableRow>
          <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
            Nenhuma transação encontrada para os filtros selecionados.
          </TableCell>
        </TableRow>
      </TableBody>
    );
  }

  return (
    <TableBody>
      {safeTransactions.map((transaction) => (
        <TableRow key={transaction.id}>
          <TableCell className="font-medium">{transaction.description}</TableCell>
          <TableCell>{transaction.entity}</TableCell>
          <TableCell className="whitespace-nowrap">
            {transaction.transactionDate ? 
              (typeof transaction.transactionDate === 'string' ? 
                format(new Date(transaction.transactionDate), 'MMM dd, yyyy') : 
                format(transaction.transactionDate, 'MMM dd, yyyy')
              ) : '-'}
          </TableCell>
          <TableCell className="text-right dark:text-white">
            <TransactionAmountDisplay transaction={transaction} />
          </TableCell>
          <TableCell>
            <TransactionAccountInfo transaction={transaction} />
          </TableCell>
          <TableCell className="w-32">
            <TransactionTypeBadge type={transaction.type} />
          </TableCell>
          <TableCell className="text-right">
            <div className="flex justify-end gap-2">
              <Button variant="ghost" size="icon" onClick={() => onEdit(transaction, "view")}>
                <Eye className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={() => onEdit(transaction, "edit")}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-destructive">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );
};
