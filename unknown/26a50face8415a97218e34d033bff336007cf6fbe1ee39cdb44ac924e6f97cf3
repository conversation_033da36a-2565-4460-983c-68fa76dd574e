
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeftRight } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { TransactionFormValues } from "../types/TransactionModalTypes";

interface TransactionTypeSectionProps {
  form: UseFormReturn<TransactionFormValues>;
}

export default function TransactionTypeSection({ form }: TransactionTypeSectionProps) {
  return (
    <FormField
      control={form.control}
      name="type"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Transaction Type</FormLabel>
          <Select 
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="accounts_receivable">Revenue</SelectItem>
              <SelectItem value="accounts_payable">Expense</SelectItem>
              <SelectItem value="transfer">
                <div className="flex items-center">
                  <ArrowLeftRight className="mr-2 h-4 w-4" />
                  <span>Transfer</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
