
import { useState, useMemo } from "react";
import { Transaction, TransferTransaction } from "@/types/transaction";

interface UseTransactionFiltersProps {
  data: Transaction[];
  searchTerm?: string;
  selectedPeriod?: string;
  selectedAccountId?: string;
  selectedEntityId?: string;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  transactionType?: string;
}

export const useTransactionFilters = ({
  data,
  searchTerm = "",
  selectedPeriod = "current-month",
  selectedAccountId = "all",
  selectedEntityId = "all",
  customDateRange = {},
  transactionType
}: UseTransactionFiltersProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  
  const filteredData = useMemo(() => {
    return data.filter(item => {
      if (transactionType && item.type !== transactionType) {
        return false;
      }
      
      const matchesSearch = 
        !searchTerm ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.entity.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.bankAccount.toLowerCase().includes(searchTerm.toLowerCase());
      
      let matchesAccount = true;
      if (selectedAccountId !== "all") {
        matchesAccount = 
          item.bankAccountId === selectedAccountId || 
          (item.type === 'transfer' && 
            (item as TransferTransaction).destinationAccountId === selectedAccountId);
      }
      
      let matchesEntity = true;
      if (selectedEntityId !== "all") {
        matchesEntity = item.entityId === selectedEntityId;
      }
      
      let matchesPeriod = true;
      const itemDate = item.transactionDate;
      const now = new Date();
      
      switch (selectedPeriod) {
        case "today":
          matchesPeriod = 
            itemDate.getDate() === now.getDate() &&
            itemDate.getMonth() === now.getMonth() &&
            itemDate.getFullYear() === now.getFullYear();
          break;
        case "current-week":
          const startOfWeek = new Date(now);
          startOfWeek.setDate(now.getDate() - now.getDay());
          startOfWeek.setHours(0, 0, 0, 0);
          const endOfWeek = new Date(now);
          endOfWeek.setDate(now.getDate() + (6 - now.getDay()));
          endOfWeek.setHours(23, 59, 59, 999);
          matchesPeriod = itemDate >= startOfWeek && itemDate <= endOfWeek;
          break;
        case "next-week":
          const startOfNextWeek = new Date(now);
          startOfNextWeek.setDate(now.getDate() - now.getDay() + 7);
          startOfNextWeek.setHours(0, 0, 0, 0);
          const endOfNextWeek = new Date(now);
          endOfNextWeek.setDate(now.getDate() + (13 - now.getDay()));
          endOfNextWeek.setHours(23, 59, 59, 999);
          matchesPeriod = itemDate >= startOfNextWeek && itemDate <= endOfNextWeek;
          break;
        case "current-month":
          matchesPeriod = 
            itemDate.getMonth() === now.getMonth() &&
            itemDate.getFullYear() === now.getFullYear();
          break;
        case "previous-month":
          const prevMonth = new Date(now);
          prevMonth.setMonth(now.getMonth() - 1);
          matchesPeriod = 
            itemDate.getMonth() === prevMonth.getMonth() &&
            itemDate.getFullYear() === prevMonth.getFullYear();
          break;
        case "next-month":
          const nextMonth = new Date(now);
          nextMonth.setMonth(now.getMonth() + 1);
          matchesPeriod = 
            itemDate.getMonth() === nextMonth.getMonth() &&
            itemDate.getFullYear() === nextMonth.getFullYear();
          break;
        case "last-3-months":
          const threeMonthsAgo = new Date(now);
          threeMonthsAgo.setMonth(now.getMonth() - 3);
          matchesPeriod = itemDate >= threeMonthsAgo;
          break;
        case "current-year":
          matchesPeriod = itemDate.getFullYear() === now.getFullYear();
          break;
        case "custom":
          if (customDateRange.startDate && customDateRange.endDate) {
            const start = new Date(customDateRange.startDate);
            start.setHours(0, 0, 0, 0);
            const end = new Date(customDateRange.endDate);
            end.setHours(23, 59, 59, 999);
            matchesPeriod = itemDate >= start && itemDate <= end;
          }
          break;
      }
      
      return matchesSearch && matchesAccount && matchesEntity && matchesPeriod;
    });
  }, [data, searchTerm, selectedAccountId, selectedEntityId, selectedPeriod, customDateRange, transactionType]);
  
  return {
    filteredData,
    currentPage,
    setCurrentPage
  };
};
