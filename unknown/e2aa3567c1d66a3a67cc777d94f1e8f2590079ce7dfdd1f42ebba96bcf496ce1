// backend/src/routes/entities/entities.module.ts
import { Modu<PERSON> } from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { EntitiesController } from './entities.controller';
import { EntityAddressesService } from './entity-addresses.service';
import { EntityAddressesController } from './entity-addresses.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { EntitiesRolesGuard } from './entities-roles.guard';
import { Reflector } from '@nestjs/core';

@Module({
  imports: [PrismaModule],
  controllers: [EntitiesController, EntityAddressesController],
  providers: [
    EntitiesService,
    EntityAddressesService,
    EntitiesRolesGuard,
    Reflector,
  ],
  exports: [EntitiesService, EntityAddressesService],
})
export class EntitiesModule {}
