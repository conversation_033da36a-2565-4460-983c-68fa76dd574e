# API RESTful - FluxoMax

API RESTful desenvolvida com NestJS seguindo o padrão MVC para o sistema FluxoMax.

## Tecnologias Utilizadas

- **Framework**: NestJS
- **Linguagem**: TypeScript
- **ORM**: Prisma
- **Banco de Dados**: PostgreSQL (v15+)
- **Autenticação**: JWT com refresh tokens
- **Documentação**: Swagger

## Estrutura do Projeto

```
backend/
├── src/
│   ├── controllers/     # Controladores que recebem requisições e retornam respostas
│   ├── models/          # Definição dos modelos de dados/schemas
│   ├── routes/          # Módulos da aplicação
│   ├── middlewares/     # Interceptadores de requisições
│   ├── services/        # Lógica de negócio
│   └── app.module.ts    # Módulo principal da aplicação
├── prisma/              # Configuração do Prisma ORM
└── package.json
```

## Configuração do Ambiente

1. Instale as dependências:
```bash
npm install
```

2. Configure o arquivo `.env` com suas credenciais de banco de dados:
```
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/fluxomax?schema=public"
JWT_SECRET="seu_jwt_secret_super_seguro"
JWT_REFRESH_SECRET="seu_refresh_token_secret_super_seguro"
JWT_EXPIRATION="15m"
JWT_REFRESH_EXPIRATION="7d"
```

3. Inicie o banco de dados PostgreSQL usando Docker:
```bash
docker compose up -d
```

4. Execute as migrações do Prisma:
```bash
npx prisma migrate dev --name init
```

5. Gere o cliente Prisma:
```bash
npx prisma generate
```

## Executando a Aplicação

```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod
```

A API estará disponível em `http://localhost:3000/api`.
A documentação Swagger estará disponível em `http://localhost:3000/api/docs`.

## Endpoints

### Autenticação

- `POST /api/auth/register` - Registrar um novo usuário
- `POST /api/auth/login` - Autenticar usuário
- `POST /api/auth/refresh-token` - Renovar token de acesso
- `POST /api/auth/logout` - Encerrar sessão

### Usuários

- `GET /api/users/profile` - Obter perfil do usuário autenticado
