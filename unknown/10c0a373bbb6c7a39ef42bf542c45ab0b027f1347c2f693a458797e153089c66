import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { transactionService } from '@/services/api';
import { CreateTransactionRequest, UpdateTransactionRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar transações com paginação e filtros
export const useTransactions = (
  page = 1, 
  limit = 10, 
  filters?: {
    search?: string,
    type?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string,
    bankAccountId?: string
  }
) => {
  const { search, type, status, startDate, endDate, categoryId, entityId, bankAccountId } = filters || {};
  
  return useQuery({
    queryKey: ['transactions', { page, limit, search, type, status, startDate, endDate, categoryId, entityId, bankAccountId }],
    queryFn: () => transactionService.getTransactions(
      page, limit, search, type, status, startDate, endDate, categoryId, entityId, bankAccountId
    ),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar uma transação específica por ID
export const useTransaction = (id: string) => {
  return useQuery({
    queryKey: ['transactions', id],
    queryFn: () => transactionService.getTransactionById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar transações por conta bancária
export const useTransactionsByBankAccount = (
  accountId: string,
  page = 1,
  limit = 10,
  startDate?: string,
  endDate?: string
) => {
  return useQuery({
    queryKey: ['transactions', 'bankAccount', accountId, { page, limit, startDate, endDate }],
    queryFn: () => transactionService.getTransactionsByBankAccount(accountId, page, limit, startDate, endDate),
    enabled: !!accountId,
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para criar uma nova transação
export const useCreateTransaction = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateTransactionRequest) => transactionService.createTransaction(data),
    onSuccess: (newTransaction) => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      
      // Invalidar também saldos das contas bancárias envolvidas
      if (newTransaction.bankAccountId) {
        queryClient.invalidateQueries({ 
          queryKey: ['bankAccounts', newTransaction.bankAccountId, 'balance'] 
        });
      }
      
      if (newTransaction.destinationBankAccountId) {
        queryClient.invalidateQueries({ 
          queryKey: ['bankAccounts', newTransaction.destinationBankAccountId, 'balance'] 
        });
      }
      
      toast.success('Transação criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar transação. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma transação existente
export const useUpdateTransaction = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateTransactionRequest }) => 
      transactionService.updateTransaction(id, data),
    onSuccess: (updatedTransaction) => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.setQueryData(['transactions', updatedTransaction.id], updatedTransaction);
      
      // Invalidar também saldos das contas bancárias envolvidas
      if (updatedTransaction.bankAccountId) {
        queryClient.invalidateQueries({ 
          queryKey: ['bankAccounts', updatedTransaction.bankAccountId, 'balance'] 
        });
      }
      
      if (updatedTransaction.destinationBankAccountId) {
        queryClient.invalidateQueries({ 
          queryKey: ['bankAccounts', updatedTransaction.destinationBankAccountId, 'balance'] 
        });
      }
      
      toast.success('Transação atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar transação. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma transação
export const useDeleteTransaction = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => transactionService.deleteTransaction(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.removeQueries({ queryKey: ['transactions', id] });
      
      // Seria ideal também invalidar os saldos das contas bancárias afetadas,
      // mas não temos acesso aos IDs após a exclusão. Por segurança, invalidamos todos os saldos.
      queryClient.invalidateQueries({ queryKey: ['bankAccounts'] });
      
      toast.success('Transação excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir transação. Por favor, tente novamente.'
      );
    }
  });
};
