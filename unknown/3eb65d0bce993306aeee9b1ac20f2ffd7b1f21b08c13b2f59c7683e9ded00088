
import { TableBody, TableCell, TableRow } from "@/components/ui/table";
import { format } from "date-fns";
import { Edit, Eye, Trash2 } from "lucide-react";
import AccountsReceivableStatusBadge from "./AccountsReceivableStatusBadge";
import AccountsReceivableRowActions from "./AccountsReceivableRowActions";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatCurrency } from "@/utils/currencyUtils";

interface AccountsReceivableTableBodyProps {
  filteredData: any[];
  onEdit: (item: any) => void;
  handleReceive: (item: any) => void;
  handleView: (item: any) => void;
}

export default function AccountsReceivableTableBody({
  filteredData,
  onEdit,
  handleReceive,
  handleView,
}: AccountsReceivableTableBodyProps) {
  return (
    <TableBody>
      {filteredData.length === 0 ? (
        <TableRow>
          <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
            Nenhuma conta encontrada
          </TableCell>
        </TableRow>
      ) : (
        filteredData.map((item) => (
          <TableRow key={item.id} className="hover:bg-muted/40 transition-colors">
            <TableCell>
              <AccountsReceivableRowActions
                item={item}
                handleReceive={handleReceive}
                handleView={handleView}
              />
            </TableCell>
            <TableCell className="font-medium">
              {item.description}
            </TableCell>
            <TableCell>{item.entity}</TableCell>
            <TableCell>{format(item.dueDate, 'dd/MM/yyyy')}</TableCell>
            <TableCell className="text-right font-semibold text-black dark:text-white">{formatCurrency(item.amount)}</TableCell>
            <TableCell className="text-right">{formatCurrency(item.paidAmount)}</TableCell>
            <TableCell>
              <AccountsReceivableStatusBadge status={item.status} />
            </TableCell>
            <TableCell>
              {item.receiveDate ? (
                <div className="text-xs text-muted-foreground">
                  Recebimento: {format(item.receiveDate, 'dd/MM/yyyy')}
                </div>
              ) : (
                item.category
              )}
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button 
                        onClick={() => handleView(item)}
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Visualizar</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <button 
                  onClick={() => onEdit(item)}
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button 
                  className="text-muted-foreground hover:text-destructive transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </TableCell>
          </TableRow>
        ))
      )}
    </TableBody>
  );
}
