-- Function to create company permissions
CREATE OR REPLACE FUNCTION create_company_permissions()
RETURNS TRIGGER AS $$
DECLARE
    sys_permission RECORD;
BEGIN
    -- For each system permission, create a company permission
    FOR sys_permission IN SELECT * FROM system_permissions
    LOOP
        INSERT INTO permissions (
            id, 
            company_id, 
            action, 
            description, 
            system_permission_id, 
            created_at, 
            updated_at
        ) VALUES (
            gen_random_uuid(), 
            NEW.id, 
            sys_permission.code, 
            sys_permission.description, 
            sys_permission.id, 
            NOW(), 
            NOW()
        );
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create company permissions when a new company is created
DROP TRIGGER IF EXISTS create_company_permissions_trigger ON companies;
CREATE TRIGGER create_company_permissions_trigger
AFTER INSERT ON companies
FOR EACH ROW
EXECUTE FUNCTION create_company_permissions();
