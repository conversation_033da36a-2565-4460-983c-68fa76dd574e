import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateRoleDto, RoleDto, UpdateRoleDto } from '../models/role.model';
import { v4 as uuidv4 } from 'uuid';
import { QueryUtil } from '../utils/query.util';
import { mapToRoleDto } from '../utils/mappers.util';

@Injectable()
export class RoleManagerService {
  private readonly logger = new Logger(RoleManagerService.name);

  constructor(
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Cria um novo papel
   * @param createRoleDto Dados para criação do papel
   * @returns O papel criado
   */
  async create(createRoleDto: CreateRoleDto): Promise<RoleDto> {
    try {
      // Verificar se já existe um papel com o mesmo nome na empresa
      if (
        await this.queryUtil.checkRoleExists(
          createRoleDto.companyId,
          createRoleDto.name,
        )
      ) {
        throw new ConflictException(
          'Já existe um papel com este nome nesta empresa',
        );
      }

      const roleId = uuidv4();

      // Criar o papel usando SQL nativo
      await this.prisma.$executeRaw`
        INSERT INTO roles (
          id, 
          company_id, 
          name, 
          description, 
          is_system_role, 
          created_at, 
          updated_at
        ) VALUES (
          ${roleId}::uuid, 
          ${createRoleDto.companyId}::uuid, 
          ${createRoleDto.name}, 
          ${createRoleDto.description || null}, 
          ${createRoleDto.isSystemRole || false}, 
          NOW(), 
          NOW()
        )
      `;

      // Buscar o papel criado
      const role = await this.queryUtil.findRoleById(roleId);
      return mapToRoleDto(role);
    } catch (error) {
      this.logger.error(`Erro ao criar papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Atualiza um papel existente
   * @param id ID do papel
   * @param updateRoleDto Dados para atualização do papel
   * @returns O papel atualizado
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleDto> {
    try {
      // Verificar se o papel existe
      const existingRole = await this.queryUtil.findRoleById(id);

      // Verificar se já existe outro papel com o mesmo nome na empresa
      if (
        updateRoleDto.name &&
        (await this.queryUtil.checkRoleExists(
          existingRole.company_id,
          updateRoleDto.name,
          id,
        ))
      ) {
        throw new ConflictException(
          'Já existe outro papel com este nome nesta empresa',
        );
      }

      // Atualizar o papel
      await this.prisma.$executeRaw`
        UPDATE roles SET
          name = COALESCE(${updateRoleDto.name}, name),
          description = COALESCE(${updateRoleDto.description}, description),
          is_system_role = COALESCE(${updateRoleDto.isSystemRole}, is_system_role),
          updated_at = NOW()
        WHERE id = ${id}::uuid
      `;

      // Buscar o papel atualizado
      const updatedRole = await this.queryUtil.findRoleById(id);
      return mapToRoleDto(updatedRole);
    } catch (error) {
      this.logger.error(`Erro ao atualizar papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Remove um papel
   * @param id ID do papel
   */
  async remove(id: string): Promise<void> {
    try {
      // Verificar se o papel existe
      await this.queryUtil.findRoleById(id);

      // Verificar se o papel está sendo usado
      if (await this.queryUtil.checkRoleInUse(id)) {
        throw new ConflictException(
          'Este papel está em uso e não pode ser removido',
        );
      }

      // Remover o papel
      await this.prisma.$executeRaw`
        DELETE FROM roles WHERE id = ${id}::uuid
      `;
    } catch (error) {
      this.logger.error(`Erro ao remover papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Método para criar a tabela de papéis se ela não existir
   */
  async ensureRolesTableExists(): Promise<void> {
    try {
      await this.prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS public.roles (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          company_id UUID NOT NULL,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          is_system_role BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          deleted_at TIMESTAMPTZ,
          FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
        )
      `;
    } catch (error) {
      this.logger.error(`Erro ao criar tabela de papéis: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Método para criar um papel padrão para uma empresa
   * @param companyId ID da empresa
   * @returns O papel criado ou existente
   */
  async createDefaultRole(companyId: string): Promise<RoleDto> {
    try {
      // Verificar se já existe um papel de administrador para esta empresa
      const existingAdminRole = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM roles 
        WHERE company_id = ${companyId}::uuid 
        AND name = 'Administrador'
      `;

      if (existingAdminRole.length > 0) {
        return mapToRoleDto(existingAdminRole[0]);
      }

      // Criar o papel de administrador
      return await this.create({
        companyId,
        name: 'Administrador',
        description: 'Acesso completo ao sistema',
        isSystemRole: true,
      });
    } catch (error) {
      this.logger.error(`Erro ao criar papel padrão: ${error.message}`, error.stack);
      throw error;
    }
  }
} 