import { Module } from '@nestjs/common';
import { UserCompanyRolesController } from '../controllers/user-company-roles.controller';
import { UserCompanyRolesService } from '../services/user-company-roles.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [UserCompanyRolesController],
  providers: [UserCompanyRolesService],
  exports: [UserCompanyRolesService],
})
export class UserCompanyRolesModule {}
