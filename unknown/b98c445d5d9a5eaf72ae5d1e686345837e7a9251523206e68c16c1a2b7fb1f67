import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { RolesGuard } from '../../guards/roles.guard';
import { Reflector } from '@nestjs/core';

@Module({
  imports: [PrismaModule],
  controllers: [ReportsController],
  providers: [ReportsService, RolesGuard, Reflector],
  exports: [ReportsService],
})
export class ReportsModule {}
