// backend/src/routes/accounts-payable/accounts-payable.module.ts
import { Module } from '@nestjs/common';
import { AccountsPayableService } from './accounts-payable.service';
import { AccountsPayableController } from './accounts-payable.controller';
import { PrismaModule } from '../../prisma/prisma.module'; // Assuming PrismaModule is exported

@Module({
  imports: [PrismaModule], // Import PrismaModule to use PrismaService
  controllers: [AccountsPayableController],
  providers: [AccountsPayableService],
  exports: [AccountsPayableService], // Export if needed by other modules
})
export class AccountsPayableModule {}