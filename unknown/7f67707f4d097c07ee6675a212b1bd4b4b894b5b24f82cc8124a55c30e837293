
import { useState, useEffect, useRef } from 'react';
import { formatMonetaryInput } from '@/utils/currencyUtils';

export function useMonetaryInput(initialValue: string = '0') {
  const [displayValue, setDisplayValue] = useState<string>('0,00');
  const inputRef = useRef<HTMLInputElement>(null);
  const [internalValue, setInternalValue] = useState<string>('000');
  const cursorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize with proper value
  useEffect(() => {
    if (initialValue) {
      const stringValue = String(initialValue);
      if (stringValue.includes(',')) {
        // Convert from 123,45 format to numeric string "12345"
        const numericValue = stringValue.replace(/\./g, '').replace(',', '');
        // Ensure at least 3 digits (2 decimal places)
        const paddedValue = numericValue.padStart(3, '0');
        setInternalValue(paddedValue);
        setDisplayValue(formatMonetaryInput(paddedValue));
      } else {
        // Handle values that might be coming as "123.45" or just "123"
        const standardized = stringValue.replace(',', '.'); 
        const parts = standardized.split('.');
        const integerPart = parts[0] || '0';
        const decimalPart = parts.length > 1 ? parts[1].padEnd(2, '0').substring(0, 2) : '00';
        const numericString = integerPart + decimalPart;
        setInternalValue(numericString);
        setDisplayValue(formatMonetaryInput(numericString));
      }
    }
  }, [initialValue]);

  // Clear any existing cursor positioning timeout when component unmounts
  useEffect(() => {
    return () => {
      if (cursorTimeoutRef.current) {
        clearTimeout(cursorTimeoutRef.current);
      }
    };
  }, []);

  const handleValueChange = (newValue: string) => {
    // Tratar apenas números - remover qualquer caractere não numérico
    const onlyDigits = newValue.replace(/\D/g, '');

    // Se ficar vazio, reset para zero
    if (!onlyDigits) {
      setInternalValue('000');
      setDisplayValue('0,00');
      return '0,00';
    }

    // Atualizar valor interno (usado em cálculos)
    setInternalValue(onlyDigits);
    
    // Formatar para exibição
    const formattedValue = formatMonetaryInput(onlyDigits);
    setDisplayValue(formattedValue);

    // Clear any existing timeout to prevent multiple cursor positioning calls
    if (cursorTimeoutRef.current) {
      clearTimeout(cursorTimeoutRef.current);
    }

    // Posiciona o cursor no final do input após renderização
    cursorTimeoutRef.current = setTimeout(() => {
      if (inputRef.current) {
        const length = formattedValue.length;
        inputRef.current.focus();
        inputRef.current.setSelectionRange(length, length);
      }
    }, 0);

    return formattedValue;
  };

  // Converte o valor formatado para número (para uso em cálculos)
  const numericValue = parseFloat(
    (internalValue || '0').replace(/^0+/, '') || '0'
  ) / 100;

  return {
    displayValue,
    numericValue,
    handleValueChange,
    inputRef
  };
}
