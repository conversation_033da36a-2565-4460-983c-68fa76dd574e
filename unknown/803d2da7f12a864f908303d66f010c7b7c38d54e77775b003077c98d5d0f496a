import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { zipCodeService } from '@/services/api';
import { toast } from 'sonner';

// Hook para buscar informações de CEP
export const useZipCode = (zipCode: string) => {
  return useQuery({
    queryKey: ['zipCodes', zipCode],
    queryFn: () => zipCodeService.getZipCode(zipCode),
    enabled: !!zipCode && zipCode.length === 8, // Ativa apenas quando tiver um CEP válido
    staleTime: 24 * 60 * 60 * 1000, // 24 horas (dados geralmente estáticos)
    retry: 1 // Limita a uma nova tentativa em caso de falha
  });
};

// Hook para limpar o cache de CEPs
export const useClearZipCodeCache = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      // Apenas invalidar o cache sem chamar serviço backend
      return Promise.resolve();
    },
    onSuccess: () => {
      // Invalidar todos os CEPs em cache
      queryClient.invalidateQueries({ queryKey: ['zipCodes'] });
      toast.success('Cache de CEPs limpo com sucesso!');
    },
    onError: (error: any) => {
      toast.error('Falha ao limpar cache de CEPs. Por favor, tente novamente.');
    }
  });
};
