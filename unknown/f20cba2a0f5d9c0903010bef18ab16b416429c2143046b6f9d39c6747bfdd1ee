// backend/src/routes/recurrence-types/recurrence-types.module.ts
import { Module } from '@nestjs/common';
import { RecurrenceTypesService } from './recurrence-types.service';
import { RecurrenceTypesController } from './recurrence-types.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [RecurrenceTypesController],
  providers: [RecurrenceTypesService],
  exports: [RecurrenceTypesService],
})
export class RecurrenceTypesModule {}
