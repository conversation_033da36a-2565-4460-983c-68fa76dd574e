import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import {
  CreateUserCompanyRoleDto,
  UserCompanyRoleDto,
} from '../models/user-company-role.model';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UserCompanyRolesService {
  constructor(private prisma: PrismaService) {}

  async create(
    createUserCompanyRoleDto: CreateUserCompanyRoleDto,
  ): Promise<UserCompanyRoleDto> {
    // Verificar se o usuário existe
    const userExists = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM users WHERE id = ${createUserCompanyRoleDto.userId}::uuid AND deleted_at IS NULL
    `;

    if (userExists.length === 0) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Verificar se a empresa existe
    const companyExists = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM companies WHERE id = ${createUserCompanyRoleDto.companyId}::uuid AND deleted_at IS NULL
    `;

    if (companyExists.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    // Verificar se o papel existe
    const roleExists = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${createUserCompanyRoleDto.roleId}::uuid
    `;

    if (roleExists.length === 0) {
      throw new NotFoundException('Papel não encontrado');
    }

    // Verificar se já existe uma associação idêntica
    const existingAssociation = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM user_company_roles 
      WHERE user_id = ${createUserCompanyRoleDto.userId}::uuid 
      AND company_id = ${createUserCompanyRoleDto.companyId}::uuid 
      AND role_id = ${createUserCompanyRoleDto.roleId}::uuid
    `;

    if (existingAssociation.length > 0) {
      throw new ConflictException('Esta associação já existe');
    }

    const associationId = uuidv4();

    // Criar a associação
    await this.prisma.$executeRaw`
      INSERT INTO user_company_roles (
        id, 
        user_id, 
        company_id, 
        role_id, 
        created_at, 
        updated_at
      ) VALUES (
        ${associationId}::uuid, 
        ${createUserCompanyRoleDto.userId}::uuid, 
        ${createUserCompanyRoleDto.companyId}::uuid, 
        ${createUserCompanyRoleDto.roleId}::uuid, 
        NOW(), 
        NOW()
      )
    `;

    // Buscar a associação criada
    const associationResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM user_company_roles WHERE id = ${associationId}::uuid
    `;

    if (associationResult.length === 0) {
      throw new NotFoundException('Falha ao recuperar a associação criada');
    }

    const association = associationResult[0];

    return {
      id: association.id,
      userId: association.user_id,
      companyId: association.company_id,
      roleId: association.role_id,
      createdAt: association.created_at,
      updatedAt: association.updated_at,
    };
  }

  async findAll(
    companyId?: string,
    userId?: string,
    page = 1,
    limit = 10,
  ): Promise<{
    items: UserCompanyRoleDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;

    // Construir a cláusula WHERE dinamicamente
    let whereClause = '';
    const params: any[] = [];

    if (companyId) {
      whereClause += 'company_id = ?::uuid';
      params.push(companyId);
    }

    if (userId) {
      if (whereClause) whereClause += ' AND ';
      whereClause += 'user_id = ?::uuid';
      params.push(userId);
    }

    // Se não houver filtros, mostrar todas as associações
    if (!whereClause) {
      whereClause = '1=1';
    }

    // Contar total de associações
    const countQuery = `SELECT COUNT(*) as count FROM user_company_roles WHERE ${whereClause}`;
    const countResult = await this.prisma.$queryRawUnsafe<
      Array<{ count: string }>
    >(countQuery, ...params);

    const total = Number(countResult[0].count);

    // Buscar associações paginadas
    const listQuery = `
      SELECT * FROM user_company_roles 
      WHERE ${whereClause} 
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${skip}
    `;

    const associationsResult = await this.prisma.$queryRawUnsafe<
      Array<{
        id: string;
        user_id: string;
        company_id: string;
        role_id: string;
        created_at: Date;
        updated_at: Date;
      }>
    >(listQuery, ...params);

    const associations = associationsResult.map((association) => ({
      id: association.id,
      userId: association.user_id,
      companyId: association.company_id,
      roleId: association.role_id,
      createdAt: association.created_at,
      updatedAt: association.updated_at,
    }));

    return {
      items: associations,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<UserCompanyRoleDto> {
    const associationResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM user_company_roles WHERE id = ${id}::uuid
    `;

    if (associationResult.length === 0) {
      throw new NotFoundException('Associação não encontrada');
    }

    const association = associationResult[0];

    return {
      id: association.id,
      userId: association.user_id,
      companyId: association.company_id,
      roleId: association.role_id,
      createdAt: association.created_at,
      updatedAt: association.updated_at,
    };
  }

  async remove(id: string): Promise<void> {
    // Verificar se a associação existe
    const existingAssociation = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM user_company_roles WHERE id = ${id}::uuid
    `;

    if (existingAssociation.length === 0) {
      throw new NotFoundException('Associação não encontrada');
    }

    // Remover a associação
    await this.prisma.$executeRaw`
      DELETE FROM user_company_roles WHERE id = ${id}::uuid
    `;
  }

  // Método para verificar se um usuário está associado a uma empresa
  async isUserAssociatedWithCompany(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    const association = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM user_company_roles 
      WHERE user_id = ${userId}::uuid 
      AND company_id = ${companyId}::uuid
    `;

    return association.length > 0;
  }

  // Método para obter os papéis de um usuário em uma empresa
  async getUserRolesInCompany(
    userId: string,
    companyId: string,
  ): Promise<string[]> {
    const roles = await this.prisma.$queryRaw<any[]>`
      SELECT r.id, r.name
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid 
      AND ucr.company_id = ${companyId}::uuid
    `;

    return roles.map((role) => role.id);
  }
}
