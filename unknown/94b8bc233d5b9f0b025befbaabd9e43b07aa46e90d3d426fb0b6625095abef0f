export const formatCurrency = (value: number): string => {
  return value.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

/**
 * Formata um valor numérico como entrada monetária durante digitação
 * seguindo o padrão onde cada dígito entra pela direita e empurra os anteriores
 * @param numericString String contendo apenas números
 * @returns String formatada como valor monetário (ex: "123" -> "1,23")
 */
export const formatMonetaryInput = (numericString: string): string => {
  // Se string vazia ou só zeros, retorna "0,00"
  if (!numericString || /^0+$/.test(numericString)) {
    return '0,00';
  }
  
  // Adiciona zeros à esquerda se necessário para garantir pelo menos 3 caracteres
  const paddedValue = numericString.padStart(3, '0');
  
  // Separa a parte inteira da decimal
  const integerPart = paddedValue.slice(0, paddedValue.length - 2) || '0';
  const decimalPart = paddedValue.slice(-2);
  
  // Formata com pontos para milhares se necessário
  const formattedInteger = Number(integerPart).toLocaleString('pt-BR');
  
  return `${formattedInteger},${decimalPart}`;
};

/**
 * Converte um valor formatado como moeda para um número
 * @param formattedValue String formatada (ex: "1.234,56" ou "1234.56") ou valor numérico
 * @returns Número (ex: 1234.56)
 */
export const parseCurrencyToNumber = (formattedValue: string | number): number => {
  if (formattedValue === null || formattedValue === undefined) return 0;
  
  // Se já é um número, retorna como está
  if (typeof formattedValue === 'number') return formattedValue;
  
  // Remove prefixo R$ e espaços
  let cleanValue = formattedValue.trim().replace(/^R\$\s*/, '');
  
  // Se está no formato "X.XX" (ponto como separador decimal)
  if (/^\d+\.\d+$/.test(cleanValue)) {
    return parseFloat(cleanValue);
  }
  
  // Remove tudo exceto números, ponto e vírgula
  cleanValue = cleanValue.replace(/[^\d.,]/g, '');
  
  // Se não tem valores, retorna 0
  if (!cleanValue) return 0;
  
  // Converte para o formato numérico JavaScript (ponto como separador decimal)
  // Remove pontos de milhar e substitui vírgula por ponto para decimal
  return parseFloat(
    cleanValue.replace(/\./g, '').replace(',', '.')
  );
};

/**
 * Converte um número para string formatada como moeda sem o símbolo (para inputs)
 * @param value Número para formatar
 * @returns String formatada (ex: 1234.56 -> "1.234,56")
 */
export const numberToFormattedString = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '0,00';
  
  return value.toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).replace('R$', '').trim();
};
