import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ZipCodesService } from './zip-codes.service';
import { ZipCodesController } from './zip-codes.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
  ],
  controllers: [ZipCodesController],
  providers: [ZipCodesService],
  exports: [ZipCodesService],
})
export class ZipCodesModule {}
