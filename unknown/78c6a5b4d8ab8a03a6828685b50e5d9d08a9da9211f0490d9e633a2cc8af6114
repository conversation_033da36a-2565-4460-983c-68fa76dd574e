import { cn } from "@/lib/utils";
import { Bell, ChevronDown, LogOut, Menu, Moon, Sun, User } from "lucide-react";
import { useState, useEffect } from "react";
import IconButton from "../ui-custom/IconButton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface NavbarProps {
  onToggleSidebar: () => void;
  className?: string;
}

const Navbar = ({ onToggleSidebar, className }: NavbarProps) => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check system preference for dark mode on initial load
  useEffect(() => {
    const isDark = document.documentElement.classList.contains("dark");
    setIsDarkMode(isDark);
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle("dark");
  };

  const handleLogout = () => {
    logout();
  };

  const navigateToProfile = () => {
    navigate("/profile");
  };

  return (
    <header
      className={cn(
        "h-16 px-4 flex items-center justify-between bg-background/80 dark:bg-dark-bg/90 backdrop-blur-md border-b border-border sticky top-0 z-50 animate-fade-in",
        className
      )}
    >
      <div className="flex items-center gap-3">
        <IconButton
          icon={<Menu className="h-5 w-5" />}
          onClick={onToggleSidebar}
          variant="ghost"
          className="mr-4 lg:hidden"
          label="Alternar menu lateral"
        />

        {/* Logo */}
        <div className="flex items-center">
          <span className="text-xl font-medium">Fluxo</span>
          <span className="text-xl font-medium text-primary">Max</span>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <IconButton
          icon={<Bell className="h-5 w-5" />}
          variant="ghost"
          label="Notificações"
        />

        <IconButton
          icon={isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          onClick={toggleTheme}
          variant="ghost"
          label="Alternar tema"
        />

        <DropdownMenu>
          <DropdownMenuTrigger className="focus:outline-none" asChild>
            <div className="flex items-center ml-2 cursor-pointer">
              <div className="h-9 w-9 rounded-md bg-primary/10 flex items-center justify-center text-primary font-medium">
                {user?.profile?.firstName?.charAt(0) || user?.email?.charAt(0) || 'U'}
              </div>
              <div className="ml-2 hidden md:block">
                <p className="text-sm font-medium">{user?.profile?.firstName || user?.email?.split('@')[0] || 'Usuário'}</p>
                <p className="text-xs text-muted-foreground">{user?.role?.name || 'Usuário'}</p>
              </div>
              <ChevronDown className="h-4 w-4 ml-1 text-muted-foreground hidden md:block" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 py-2 bg-card dark:bg-dark-card shadow-lg rounded-md border border-border">
            <DropdownMenuLabel className="font-normal px-4 py-2 text-sm text-muted-foreground">
              <div className="flex flex-col">
                <p className="font-medium text-card-foreground">{user?.profile?.firstName || 'Usuário'} {user?.profile?.lastName || ''}</p>
                <p className="text-xs text-muted-foreground">{user?.email || '<EMAIL>'}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-border" />
            <DropdownMenuItem
              className="px-4 py-2.5 cursor-pointer flex items-center gap-3 focus:bg-secondary focus:text-secondary-foreground"
              onClick={navigateToProfile}
            >
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-foreground">Meu Perfil</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-border" />
            <DropdownMenuItem
              className="px-4 py-2.5 cursor-pointer flex items-center gap-3 text-destructive focus:bg-destructive/10"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              <span>Sair</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Navbar;
