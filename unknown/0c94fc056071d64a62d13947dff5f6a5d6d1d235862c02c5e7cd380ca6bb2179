services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - '3000:3000'
      - '5555:5555' # Porta para o Prisma Studio
    restart: unless-stopped
    init: true
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/node_modules
      - prisma_migrations:/app/prisma/migrations
    depends_on:
      database: # Use the name of your service DB
        condition: service_healthy # Espera o healthcheck passar
    environment:
      - NODE_ENV=development
    # O comando será definido no Dockerfile/entrypoint

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - '3001:3001'
    restart: unless-stopped
    init: true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000/api
    command: npm run dev -- --host 0.0.0.0 --port 3001

  database:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: fluxomax
    ports:
      - '5432:5432'
    volumes:
      - db_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5
volumes:
  db_data:
  prisma_migrations:
