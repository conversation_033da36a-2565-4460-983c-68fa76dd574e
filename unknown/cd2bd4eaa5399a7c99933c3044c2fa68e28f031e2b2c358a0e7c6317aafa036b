// backend/src/routes/accounts-payable/dto/pay-account-payable.dto.ts
import {
  IsDateString,
  IsNumber,
  IsOptional,
  IsUUID,
  Min,
  IsString,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PayAccountPayableDto {
  @ApiPropertyOptional({ description: 'Data do pagamento (formato: YYYY-MM-DD)', example: '2025-12-31' })
  @IsDateString()
  @IsOptional()
  paymentDate?: string; // If not provided, current date will be used

  @ApiProperty({ description: 'ID da conta bancária para debitar o pagamento', example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  bankAccountId: string; // Bank account to debit the payment from

  @ApiPropertyOptional({ description: 'Valor do pagamento (se não fornecido, ser<PERSON> pago o valor total restante)', example: 1500.50, minimum: 0.01 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Type(() => Number)
  amount?: number; // Optional: If not provided, the full remaining amount will be paid

  @ApiPropertyOptional({ description: 'Descrição personalizada para a transação', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string; // Optional: Custom description for the transaction

  @ApiPropertyOptional({ description: 'ID do método de pagamento', example: '123e4567-e89b-12d3-a456-************' })
  @IsOptional()
  @IsUUID()
  paymentMethodId?: string; // Optional: Payment method used

  @ApiPropertyOptional({ description: 'Observações adicionais para a transação', maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  notes?: string; // Optional: Additional notes for the transaction
}
