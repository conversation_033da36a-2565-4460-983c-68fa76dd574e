
import {
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Edit, Eye, Trash2 } from "lucide-react";
import AccountsPayableStatusBadge from "./AccountsPayableStatusBadge";
import AccountsPayableRowActions from "./AccountsPayableRowActions";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatCurrency } from "@/utils/currencyUtils";

interface AccountsPayableTableBodyProps {
  filteredData: any[];
  onEdit: (item: any) => void;
  handlePay: (item: any) => void;
  handleView: (item: any) => void;
}

export default function AccountsPayableTableBody({
  filteredData,
  onEdit,
  handlePay,
  handleView,
}: AccountsPayableTableBodyProps) {
  return (
    <TableBody>
      {filteredData.length === 0 ? (
        <TableRow>
          <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
            Nenhuma conta encontrada
          </TableCell>
        </TableRow>
      ) : (
        filteredData.map((item) => (
          <TableRow key={item.id} className="hover:bg-muted/40 transition-colors">
            <TableCell>
              <AccountsPayableRowActions
                item={item}
                handlePay={handlePay}
                handleView={handleView}
              />
            </TableCell>
            <TableCell className="font-medium">
              {item.description}
            </TableCell>
            <TableCell>{item.entity?.name || 'N/A'}</TableCell>
            <TableCell>{format(new Date(item.dueDate), 'dd/MM/yyyy')}</TableCell>
            <TableCell className="text-right font-semibold text-black dark:text-white">{formatCurrency(Math.abs(Number(item.amount)))}</TableCell>
            <TableCell className="text-right">{formatCurrency(Number(item.paidAmount))}</TableCell>
            <TableCell><AccountsPayableStatusBadge status={item.status} /></TableCell>
            <TableCell>
              {item.paymentDate ? (
                <div className="text-xs text-muted-foreground">
                  <span className="block">Pagamento:</span>
                  <span className="block">{format(new Date(item.paymentDate), 'dd/MM/yyyy')}</span>
                </div>
              ) : (
                item.category?.name || 'Sem categoria'
              )}
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => handleView(item)}
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Visualizar</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <button
                  onClick={() => onEdit(item)}
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  className="text-muted-foreground hover:text-destructive transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </TableCell>
          </TableRow>
        ))
      )}
    </TableBody>
  );
}
