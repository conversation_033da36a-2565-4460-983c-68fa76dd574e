import { Module } from '@nestjs/common';
import { RolesController } from '../controllers/roles.controller';
import { RolesService } from '../services/roles.service';
import { PrismaModule } from '../prisma/prisma.module';
import { RoleManagerService } from '../services/role-manager.service';
import { RoleQueryService } from '../services/role-query.service';
import { UtilsModule } from '../utils/utils.module';

@Module({
  imports: [PrismaModule, UtilsModule],
  controllers: [RolesController],
  providers: [
    RolesService,
    RoleManagerService,
    RoleQueryService
  ],
  exports: [
    RolesService,
    RoleManagerService,
    RoleQueryService
  ],
})
export class RolesModule {}
