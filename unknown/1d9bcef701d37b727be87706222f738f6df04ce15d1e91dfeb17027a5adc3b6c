import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, MaxLength } from 'class-validator';

export class UpdateCustomPeriodDto {
  @ApiPropertyOptional({
    description: 'Nome do período personalizado',
    example: 'Primeiro Trimestre 2025 (Atualizado)',
    maxLength: 100
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Data de início do período (formato YYYY-MM-DD)',
    example: '2025-01-01',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de fim do período (formato YYYY-MM-DD)',
    example: '2025-03-31',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
