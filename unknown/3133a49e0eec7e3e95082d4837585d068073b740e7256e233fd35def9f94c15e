import { Module } from '@nestjs/common';
import { CustomPeriodsService } from './custom-periods.service';
import { CustomPeriodsController } from './custom-periods.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CustomPeriodsController],
  providers: [CustomPeriodsService],
  exports: [CustomPeriodsService],
})
export class CustomPeriodsModule {}
