#!/bin/sh
# Não usar set -e para permitir tratamento de erros
# set -e # Exit immediately if a command exits with a non-zero status.

# Opcional, mas recomendado: Espera explícita pela porta do DB
# (healthcheck do compose já ajuda, mas isso é uma garantia extra)
# host="$POSTGRES_HOST" # Assumindo que você define POSTGRES_HOST no compose env
# port="5432"
# echo "Waiting for database at $host:$port..."
# while ! nc -z $host $port; do
#   sleep 1 # wait for 1 second before check again
# done
# echo "Database is up!"

# Verificar se o diretório de migrações existe
if [ ! -d "/app/prisma/migrations" ]; then
  echo "Criando diretório de migrações..."
  mkdir -p /app/prisma/migrations
  # Copiar migrações existentes se necessário
  cp -r /app/prisma/migrations_backup/* /app/prisma/migrations/ 2>/dev/null || true
fi

# Função para limpar o estado das migrações no banco de dados
clean_migration_state() {
  echo "Limpando estado das migrações no banco de dados..."
  # Verificar se o banco de dados está acessível
  if psql $DATABASE_URL -c "SELECT 1" > /dev/null 2>&1; then
    # Remover registros da tabela _prisma_migrations
    echo "Removendo registros da tabela _prisma_migrations..."
    psql $DATABASE_URL -c "DROP TABLE IF EXISTS _prisma_migrations;"
    return 0
  else
    echo "Não foi possível conectar ao banco de dados."
    return 1
  fi
}

# Tentar aplicar migrações
echo "Running Prisma migrations..."
# Use migrate deploy for non-interactive deployment
npx prisma migrate deploy

# Verificar se houve erro de migração
if [ $? -ne 0 ]; then
  echo "Erro ao aplicar migrações. Tentando limpar o estado das migrações..."

  # Tentar limpar o estado das migrações
  if clean_migration_state; then
    echo "Estado das migrações limpo com sucesso. Tentando aplicar migrações novamente..."
    # Tentar aplicar migrações novamente
    npx prisma migrate deploy

    # Se ainda houver erro, tentar usar db push
    if [ $? -ne 0 ]; then
      echo "Ainda há erro ao aplicar migrações. Tentando usar db push..."
      npx prisma db push --accept-data-loss --force-reset
    fi
  else
    echo "Não foi possível limpar o estado das migrações."
  fi
fi

# Compilar os scripts TypeScript
echo "Compilando scripts de configuração..."
npm run build

# Garantir que o diretório dist/scripts exista e copiar arquivos SQL
# Este passo é crucial e deve ser executado DEPOIS da compilação
echo "Criando diretório dist/scripts e copiando arquivos SQL..."
mkdir -p /app/dist/scripts
cp -f /app/src/scripts/*.sql /app/dist/scripts/
cp -f /app/src/scripts/*.js /app/dist/scripts/ 2>/dev/null || true
ls -la /app/dist/scripts/
echo "Arquivos SQL copiados para /app/dist/scripts/"

# Executar o script de inicialização do banco de dados
echo "Executando script de inicialização do banco de dados..."
/bin/bash /app/src/scripts/init-db.sh

echo "Starting NestJS application..."
# Executa o comando original passado para o container (o CMD do Dockerfile)
exec "$@"