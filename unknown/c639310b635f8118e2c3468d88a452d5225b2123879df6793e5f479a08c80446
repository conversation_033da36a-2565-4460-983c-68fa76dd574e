import {
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { SystemPermissionsService } from '../system-permissions/system-permissions.service';
import { PermissionsService } from '../permissions/permissions.service';
import { RolesService } from '../roles/roles.service';
import { UserCompanyRolesService } from '../user-company-roles/user-company-roles.service';

@Injectable()
export class RbacService {
  private readonly logger = new Logger(RbacService.name);

  constructor(
    private prisma: PrismaService,
    private systemPermissionsService: SystemPermissionsService,
    private permissionsService: PermissionsService,
    private rolesService: RolesService,
    private userCompanyRolesService: UserCompanyRolesService,
  ) {}

  /**
   * Configura o RBAC para uma empresa
   * 1. Cria permissões do sistema padrão (se não existirem)
   * 2. Cria permissões da empresa com base nas permissões do sistema
   * 3. Cria papel de administrador padrão
   * 4. Atribui todas as permissões ao papel de administrador
   */
  async setupRbacForCompany(companyId: string): Promise<{ message: string }> {
    try {
      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // 1. Criar permissões do sistema padrão
      await this.systemPermissionsService.createDefaultSystemPermissions();

      // 2. Criar permissões da empresa
      await this.permissionsService.createCompanyPermissions(companyId);

      // 3. Criar papel de administrador padrão
      const adminRole = await this.rolesService.createDefaultAdminRole(companyId);

      // 4. Atribuir todas as permissões ao papel de administrador
      const permissions = await this.prisma.permission.findMany({
        where: { companyId },
        select: { id: true },
      });

      if (permissions.length > 0) {
        // Usar transação para garantir consistência
        await this.prisma.$transaction(async (prisma) => {
          // Remover permissões existentes do papel
          await prisma.rolePermission.deleteMany({
            where: { roleId: adminRole.id },
          });

          // Adicionar todas as permissões ao papel
          for (const permission of permissions) {
            await prisma.rolePermission.create({
              data: {
                roleId: adminRole.id,
                permissionId: permission.id,
              },
            });
          }
        });
      }

      return { message: 'RBAC configurado com sucesso para a empresa' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao configurar RBAC para empresa: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Verifica se um usuário tem uma permissão específica em uma empresa
   */
  async hasPermission(
    userId: string,
    companyId: string,
    permissionAction: string,
  ): Promise<boolean> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        return false;
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        return false;
      }

      // Buscar os papéis do usuário na empresa
      const userRoles = await this.prisma.role.findMany({
        where: {
          companyId,
          deletedAt: null,
          userCompanyRoles: {
            some: {
              userId,
              companyId,
              deletedAt: null,
            },
          },
        },
      });

      // Se o usuário tem um papel de administrador, ele tem todas as permissões
      if (userRoles.some((role) => role.isAdmin)) {
        return true;
      }

      // Buscar a permissão na empresa
      const permission = await this.prisma.permission.findFirst({
        where: {
          companyId,
          action: permissionAction,
        },
      });

      if (!permission) {
        return false;
      }

      // Verificar se algum dos papéis do usuário tem a permissão
      const roleIds = userRoles.map((role) => role.id);
      const rolePermission = await this.prisma.rolePermission.findFirst({
        where: {
          roleId: { in: roleIds },
          permissionId: permission.id,
        },
      });

      return !!rolePermission;
    } catch (error) {
      this.logger.error(
        `Erro ao verificar permissão: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Obtém todas as permissões de um usuário em uma empresa
   */
  async getUserPermissions(
    userId: string,
    companyId: string,
  ): Promise<string[]> {
    try {
      // Verificar se o usuário existe
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          deletedAt: null,
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Verificar se a empresa existe
      const company = await this.prisma.company.findFirst({
        where: {
          id: companyId,
          deleted_at: null,
        },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Buscar os papéis do usuário na empresa
      const userRoles = await this.prisma.role.findMany({
        where: {
          companyId,
          deletedAt: null,
          userCompanyRoles: {
            some: {
              userId,
              companyId,
              deletedAt: null,
            },
          },
        },
      });

      // Se o usuário tem um papel de administrador, ele tem todas as permissões
      if (userRoles.some((role) => role.isAdmin)) {
        const allPermissions = await this.prisma.permission.findMany({
          where: { companyId },
          select: { action: true },
        });
        return allPermissions.map((p) => p.action);
      }

      // Buscar as permissões dos papéis do usuário
      const roleIds = userRoles.map((role) => role.id);
      const permissions = await this.prisma.permission.findMany({
        where: {
          companyId,
          rolePermissions: {
            some: {
              roleId: { in: roleIds },
            },
          },
        },
        select: { action: true },
      });

      return permissions.map((p) => p.action);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar permissões do usuário: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
