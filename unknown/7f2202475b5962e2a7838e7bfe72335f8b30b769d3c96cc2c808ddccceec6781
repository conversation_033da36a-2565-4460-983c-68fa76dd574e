import { ApiConnectionTest } from '@/components/ApiConnectionTest';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useEffect } from 'react';

export default function DiagnosticoPage() {
  useEffect(() => {
    document.title = 'FluxoMax - Diagnóstico do Sistema';
  }, []);
  return (
    <div className="container py-8 max-w-5xl">
      <h1 className="text-3xl font-bold mb-2">Diagnóstico do Sistema</h1>
      <p className="text-muted-foreground mb-6">
        Ferramentas para diagnosticar problemas e verificar a conectividade com serviços
      </p>
      
      <Tabs defaultValue="api" className="space-y-4">
        <TabsList>
          <TabsTrigger value="api">API &amp; Conectividade</TabsTrigger>
          <TabsTrigger value="sistema">Sistema</TabsTrigger>
          <TabsTrigger value="desempenho">Desempenho</TabsTrigger>
        </TabsList>
        
        <TabsContent value="api" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Status da Conexão com Backend</CardTitle>
                <CardDescription>
                  Verifica a conexão com a API do backend
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ApiConnectionTest />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Informações do Ambiente</CardTitle>
                <CardDescription>
                  Configurações e variáveis de ambiente
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Variáveis de Ambiente</h3>
                  <div className="bg-muted p-3 rounded text-sm">
                    <div className="mb-2">
                      <span className="font-mono text-blue-600">VITE_API_URL: </span>
                      <span className="font-mono">{import.meta.env.VITE_API_URL || '(não definido)'}</span>
                    </div>
                    <div>
                      <span className="font-mono text-blue-600">MODE: </span>
                      <span className="font-mono">{import.meta.env.MODE || '(não definido)'}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">Informações do Navegador</h3>
                  <div className="bg-muted p-3 rounded text-sm">
                    <div className="mb-2">
                      <span className="font-mono text-blue-600">User Agent: </span>
                      <span className="font-mono break-all text-xs">{typeof window !== 'undefined' ? window.navigator.userAgent : 'Indisponível durante SSR'}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Logs e Depuração</CardTitle>
              <CardDescription>
                Ferramentas para ajudar na identificação de problemas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-3">
                <h3 className="font-medium mb-2">Dicas de Solução de Problemas</h3>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  <li>Verifique se o backend está em execução e acessível</li>
                  <li>Confirme se o CORS está configurado corretamente no backend</li>
                  <li>Verifique se as variáveis de ambiente estão configuradas corretamente</li>
                  <li>Consulte o console do navegador para mensagens de erro detalhadas</li>
                  <li>Verifique os logs do Docker para problemas no backend</li>
                </ul>
              </div>
              
              <Separator className="my-4" />
              
              <div>
                <h3 className="font-medium mb-2">Comandos Úteis</h3>
                <div className="bg-black text-white p-3 rounded text-sm font-mono space-y-2">
                  <p># Reiniciar os contêineres Docker</p>
                  <p>docker compose restart</p>
                  <p className="mt-2"># Ver logs do backend</p>
                  <p>docker logs fluxo-max-backend-1 -f</p>
                  <p className="mt-2"># Ver logs do frontend</p>
                  <p>docker logs fluxo-max-frontend-1 -f</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="sistema">
          <Card>
            <CardHeader>
              <CardTitle>Diagnóstico do Sistema</CardTitle>
              <CardDescription>
                Esta funcionalidade será implementada em breve
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded">
                <p>Esta seção está em desenvolvimento e será disponibilizada em uma atualização futura.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="desempenho">
          <Card>
            <CardHeader>
              <CardTitle>Métricas de Desempenho</CardTitle>
              <CardDescription>
                Esta funcionalidade será implementada em breve
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded">
                <p>Esta seção está em desenvolvimento e será disponibilizada em uma atualização futura.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
