
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { CheckSquare, ShieldCheck } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface UserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (user: any) => void;
  user?: any;
}

export function UserModal({ open, onOpenChange, onSave, user }: UserModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    isAdmin: false,
    active: true,
  });

  // Reset form and populate with user data when modal opens or user changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        password: '',
        confirmPassword: '',
        isAdmin: user.isAdmin || false,
        active: user.active !== undefined ? user.active : true,
      });
    } else {
      setFormData({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        isAdmin: false,
        active: true,
      });
    }
  }, [user, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Only validate passwords if this is a new user or if password fields were filled
    if (!user || (formData.password && formData.confirmPassword)) {
      if (formData.password !== formData.confirmPassword) {
        alert('As senhas não coincidem!');
        return;
      }
    }
    
    // Remove password fields if editing and no password was entered
    const dataToSave = { ...formData };
    if (user && !formData.password) {
      delete dataToSave.password;
      delete dataToSave.confirmPassword;
    }
    
    onSave(dataToSave);
  };

  const isEditMode = !!user;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {isEditMode ? "Editar Usuário" : "Novo Usuário"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode 
                ? "Modifique os dados do usuário conforme necessário."
                : "Preencha os dados para cadastrar um novo usuário no sistema."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                Nome do Usuário<span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Nome completo"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">
                E-mail<span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">
                Senha{!isEditMode && <span className="text-red-500">*</span>}
                {isEditMode && <span className="text-xs text-gray-500 ml-2">(Deixe em branco para manter a senha atual)</span>}
              </Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="********"
                required={!isEditMode}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">
                Confirmar Senha{!isEditMode && <span className="text-red-500">*</span>}
              </Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="********"
                required={!isEditMode}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="isAdmin" 
                checked={formData.isAdmin}
                onCheckedChange={(checked) => 
                  setFormData({...formData, isAdmin: checked as boolean})
                }
              />
              <Label htmlFor="isAdmin" className="font-normal cursor-pointer flex items-center gap-2">
                <ShieldCheck className="h-4 w-4" />
                Administrador
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="active" 
                checked={formData.active}
                onCheckedChange={(checked) => 
                  setFormData({...formData, active: checked as boolean})
                }
              />
              <Label htmlFor="active" className="font-normal cursor-pointer flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Usuário Ativo
              </Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
            >
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
