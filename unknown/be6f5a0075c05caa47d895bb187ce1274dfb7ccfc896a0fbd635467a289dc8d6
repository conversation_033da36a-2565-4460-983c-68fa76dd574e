// backend/src/routes/address-types/dto/update-address-type.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateAddressTypeDto {
  @ApiPropertyOptional({
    description: 'Nome do tipo de endereço',
    example: 'Comercial',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: 'Descrição do tipo de endereço',
    example: 'Endereço comercial ou de trabalho',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
