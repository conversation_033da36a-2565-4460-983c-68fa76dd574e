# Gerenciamento de Banco de Dados com Prisma

Este documento descreve o fluxo de trabalho simplificado para gerenciar o banco de dados usando o Prisma ORM no projeto FluxoMax.

## Visão Geral

O sistema de gerenciamento de banco de dados foi projetado para simplificar e automatizar as operações comuns do Prisma ORM, como:

- Resetar o banco de dados
- Consolidar migrações
- Criar novas migrações
- Aplicar migrações pendentes
- Executar seeding
- Verificar o status das migrações
- Forçar atualizações do banco de dados
- Atualizar o schema.prisma com base no banco de dados
- Gerar o cliente Prisma

Todas essas operações podem ser executadas tanto no ambiente local quanto no ambiente Docker.

## Arquitetura

O sistema consiste em:

1. **Script principal**: `scripts/db-manager.js` - Um script Node.js que centraliza todas as operações do Prisma
2. **Scripts NPM**: Atalhos definidos no `package.json` para facilitar o uso do script principal
3. **Makefile**: Interface de linha de comando para os scripts NPM

## Estrutura de Arquivos

- `prisma/schema.prisma`: Define o modelo de dados e a configuração do banco de dados
- `prisma/migrations/`: Contém as migrações do banco de dados
- `prisma/seed.sh`: Script para popular o banco de dados com dados iniciais
- `scripts/db-manager.js`: Script principal que centraliza todas as operações do Prisma

## Comandos Disponíveis

### Usando o Makefile (Recomendado)

O Makefile fornece uma interface simples para os comandos do Prisma:

```bash
# Mostrar ajuda
make help

# Resetar o banco de dados (ambiente local)
make db-reset

# Resetar o banco de dados (ambiente Docker)
make db-reset docker=1

# Consolidar todas as migrações em uma única
make db-squash

# Criar uma nova migração
make db-create name=add_user_field

# Aplicar migrações pendentes
make db-deploy

# Aplicar migrações pendentes no Docker
make db-deploy docker=1

# Executar o seeding do banco de dados
make db-seed

# Verificar o status das migrações
make db-status

# Forçar atualização do banco de dados (aceita perda de dados)
make db-push-force

# Atualizar schema.prisma com base no banco de dados
make db-introspect

# Gerar cliente Prisma
make db-generate

# Executar um comando Prisma personalizado
make db-custom cmd="migrate dev --name add_user_field"
```

### Usando os Scripts NPM

Alternativamente, você pode usar os scripts NPM diretamente:

```bash
# Mostrar ajuda
npm run db help

# Resetar o banco de dados
npm run db:reset

# Consolidar migrações
npm run db:squash

# Criar uma nova migração
npm run db:create

# Aplicar migrações pendentes
npm run db:deploy

# Executar o seeding do banco de dados
npm run db:seed

# Verificar o status das migrações
npm run db:status

# Forçar atualização do banco de dados
npm run db:push-force

# Atualizar schema.prisma com base no banco de dados
npm run db:introspect

# Gerar cliente Prisma
npm run db:generate

# Executar comandos no Docker
npm run db:docker
```

### Usando o Script Diretamente

Você também pode usar o script `db-manager.js` diretamente:

```bash
# Mostrar ajuda
node scripts/db-manager.js help

# Resetar o banco de dados
node scripts/db-manager.js reset

# Criar uma nova migração
node scripts/db-manager.js create add_user_field

# Executar no Docker
node scripts/db-manager.js deploy --docker
```

## Fluxo de Trabalho Recomendado

### Desenvolvimento Inicial

Durante o desenvolvimento inicial, quando o esquema do banco de dados está mudando frequentemente:

1. Modifique o `schema.prisma` conforme necessário
2. Execute `make db-push-force` para atualizar o banco de dados rapidamente
3. Quando estiver satisfeito com as alterações, crie uma migração formal:
   ```bash
   make db-create name=adicionar_campo_usuario
   ```

### Desenvolvimento em Equipe

Quando trabalhar em equipe, siga este fluxo:

1. Antes de começar a trabalhar, atualize seu banco de dados:
   ```bash
   make db-deploy
   ```

2. Faça suas alterações no `schema.prisma`

3. Crie uma migração para suas alterações:
   ```bash
   make db-create name=adicionar_campo_usuario
   ```

4. Revise o arquivo SQL gerado em `prisma/migrations/YYYYMMDDHHMMSS_adicionar_campo_usuario/migration.sql`

5. Aplique a migração:
   ```bash
   make db-deploy
   ```

### Consolidação de Migrações

Se o histórico de migrações ficar muito grande, você pode consolidá-las:

1. Faça backup do banco de dados (se necessário)
2. Execute:
   ```bash
   make db-squash
   ```
3. Isso criará uma única migração que representa o estado atual do esquema
4. Compartilhe a migração consolidada com a equipe

## Trabalhando com Docker

### Configuração Inicial do Ambiente Docker

Para trabalhar com o banco de dados no ambiente Docker:

1. Inicie os contêineres Docker:
   ```bash
   ./dev.sh start
   ```

2. Verifique se os contêineres estão em execução:
   ```bash
   docker ps
   ```

3. Use os comandos com a opção `docker=1` para executar no contêiner:
   ```bash
   make db-status docker=1
   ```

### Exemplos Práticos com Docker

#### Cenário 1: Iniciar um Projeto em uma Nova Máquina

Quando você clonar o repositório em uma nova máquina:

```bash
# 1. Inicie os contêineres Docker
./dev.sh start

# 2. Aplique todas as migrações existentes
make db-deploy docker=1

# 3. Execute o seeding para popular o banco de dados
make db-seed docker=1
```

#### Cenário 2: Resetar o Banco de Dados Durante o Desenvolvimento

Quando você precisar começar com um banco de dados limpo:

```bash
# Resetar completamente o banco de dados (apaga todos os dados)
make db-reset docker=1
```

#### Cenário 3: Fazer Alterações no Esquema do Banco de Dados

```bash
# 1. Modifique o arquivo prisma/schema.prisma

# 2. Crie uma nova migração
make db-create name=adicionar_campo_email docker=1

# 3. Revise o arquivo SQL gerado

# 4. Aplique a migração
make db-deploy docker=1
```

#### Cenário 4: Atualização Rápida Durante o Desenvolvimento

Quando você está fazendo mudanças frequentes e não precisa manter um histórico de migrações:

```bash
# Atualizar o banco de dados diretamente sem criar migrações
make db-push-force docker=1
```

#### Cenário 5: Verificar o Estado das Migrações

```bash
# Verificar quais migrações foram aplicadas e quais estão pendentes
make db-status docker=1
```

#### Cenário 6: Executar Comandos Personalizados do Prisma

```bash
# Validar o schema.prisma
make db-custom cmd="validate" docker=1

# Gerar SQL para uma migração sem aplicá-la
make db-custom cmd="migrate diff --from-schema-datamodel --to-schema-datamodel --script" docker=1
```

#### Cenário 7: Resolver Problemas de Conexão

Se você estiver enfrentando problemas de conexão com o banco de dados:

```bash
# 1. Verificar o status dos contêineres
docker ps

# 2. Reiniciar os contêineres
./dev.sh restart

# 3. Verificar os logs do banco de dados
./dev.sh logs:db
```

## Resolução de Problemas

### Erro de Conexão com o Banco de Dados

Se você receber um erro como `Can't reach database server`:

1. Verifique se o banco de dados está em execução:
   ```bash
   docker ps
   ```

2. Verifique as credenciais no arquivo `.env`

3. Tente reiniciar os contêineres:
   ```bash
   ./dev.sh restart
   ```

### Erro de Perda de Dados

Se você receber um aviso sobre possível perda de dados:

1. Revise cuidadosamente as alterações
2. Se for seguro prosseguir, use:
   ```bash
   make db-push-force
   ```

### Conflitos de Migração

Se houver conflitos entre migrações:

1. Tente aplicar as migrações uma por uma:
   ```bash
   make db-custom cmd="migrate up --create-only"
   ```

2. Se o problema persistir, considere consolidar as migrações:
   ```bash
   make db-squash
   ```

## Comandos Avançados

### Executar Comandos Personalizados

Você pode executar qualquer comando do Prisma usando:

```bash
make db-custom cmd="<comando-prisma>"
```

Exemplos:

```bash
# Verificar a versão do Prisma
make db-custom cmd="--version"

# Executar uma migração específica
make db-custom cmd="migrate up --create-only"

# Validar o schema.prisma
make db-custom cmd="validate"
```

## Integração com CI/CD

O script `db-manager.js` pode ser facilmente integrado em pipelines de CI/CD para automatizar o gerenciamento do banco de dados em ambientes de teste e produção.

### Exemplo de Configuração para GitHub Actions

```yaml
name: Database Migration

on:
  push:
    branches: [ main ]
    paths:
      - 'backend/prisma/**'

jobs:
  migrate-database:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: cd backend && npm install

      - name: Apply database migrations
        run: cd backend && node scripts/db-manager.js deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
```

### Exemplo de Script para Deploy em Produção

Crie um script `deploy-prod.sh` para facilitar o deploy em produção:

```bash
#!/bin/bash

# Definir variáveis de ambiente para produção
export DATABASE_URL="*********************************************/banco_producao"

# Aplicar migrações sem confirmação interativa
NODE_ENV=production node scripts/db-manager.js deploy

echo "Migrações aplicadas com sucesso em produção!"
```

### Boas Práticas para CI/CD

1. **Sempre teste as migrações em um ambiente de staging antes de aplicá-las em produção**
2. **Faça backup do banco de dados antes de aplicar migrações em produção**
3. **Use variáveis de ambiente para armazenar credenciais sensíveis**
4. **Configure notificações para alertar a equipe sobre falhas nas migrações**
5. **Mantenha um registro (log) detalhado das migrações aplicadas em cada ambiente**

## Conclusão

Este sistema de gerenciamento de banco de dados simplifica o trabalho com o Prisma ORM, fornecendo uma interface consistente e fácil de usar para todas as operações comuns. Ao seguir o fluxo de trabalho recomendado, você pode evitar problemas comuns e manter seu banco de dados sincronizado com seu código.

A abordagem unificada com um único script central (`db-manager.js`) reduz a complexidade e facilita a manutenção, além de fornecer uma experiência consistente tanto para o ambiente local quanto para o ambiente Docker.
