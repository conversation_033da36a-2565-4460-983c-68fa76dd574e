import { PrismaService } from '../services/prisma.service';
import { UserRawResult, ProfileRawResult, CompanyRawResult, RoleRawResult } from './sql-transforms.util';
import { NotFoundException } from '@nestjs/common';

/**
 * Utilitários para consultas SQL comuns
 */
export class QueryUtil {
  constructor(private prisma: PrismaService) {}

  /**
   * Busca um usuário pelo email
   * @param email Email do usuário
   * @returns O usuário encontrado
   * @throws NotFoundException se o usuário não existir
   */
  async findUserByEmail(email: string): Promise<UserRawResult> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.email = ${email} AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return user;
  }

  /**
   * Busca um usuário pelo ID
   * @param id ID do usuário
   * @returns O usuário encontrado
   * @throws NotFoundException se o usuário não existir
   */
  async findUserById(id: string): Promise<UserRawResult> {
    const userResult = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
             p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
      FROM "users" u
      LEFT JOIN "profiles" p ON u.id = p.id
      WHERE u.id = ${id}::uuid AND u.deleted_at IS NULL
    `;

    const user = userResult.length > 0 ? userResult[0] : null;

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    return user;
  }

  /**
   * Verifica se existe um usuário com um determinado email
   * @param email Email a ser verificado
   * @returns true se existir, false caso contrário
   */
  async checkUserExistsByEmail(email: string): Promise<boolean> {
    const existingUser = await this.prisma.$queryRaw<UserRawResult[]>`
      SELECT * FROM "users" WHERE email = ${email} AND deleted_at IS NULL
    `;

    return existingUser.length > 0;
  }

  /**
   * Verifica se existe um perfil com um determinado username
   * @param username Username a ser verificado
   * @returns true se existir, false caso contrário
   */
  async checkProfileExistsByUsername(username: string): Promise<boolean> {
    const existingProfile = await this.prisma.$queryRaw<ProfileRawResult[]>`
      SELECT * FROM "profiles" WHERE username = ${username} AND deleted_at IS NULL
    `;

    return existingProfile.length > 0;
  }

  /**
   * Verifica se existe uma empresa com um determinado CNPJ
   * @param cnpj CNPJ a ser verificado
   * @returns true se existir, false caso contrário
   */
  async checkCompanyExistsByCnpj(cnpj: string): Promise<boolean> {
    const existingCompany = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM "companies" WHERE cnpj = ${cnpj} AND deleted_at IS NULL
    `;

    return existingCompany.length > 0;
  }

  /**
   * Busca uma empresa pelo ID
   * @param id ID da empresa
   * @returns A empresa encontrada
   * @throws NotFoundException se a empresa não existir
   */
  async findCompanyById(id: string): Promise<CompanyRawResult> {
    const company = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (company.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    return company[0];
  }

  /**
   * Busca um papel pelo ID
   * @param id ID do papel
   * @returns O papel encontrado
   * @throws NotFoundException se o papel não existir
   */
  async findRoleById(id: string): Promise<RoleRawResult> {
    const roleResult = await this.prisma.$queryRaw<RoleRawResult[]>`
      SELECT * FROM roles WHERE id = ${id}::uuid
    `;

    if (roleResult.length === 0) {
      throw new NotFoundException('Papel não encontrado');
    }

    return roleResult[0];
  }

  /**
   * Verifica se existe um papel com o mesmo nome em uma empresa
   * @param companyId ID da empresa
   * @param name Nome do papel
   * @param excludeRoleId ID do papel a ser excluído da verificação (para atualizações)
   * @returns true se existir, false caso contrário
   */
  async checkRoleExists(
    companyId: string,
    name: string,
    excludeRoleId?: string,
  ): Promise<boolean> {
    let query = this.prisma.$queryRaw<RoleRawResult[]>`
      SELECT * FROM roles 
      WHERE company_id = ${companyId}::uuid 
      AND name = ${name}
    `;

    if (excludeRoleId) {
      query = this.prisma.$queryRaw<RoleRawResult[]>`
        SELECT * FROM roles 
        WHERE company_id = ${companyId}::uuid 
        AND name = ${name}
        AND id != ${excludeRoleId}::uuid
      `;
    }

    const existingRole = await query;
    return existingRole.length > 0;
  }

  /**
   * Verifica se um papel está sendo usado por usuários
   * @param roleId ID do papel
   * @returns true se estiver em uso, false caso contrário
   */
  async checkRoleInUse(roleId: string): Promise<boolean> {
    const usageCount = await this.prisma.$queryRaw<[{ count: string }]>`
      SELECT COUNT(*) as count FROM user_company_roles WHERE role_id = ${roleId}::uuid
    `;

    return parseInt(usageCount[0].count, 10) > 0;
  }
} 