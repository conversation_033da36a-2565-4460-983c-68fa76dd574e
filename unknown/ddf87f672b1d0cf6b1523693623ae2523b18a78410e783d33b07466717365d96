import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateRoleDto, RoleDto, UpdateRoleDto } from '../models/role.model';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RolesService {
  constructor(private prisma: PrismaService) {}

  async create(createRoleDto: CreateRoleDto): Promise<RoleDto> {
    // Verificar se já existe um papel com o mesmo nome na empresa
    const existingRole = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles 
      WHERE company_id = ${createRoleDto.companyId}::uuid 
      AND name = ${createRoleDto.name}
    `;

    if (existingRole.length > 0) {
      throw new ConflictException(
        'Já existe um papel com este nome nesta empresa',
      );
    }

    const roleId = uuidv4();

    // Criar o papel usando SQL nativo
    await this.prisma.$executeRaw`
      INSERT INTO roles (
        id, 
        company_id, 
        name, 
        description, 
        is_system_role, 
        created_at, 
        updated_at
      ) VALUES (
        ${roleId}::uuid, 
        ${createRoleDto.companyId}::uuid, 
        ${createRoleDto.name}, 
        ${createRoleDto.description || null}, 
        ${createRoleDto.isSystemRole || false}, 
        NOW(), 
        NOW()
      )
    `;

    // Buscar o papel criado
    const roleResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${roleId}::uuid
    `;

    if (roleResult.length === 0) {
      throw new NotFoundException('Falha ao recuperar o papel criado');
    }

    const role = roleResult[0];

    return {
      id: role.id,
      companyId: role.company_id,
      name: role.name,
      description: role.description,
      isSystemRole: role.is_system_role,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    };
  }

  async findAll(
    companyId: string,
    page = 1,
    limit = 10,
  ): Promise<{ items: RoleDto[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    // Contar total de papéis
    const countResult = await this.prisma.$queryRaw<[{ count: number }]>`
      SELECT COUNT(*) as count FROM roles WHERE company_id = ${companyId}::uuid
    `;

    const total = Number(countResult[0].count);

    // Buscar papéis paginados
    const rolesResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles 
      WHERE company_id = ${companyId}::uuid 
      ORDER BY name ASC
      LIMIT ${limit} OFFSET ${skip}
    `;

    const roles = rolesResult.map((role) => ({
      id: role.id,
      companyId: role.company_id,
      name: role.name,
      description: role.description,
      isSystemRole: role.is_system_role,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    }));

    return {
      items: roles,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<RoleDto> {
    const roleResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${id}::uuid
    `;

    if (roleResult.length === 0) {
      throw new NotFoundException('Papel não encontrado');
    }

    const role = roleResult[0];

    return {
      id: role.id,
      companyId: role.company_id,
      name: role.name,
      description: role.description,
      isSystemRole: role.is_system_role,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    };
  }

  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleDto> {
    // Verificar se o papel existe
    const existingRole = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${id}::uuid
    `;

    if (existingRole.length === 0) {
      throw new NotFoundException('Papel não encontrado');
    }

    // Verificar se já existe outro papel com o mesmo nome na empresa
    if (updateRoleDto.name) {
      const duplicateRole = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM roles 
        WHERE company_id = ${existingRole[0].company_id}::uuid 
        AND name = ${updateRoleDto.name}
        AND id != ${id}::uuid
      `;

      if (duplicateRole.length > 0) {
        throw new ConflictException(
          'Já existe outro papel com este nome nesta empresa',
        );
      }
    }

    // Atualizar o papel
    await this.prisma.$executeRaw`
      UPDATE roles SET
        name = COALESCE(${updateRoleDto.name}, name),
        description = COALESCE(${updateRoleDto.description}, description),
        is_system_role = COALESCE(${updateRoleDto.isSystemRole}, is_system_role),
        updated_at = NOW()
      WHERE id = ${id}::uuid
    `;

    // Buscar o papel atualizado
    const updatedRoleResult = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${id}::uuid
    `;

    const role = updatedRoleResult[0];

    return {
      id: role.id,
      companyId: role.company_id,
      name: role.name,
      description: role.description,
      isSystemRole: role.is_system_role,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    };
  }

  async remove(id: string): Promise<void> {
    // Verificar se o papel existe
    const existingRole = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles WHERE id = ${id}::uuid
    `;

    if (existingRole.length === 0) {
      throw new NotFoundException('Papel não encontrado');
    }

    // Verificar se o papel está sendo usado
    const usageCount = await this.prisma.$queryRaw<[{ count: number }]>`
      SELECT COUNT(*) as count FROM user_company_roles WHERE role_id = ${id}::uuid
    `;

    if (Number(usageCount[0].count) > 0) {
      throw new ConflictException(
        'Este papel está em uso e não pode ser removido',
      );
    }

    // Remover o papel
    await this.prisma.$executeRaw`
      DELETE FROM roles WHERE id = ${id}::uuid
    `;
  }

  // Método para criar a tabela de papéis se ela não existir
  async ensureRolesTableExists(): Promise<void> {
    await this.prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS public.roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        company_id UUID NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        is_system_role BOOLEAN DEFAULT false,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW(),
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
      )
    `;
  }

  // Método para criar um papel padrão para uma empresa
  async createDefaultRole(companyId: string): Promise<RoleDto> {
    // Verificar se já existe um papel de administrador para esta empresa
    const existingAdminRole = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM roles 
      WHERE company_id = ${companyId}::uuid 
      AND name = 'Administrador'
    `;

    if (existingAdminRole.length > 0) {
      return {
        id: existingAdminRole[0].id,
        companyId: existingAdminRole[0].company_id,
        name: existingAdminRole[0].name,
        description: existingAdminRole[0].description,
        isSystemRole: existingAdminRole[0].is_system_role,
        createdAt: existingAdminRole[0].created_at,
        updatedAt: existingAdminRole[0].updated_at,
      };
    }

    // Criar papel de administrador
    return this.create({
      companyId,
      name: 'Administrador',
      description: 'Acesso completo ao sistema',
      isSystemRole: true,
    });
  }
}
