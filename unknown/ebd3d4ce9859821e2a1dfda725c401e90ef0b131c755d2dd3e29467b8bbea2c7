
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";

interface AccountsReceivableSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  onClearFilters: () => void;
}

export default function AccountsReceivableSearch({
  searchTerm,
  setSearchTerm,
  onClearFilters,
}: AccountsReceivableSearchProps) {
  return (
    <div className="relative min-w-[200px] max-w-[300px]">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input 
        placeholder="Pesquisar transações..." 
        className="pl-10 pr-9 h-9"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      {searchTerm && (
        <button
          className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
          onClick={onClearFilters}
          aria-label="Limpar pesquisa"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
}
