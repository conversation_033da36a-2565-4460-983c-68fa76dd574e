
import { useState, useEffect } from 'react';
import { Layout } from "@/components/layout/Layout";
import { MapPin, Search, Plus, Pencil, Trash, Filter, Refresh<PERSON>w, <PERSON>Left } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { AddressFormModal } from "@/components/address/AddressFormModal";
import { useAddressService } from "@/hooks/useAddressService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNavigate } from "react-router-dom";

const AddressesPage = () => {
  const navigate = useNavigate();
  const [addresses, setAddresses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddressFormOpen, setIsAddressFormOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [filterCompany, setFilterCompany] = useState<string>("all");
  const [companies, setCompanies] = useState<any[]>([]);
  const addressService = useAddressService();

  useEffect(() => {
    loadAddresses();
    loadCompanies();
  }, []);

  const loadAddresses = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      const data = await addressService.getAddresses();
      setAddresses(data);
    } catch (error) {
      console.error("Failed to load addresses:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCompanies = async () => {
    try {
      // Mock data - in a real app, this would be an API call
      setCompanies([
        { id: '1', name: 'Matriz' },
        { id: '2', name: 'Filial SP' },
        { id: '3', name: 'Filial RJ' },
        { id: '4', name: 'Fluxxo' },
      ]);
    } catch (error) {
      console.error("Failed to load companies:", error);
    }
  };

  const handleSearch = () => {
    // Filter addresses locally - in a real app, this might be an API call
    loadAddresses();
  };

  const handleEditAddress = (address: any) => {
    setSelectedAddress(address);
    setIsAddressFormOpen(true);
  };

  const handleDeleteAddress = async (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este endereço?")) {
      try {
        // In a real app, this would be an API call
        await addressService.deleteAddress(id);
        loadAddresses();
      } catch (error) {
        console.error("Failed to delete address:", error);
      }
    }
  };

  const handleSaveAddress = (address: any) => {
    loadAddresses();
    setIsAddressFormOpen(false);
    setSelectedAddress(null);
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const filteredAddresses = addresses.filter(address => {
    let matches = true;
    
    // Apply company filter
    if (filterCompany !== "all") {
      matches = address.company_id === filterCompany;
    }
    
    // Apply search term
    if (searchTerm && matches) {
      const term = searchTerm.toLowerCase();
      matches = address.zip_code.toLowerCase().includes(term) ||
               address.street.toLowerCase().includes(term) ||
               address.city.toLowerCase().includes(term) ||
               address.state.toLowerCase().includes(term);
    }
    
    return matches;
  });

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleGoBack} 
              className="mr-2"
              aria-label="Voltar"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <MapPin className="h-8 w-8 text-primary mr-3" />
            <h1 className="text-3xl font-bold">Gerenciamento de Endereços</h1>
          </div>
          <Button
            onClick={() => {
              setSelectedAddress(null);
              setIsAddressFormOpen(true);
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Novo Endereço
          </Button>
        </div>

        <Card className="mb-6 p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Buscar por CEP, rua, cidade ou estado..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select 
                value={filterCompany} 
                onValueChange={setFilterCompany}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por empresa" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as empresas</SelectItem>
                  {companies.map((company) => (
                    <SelectItem key={company.id} value={company.id}>
                      {company.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                className="flex items-center gap-2"
                onClick={handleSearch}
              >
                <Filter className="h-4 w-4" />
                Filtrar
              </Button>
              <Button 
                variant="ghost" 
                onClick={loadAddresses}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Atualizar
              </Button>
            </div>
          </div>
        </Card>

        <div className="bg-white dark:bg-dark-card rounded-lg border shadow">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>CEP</TableHead>
                <TableHead>Endereço</TableHead>
                <TableHead>Bairro</TableHead>
                <TableHead>Cidade/Estado</TableHead>
                <TableHead>Empresa</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-10">
                    <div className="flex flex-col items-center justify-center">
                      <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                      <p className="mt-2 text-sm text-gray-500">Carregando endereços...</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredAddresses.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-10">
                    <p className="text-gray-500">Nenhum endereço encontrado</p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAddresses.map((address) => {
                  // Find the company for this address
                  const company = companies.find(c => c.id === address.company_id);
                  
                  return (
                    <TableRow key={address.id}>
                      <TableCell>
                        <Badge variant="outline">{address.zip_code}</Badge>
                      </TableCell>
                      <TableCell>
                        {address.street}
                        {address.complement && ` - ${address.complement}`}
                      </TableCell>
                      <TableCell>{address.neighborhood}</TableCell>
                      <TableCell>{address.city}/{address.state}</TableCell>
                      <TableCell>
                        {company ? company.name : "Não vinculado"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => handleEditAddress(address)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => handleDeleteAddress(address.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      
      <AddressFormModal
        open={isAddressFormOpen}
        onOpenChange={setIsAddressFormOpen}
        onSave={handleSaveAddress}
        companyId=""
        address={selectedAddress}
      />
    </Layout>
  );
};

export default AddressesPage;
