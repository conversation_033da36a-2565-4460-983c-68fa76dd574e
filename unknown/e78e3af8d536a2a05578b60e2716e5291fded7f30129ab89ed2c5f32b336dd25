import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { RoleDto } from '../models/role.model';
import { QueryUtil } from '../utils/query.util';
import { mapToRoleDto } from '../utils/mappers.util';
import { RoleRawResult } from '../utils/sql-transforms.util';

@Injectable()
export class RoleQueryService {
  private readonly logger = new Logger(RoleQueryService.name);

  constructor(
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Busca um papel pelo ID
   * @param id ID do papel
   * @returns O papel encontrado
   */
  async findOne(id: string): Promise<RoleDto> {
    try {
      const role = await this.queryUtil.findRoleById(id);
      return mapToRoleDto(role);
    } catch (error) {
      this.logger.error(`Erro ao buscar papel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista todos os papéis de uma empresa, com paginação
   * @param companyId ID da empresa
   * @param page Número da página
   * @param limit Limite de registros por página
   * @returns Lista paginada de papéis
   */
  async findAll(
    companyId: string,
    page = 1,
    limit = 10,
  ): Promise<{ items: RoleDto[]; total: number; page: number; limit: number }> {
    try {
      const skip = (page - 1) * limit;

      // Contar total de papéis
      const countResult = await this.prisma.$queryRaw<[{ count: string }]>`
        SELECT COUNT(*) as count FROM roles WHERE company_id = ${companyId}::uuid
      `;

      const total = Number(countResult[0].count);

      // Buscar papéis paginados
      const rolesResult = await this.prisma.$queryRaw<RoleRawResult[]>`
        SELECT * FROM roles 
        WHERE company_id = ${companyId}::uuid 
        ORDER BY name ASC
        LIMIT ${limit} OFFSET ${skip}
      `;

      const roles = rolesResult.map(mapToRoleDto);

      return {
        items: roles,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Erro ao listar papéis: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista todos os papéis de uma empresa, sem paginação
   * @param companyId ID da empresa
   * @returns Lista de papéis
   */
  async findAllByCompany(companyId: string): Promise<RoleDto[]> {
    try {
      const rolesResult = await this.prisma.$queryRaw<RoleRawResult[]>`
        SELECT * FROM roles 
        WHERE company_id = ${companyId}::uuid 
        ORDER BY name ASC
      `;

      return rolesResult.map(mapToRoleDto);
    } catch (error) {
      this.logger.error(`Erro ao listar papéis da empresa: ${error.message}`, error.stack);
      throw error;
    }
  }
} 