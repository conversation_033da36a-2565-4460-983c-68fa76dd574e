# Documentação da Modelagem de Dados: Contas a Pagar e Contas a Receber

## Visão Geral

Este documento explica a decisão de arquitetura para manter tabelas separadas para Contas a Pagar e Contas a Receber no sistema de controle financeiro, em vez de uma tabela única com um campo discriminador de tipo.

## Estrutura Implementada

O sistema utiliza três tabelas principais para operações financeiras:

1. `accounts_payable` - Registra obrigações financeiras (contas a pagar)
2. `accounts_receivable` - Registra direitos financeiros (contas a receber)
3. `transactions` - Registra movimentações financeiras efetivas nas contas correntes

Esta separação permite que:
- Contas a Pagar/Receber representem eventos financeiros **planejados** ou **previstos**
- Transações representem movimentações financeiras **efetivas**

## Vantagens da Separação em Tabelas Distintas

### 1. Integridade Conceitual

- Contas a pagar e contas a receber representam conceitos de negócio distintos, mesmo tendo atributos semelhantes
- Facilita a compreensão do modelo de dados para novos desenvolvedores
- Mantém o modelo de dados mais próximo da realidade do negócio

### 2. Integridade Referencial Explícita

- As relações na tabela `transactions` ficam mais claras com campos separados (`accounts_payable_id` e `accounts_receivable_id`)
- Evita a necessidade de verificações adicionais para garantir que uma transação não seja vinculada a tipos incorretos
- Minimiza erros de referência entre transações e contas

### 3. Segurança e Permissões

- Permite implementar políticas de segurança diferentes (RLS no Supabase) para cada tipo de conta
- Usuários podem ter permissões específicas para visualizar/editar contas a receber, mas não contas a pagar
- Facilita a implementação de controle de acesso granular

### 4. Evolução do Esquema

- Se no futuro os requisitos mudarem e for necessário adicionar campos específicos para um tipo, não haverá campos nulos no outro tipo
- Maior facilidade para adicionar regras de negócio específicas para cada tipo de conta
- Flexibilidade para evolução independente de cada entidade

### 5. Performance em Consultas Específicas

- Consultas específicas para um tipo de conta não precisam filtrar por tipo
- Índices mais eficientes por terem menos registros em cada tabela
- Possibilidade de otimizações específicas para cada tipo de operação

## Comportamentos Essenciais Garantidos por Este Modelo

Esta separação permite comportamentos essenciais do sistema:

1. **Exclusão de transações**: Ao cancelar um pagamento/recebimento, a simples exclusão da Transação automaticamente retorna a conta associada ao status "em aberto"

2. **Integridade referencial**: Uma Conta a Pagar/Receber com Transações vinculadas não pode ser excluída

3. **Precisão de saldos**: Apenas as Transações afetam os saldos das contas correntes, não os registros de Contas a Pagar/Receber

## Considerações sobre Normalização

Este modelo atende aos princípios de normalização:

- **1ª Forma Normal**: Cada tabela tem um identificador único (chave primária) e não há grupos repetitivos
- **2ª Forma Normal**: Todos os atributos dependem da chave primária completa
- **3ª Forma Normal**: Não há dependências transitivas entre atributos não-chave

## Trade-offs e Desvantagens

Embora esta abordagem cause alguma duplicação na estrutura do banco de dados, as vantagens superam as desvantagens em termos de:

- Clareza conceitual
- Manutenção a longo prazo 
- Flexibilidade para evolução
- Suporte a regras de negócio complexas

## Diagrama de Relacionamento

```
┌─────────────────┐      ┌─────────────────┐
│ accounts_payable│      │accounts_receivable
├─────────────────┤      ├─────────────────┤
│ id              │      │ id              │
│ description     │      │ description     │
│ entity_id       │      │ entity_id       │
│ due_date        │      │ due_date        │
│ amount          │      │ amount          │
│ status          │      │ status          │
│ ...             │      │ ...             │
└────────┬────────┘      └────────┬────────┘
         │                        │
         │                        │
         ▼                        ▼
┌─────────────────────────────────────────┐
│             transactions                │
├─────────────────────────────────────────┤
│ id                                      │
│ description                             │
│ account_id                              │
│ accounts_payable_id                     │
│ accounts_receivable_id                  │
│ ...                                     │
└─────────────────────────────────────────┘
```
