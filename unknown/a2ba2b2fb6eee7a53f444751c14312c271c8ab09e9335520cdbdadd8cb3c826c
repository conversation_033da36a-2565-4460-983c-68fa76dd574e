import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

async function associateUsersWithCompanies() {
  const prisma = new PrismaClient();

  try {
    console.log('Conectando ao banco de dados...');
    await prisma.$connect();

    // Verificar se a tabela de papéis existe
    const rolesTableExists = await prisma.$queryRaw<any[]>`
      SELECT EXISTS (
        SELECT FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename = 'roles'
      ) as exists
    `;

    if (!rolesTableExists[0].exists) {
      console.error(
        'A tabela de papéis não existe. Execute o script setup-roles.ts primeiro.',
      );
      return;
    }

    // Buscar todos os usuários
    console.log('Buscando usuários...');
    const users = await prisma.$queryRaw<any[]>`
      SELECT id, email FROM users WHERE deleted_at IS NULL
    `;

    if (users.length === 0) {
      console.log('Nenhum usuário encontrado para associar às empresas.');
      return;
    }

    console.log(`Encontrados ${users.length} usuários.`);

    // Buscar todas as empresas
    console.log('Buscando empresas...');
    const companies = await prisma.$queryRaw<any[]>`
      SELECT id, name FROM companies WHERE deleted_at IS NULL
    `;

    if (companies.length === 0) {
      console.log('Nenhuma empresa encontrada para associar aos usuários.');
      return;
    }

    console.log(`Encontradas ${companies.length} empresas.`);

    // Para cada empresa, buscar o papel de Administrador
    for (const company of companies) {
      console.log(`Processando empresa: ${company.name} (${company.id})...`);

      const adminRole = await prisma.$queryRaw<any[]>`
        SELECT id FROM roles 
        WHERE company_id = ${company.id}::uuid 
        AND name = 'Administrador'
      `;

      if (adminRole.length === 0) {
        console.log(
          `  - Papel 'Administrador' não encontrado para a empresa ${company.name}. Pulando...`,
        );
        continue;
      }

      const adminRoleId = adminRole[0].id;

      // Verificar associações existentes para esta empresa
      const existingAssociations = await prisma.$queryRaw<any[]>`
        SELECT user_id FROM user_company_roles 
        WHERE company_id = ${company.id}::uuid
      `;

      const existingUserIds = existingAssociations.map(
        (assoc) => assoc.user_id,
      );

      // Associar cada usuário à empresa com o papel de Administrador
      for (const user of users) {
        // Pular se o usuário já estiver associado a esta empresa
        if (existingUserIds.includes(user.id)) {
          console.log(
            `  - Usuário ${user.email} já está associado à empresa ${company.name}.`,
          );
          continue;
        }

        const associationId = uuidv4();

        await prisma.$executeRaw`
          INSERT INTO user_company_roles (
            id, 
            user_id, 
            company_id, 
            role_id, 
            created_at, 
            updated_at,
            deleted_at
          ) VALUES (
            ${associationId}::uuid, 
            ${user.id}::uuid, 
            ${company.id}::uuid, 
            ${adminRoleId}::uuid, 
            NOW(), 
            NOW(),
            NULL
          )
        `;

        console.log(
          `  - Usuário ${user.email} associado à empresa ${company.name} como Administrador.`,
        );
      }
    }

    console.log('Associação de usuários às empresas concluída com sucesso!');
  } catch (error) {
    console.error('Erro ao associar usuários às empresas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a função
associateUsersWithCompanies()
  .then(() => {
    console.log('Script finalizado.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Erro ao executar o script:', error);
    process.exit(1);
  });
