import {
  Controller,
  Get,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ReportsService } from './reports.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { RolesGuard } from '../../guards/roles.guard';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import {
  CashFlowQueryDto,
  CashFlowResponseDto,
  BalanceSheetQueryDto,
  BalanceSheetResponseDto,
  IncomeStatementQueryDto,
  IncomeStatementResponseDto,
} from './dto';

@ApiTags('reports')
@Controller('reports')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Get('cash-flow')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Gerar relatório de fluxo de caixa' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Data de início (ISO 8601)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Data de fim (ISO 8601)' })
  @ApiQuery({ name: 'currencyId', required: false, description: 'ID da moeda (opcional)' })
  @ApiResponse({
    status: 200,
    description: 'Relatório de fluxo de caixa gerado com sucesso',
    type: CashFlowResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async getCashFlow(
    @Req() req: RequestWithUser,
    @Query() query: CashFlowQueryDto,
  ): Promise<CashFlowResponseDto> {
    return this.reportsService.getCashFlow(req.user, query);
  }

  @Get('balance-sheet')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Gerar balanço patrimonial' })
  @ApiQuery({ name: 'date', required: true, description: 'Data de referência (ISO 8601)' })
  @ApiQuery({ name: 'currencyId', required: false, description: 'ID da moeda (opcional)' })
  @ApiResponse({
    status: 200,
    description: 'Balanço patrimonial gerado com sucesso',
    type: BalanceSheetResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async getBalanceSheet(
    @Req() req: RequestWithUser,
    @Query() query: BalanceSheetQueryDto,
  ): Promise<BalanceSheetResponseDto> {
    return this.reportsService.getBalanceSheet(req.user, query);
  }

  @Get('income-statement')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Gerar demonstração de resultados' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Data de início (ISO 8601)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Data de fim (ISO 8601)' })
  @ApiQuery({ name: 'currencyId', required: false, description: 'ID da moeda (opcional)' })
  @ApiResponse({
    status: 200,
    description: 'Demonstração de resultados gerada com sucesso',
    type: IncomeStatementResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  async getIncomeStatement(
    @Req() req: RequestWithUser,
    @Query() query: IncomeStatementQueryDto,
  ): Promise<IncomeStatementResponseDto> {
    return this.reportsService.getIncomeStatement(req.user, query);
  }
}
