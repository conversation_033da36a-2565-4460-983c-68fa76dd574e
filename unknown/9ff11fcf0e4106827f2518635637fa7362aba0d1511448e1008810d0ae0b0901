
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/hooks/use-toast";
import { TransactionFormValues, transactionSchema, RefreshableField } from "../types/TransactionModalTypes";

export function useTransactionModalForm(isOpen: boolean, transaction: any, onClose: () => void) {
  const isEditing = transaction && transaction.id;
  
  // Track which fields are currently being refreshed
  const [refreshingFields, setRefreshingFields] = useState<Record<RefreshableField, boolean>>({
    bankAccountId: false,
    destinationAccountId: false,
    paymentMethod: false,
    recurrence: false
  });
  
  // Setup the form with default values
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      type: transaction?.type || "accounts_receivable",
      description: transaction?.description || "",
      amount: transaction?.amount ? Math.abs(transaction.amount).toString() : "",
      transactionDate: transaction?.transactionDate || new Date(),
      bankAccountId: transaction?.bankAccount?.id || "",
      destinationAccountId: transaction?.destinationAccountId || "",
      relatedId: transaction?.relatedId || "",
      notes: transaction?.notes || "",
      // Initialize new fields
      recurrence: transaction?.recurrence || "none",
      paymentMethod: transaction?.paymentMethod || "",
      invoiceNumber: transaction?.invoiceNumber || "",
    },
  });

  const transactionType = form.watch("type");
  
  // Reset form when transaction changes
  useEffect(() => {
    if (isOpen) {
      form.reset({
        type: transaction?.type || "accounts_receivable",
        description: transaction?.description || "",
        amount: transaction?.amount ? Math.abs(transaction.amount).toString() : "",
        transactionDate: transaction?.transactionDate || new Date(),
        bankAccountId: transaction?.bankAccount?.id || "",
        destinationAccountId: transaction?.destinationAccountId || "",
        relatedId: transaction?.relatedId || "",
        notes: transaction?.notes || "",
        // Reset new fields
        recurrence: transaction?.recurrence || "none",
        paymentMethod: transaction?.paymentMethod || "",
        invoiceNumber: transaction?.invoiceNumber || "",
      });
    }
  }, [isOpen, transaction, form]);

  const onSubmit = (data: TransactionFormValues) => {
    console.info("Submitting transaction data:", data);
    
    // Here you would typically send this data to your API
    toast({
      title: isEditing ? "Transaction updated" : "Transaction created",
      description: `Transaction ${isEditing ? "updated" : "created"} successfully`,
    });
    
    onClose();
  };

  // Function to refresh specific field options
  const refreshFieldOptions = (field: RefreshableField) => {
    // Set only the specific field to refreshing state
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));
    
    // Simulate API call delay
    setTimeout(() => {
      // Reset only the specific field refreshing state
      setRefreshingFields(prev => ({
        ...prev,
        [field]: false
      }));
      
      // Show success toast with the specific field name
      const fieldDisplayNames: Record<RefreshableField, string> = {
        bankAccountId: "contas bancárias",
        destinationAccountId: "contas de destino",
        paymentMethod: "métodos de pagamento",
        recurrence: "tipos de recorrência"
      };
      
      toast({
        title: "Opções atualizadas",
        description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
      });
    }, 800);
  };

  const getFormTitle = () => {
    if (isEditing) return "Edit Transaction";
    
    switch (transactionType) {
      case "accounts_receivable":
        return "New Revenue";
      case "accounts_payable":
        return "New Expense";
      case "transfer":
        return "New Transfer";
      default:
        return "New Transaction";
    }
  };

  return {
    form,
    transactionType,
    isEditing,
    refreshingFields,
    refreshFieldOptions,
    onSubmit,
    getFormTitle
  };
}
