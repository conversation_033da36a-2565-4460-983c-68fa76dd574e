import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TransactionSummaryCards } from './TransactionSummaryCards';
import { TransactionListItem } from '@/types/transaction';

interface TransactionTabContentProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  data: {
    data: TransactionListItem[];
    meta: { totalItems: number };
  } | null;
}

export const TransactionTabContent: React.FC<TransactionTabContentProps> = ({
  activeTab,
  setActiveTab,
  data,
}) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Resumo de Transações</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">Todas</TabsTrigger>
            <TabsTrigger value="income">Receitas</TabsTrigger>
            <TabsTrigger value="expense">Despesas</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            <TransactionSummaryCards data={data} activeTab="all" />
          </TabsContent>
          
          <TabsContent value="income" className="mt-4">
            <TransactionSummaryCards data={data} activeTab="income" />
          </TabsContent>
          
          <TabsContent value="expense" className="mt-4">
            <TransactionSummaryCards data={data} activeTab="expense" />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
