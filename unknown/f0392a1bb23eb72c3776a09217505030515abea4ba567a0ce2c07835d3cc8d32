
import { Button } from "@/components/ui/button";
import { Receipt } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface AccountsPayableRowActionsProps {
  item: any;
  handlePay: (item: any) => void;
  handleView: (item: any) => void;
}

export default function AccountsPayableRowActions({
  item,
  handlePay,
  handleView,
}: AccountsPayableRowActionsProps) {
  const isPaid = item.status === 'paid';
  
  return (
    <div className="flex space-x-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="default" 
              size="sm" 
              className={`h-8 ${isPaid 
                ? 'bg-[#8A898C] dark:bg-[#403E43] hover:bg-[#8A898C] dark:hover:bg-[#403E43] cursor-not-allowed text-white' 
                : 'bg-[#007FFF] hover:bg-[#0066CC]'}`}
              onClick={() => !isPaid && handlePay(item)}
              disabled={isPaid}
            >
              <Receipt className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isPaid ? 'Já baixado' : 'Baixar'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
