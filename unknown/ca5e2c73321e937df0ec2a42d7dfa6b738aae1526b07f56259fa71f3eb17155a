import { useState, useEffect } from 'react';

export function useMonitoringService() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const checkService = () => {
      if (window.monitoringService && window.monitoringServiceInitialized) {
        setIsReady(true);
        return;
      }
      setTimeout(checkService, 50);
    };

    checkService();
  }, []);

  return {
    isReady,
    monitoringService: window.monitoringService
  };
}
