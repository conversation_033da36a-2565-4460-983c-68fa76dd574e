import { Label } from "@/components/ui/label";
import { MoneyDisplay } from "@/components/ui-custom/MoneyInput";

interface TransactionFinalValueDisplayProps {
  finalValue: number;
  isPayment?: boolean;
  description: string;
}

export default function TransactionFinalValueDisplay({
  finalValue,
  isPayment = true,
  description
}: TransactionFinalValueDisplayProps) {
  const textColorClass = isPayment ? 'text-red-500' : 'text-green-500';
  
  return (
    <div className="space-y-1 border-t pt-5">
      <Label className="text-lg font-semibold">Valor Final</Label>
      <div className={`text-xl font-bold ${textColorClass}`}>
        <MoneyDisplay value={Math.abs(finalValue)} className={textColorClass} />
      </div>
      <p className="text-sm text-muted-foreground">
        {description}
      </p>
    </div>
  );
}
