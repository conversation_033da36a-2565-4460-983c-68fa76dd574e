FROM node:20-slim

# Set working directory
WORKDIR /app

# Install nodemon globally
RUN npm install -g nodemon

# Install dependencies for Prisma, bcrypt, and PostgreSQL client
RUN apt-get update && apt-get install -y python3 make g++ openssl procps postgresql-client && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Copy prisma schema
COPY prisma ./prisma/

# Generate Prisma client
RUN npx prisma generate

# Expose application port
EXPOSE 3000

# Copy the entrypoint script
COPY entrypoint.dev.sh /app/entrypoint.dev.sh

# Make it executable
RUN chmod +x /app/entrypoint.dev.sh

# Create scripts directory
RUN mkdir -p /app/dist/scripts

# Copy SQL scripts
COPY src/scripts/*.sql /app/dist/scripts/

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.dev.sh"]

# Default command to start the application (passed to entrypoint)
CMD ["npm", "run", "start:dev"]
