import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { bankService } from '@/services/api';
import { CreateBankRequest, UpdateBankRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar bancos com paginação
export const useBanks = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['banks', { page, limit, search }],
    queryFn: () => bankService.getBanks(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 10 * 60 * 1000, // 10 minutos (dados não mudam com frequência)
  });
};

// Hook para buscar um banco específico por ID
export const useBank = (id: string) => {
  return useQuery({
    queryKey: ['banks', id],
    queryFn: () => bankService.getBankById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para criar um novo banco
export const useCreateBank = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateBankRequest) => bankService.createBank(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['banks'] });
      toast.success('Banco criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar banco. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um banco existente
export const useUpdateBank = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateBankRequest }) => 
      bankService.updateBank(id, data),
    onSuccess: (updatedBank) => {
      queryClient.invalidateQueries({ queryKey: ['banks'] });
      queryClient.setQueryData(['banks', updatedBank.id], updatedBank);
      toast.success('Banco atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar banco. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um banco
export const useDeleteBank = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => bankService.deleteBank(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['banks'] });
      queryClient.removeQueries({ queryKey: ['banks', id] });
      toast.success('Banco excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir banco. Por favor, tente novamente.'
      );
    }
  });
};
