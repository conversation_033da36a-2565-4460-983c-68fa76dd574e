# Especificação Técnica – Sistema de Gestão Financeira Multi-Tenant

## Visão Geral

O sistema é uma solução completa de gestão financeira projetada para pequenas empresas, com foco em empresas de construção e fazendas. Ele oferece suporte multi-tenant, garantindo isolamento de dados por empresa, e funcionalidades como controle de transações, projetos, pagamentos, orçamentos, relatórios, notificações e saldo bancário, com uma arquitetura segura, escalável e stateless.

- **Objetivo**: Proporcionar uma visão integrada e em tempo real da saúde financeira, com precisão, integridade e segurança.
- **Usuários-Alvo**: Pequenas empresas de construção e fazendas.

---

## Requisitos Funcionais

1. **Suporte Multi-Tenant**: Isolamento de dados por empresa via Row-Level Security (RLS) no PostgreSQL.
2. **Transações Financeiras**: Entradas, saídas e transferências, vinculadas a contas bancárias.
3. **Gestão de Projetos**: Rastreamento específico para construção, com orçamentos e custos.
4. **Pagamentos**: Únicos (imediato, futuro ou atrasado), parcelados ou parciais, com opção de marcar como pagos/recebidos.
5. **Orçamentos e Previsões**: Por empresa e projeto, com comparações planejado vs. realizado.
6. **Relatórios**: Fluxo de caixa, despesas por projeto/empresa/conta bancária, contas a pagar/receber, despesas x receitas.
7. **Notificações**: E-mails e in-app em tempo real para eventos como vencimentos ou pagamentos.
8. **Entidades**: Clientes e fornecedores com endereços detalhados.
9. **Controle de Acesso**: RBAC personalizável por tenant, com permissões granulares.
10. **Contas Bancárias**: Cadastro e controle de saldo por conta, associadas a bancos globais (FEBRABAN).
11. **Categorias**: Hierárquicas (pai/filha), específicas por tipo de transação (pagar/receber).

---

## Arquitetura

- **Tipo**: Arquitetura stateless baseada em contêineres Docker, com separação de frontend e backend via APIs RESTful.
- **Princípios**: SOLID (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion).

### Stack Tecnológica

- **Frontend**: React com Next.js, TypeScript, Tailwind CSS (shadcn/ui).
- **Backend**: Node.js com NestJS, TypeScript.
- **Banco de Dados**: PostgreSQL com Prisma ORM.
- **Infraestrutura**: Docker, AWS (ECS/RDS) ou VPS privada.
- **CI/CD**: GitHub Actions.
- **Monitoramento**: Prometheus e Grafana.
- **Documentação**: Swagger (OpenAPI).
- **APIs Externas**: ViaCEP (ou similar) para consulta de CEPs.

---

## Modelo de Dados (DBML)

```dbml
// Projeto: Sistema de Gestão Financeira Multi-Tenant
Project "FinanceApp" {
  database_type: "PostgreSQL"
  Note: "Sistema multi-tenant para gestão financeira."
}

Table "Tenants" {
  id uuid [pk, default: `gen_random_uuid()`]
  name varchar(255) [not null, unique]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Users" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  email varchar(255) [not null, unique]
  password varchar(255) [not null, note: "Hash bcrypt"]
  role_id uuid [ref: > "Roles".id]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Projects" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  name varchar(255) [not null]
  budget double
  start_date timestamp
  end_date timestamp
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Entities" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  type varchar(50) [not null, note: "customer, supplier"]
  name varchar(255) [not null]
  address_id uuid [ref: > "Addresses".id]
  phone varchar(20)
  contact varchar(100)
  email varchar(255)
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Addresses" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  cep varchar(9) [note: "Ex.: '12345-678' ou 'SEM CEP'"]
  logradouro varchar(255) [not null]
  numero varchar(20) [not null]
  complemento varchar(100)
  bairro varchar(100) [not null]
  cidade varchar(100) [not null]
  estado varchar(2) [not null]
  pais varchar(50) [not null, default: `'Brasil'`]
  origem_dado varchar(10) [not null, default: `'manual'`, note: "manual, auto"]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Bills" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  type varchar(50) [not null, note: "receivable, payable"]
  amount double [not null]
  paid_amount double [not null, default: 0]
  due_date timestamp [not null]
  status varchar(50) [not null, note: "pending, partial, paid, overdue"]
  description text
  payment_method varchar(50)
  category_id uuid [ref: > "Categories".id]
  subcategory_id uuid [ref: > "Categories".id]
  project_id uuid [ref: > "Projects".id]
  entity_id uuid [ref: > "Entities".id]
  bank_account_id uuid [ref: > "BankAccounts".id]
  recurrence varchar(50)
  installments integer
  parent_bill_id uuid [ref: > "Bills".id]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
  Note: "Contas a pagar/receber (planejamento e parcelamentos)."
}

Table "Transactions" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  bill_id uuid [not null, ref: > "Bills".id]
  amount double [not null]
  transaction_date timestamp [not null]
  bank_account_id uuid [ref: > "BankAccounts".id]
  description text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
  Note: "Registros efetivos de pagamentos/recebimentos."
}

Table "Categories" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  name varchar(100) [not null]
  transaction_type varchar(50) [not null, note: "receivable, payable"]
  parent_category_id uuid [ref: > "Categories".id]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Roles" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  name varchar(100) [not null]
  description text
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Permissions" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  role_id uuid [not null, ref: > "Roles".id]
  action varchar(100) [not null]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Notifications" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  user_id uuid [ref: > "Users".id]
  title varchar(255) [not null]
  message text [not null]
  type varchar(50) [not null, note: "email, in-app, both"]
  status varchar(50) [default: `'unread'`, note: "unread, read, sent"]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Banks" {
  id uuid [pk, default: `gen_random_uuid()`]
  code varchar(10) [not null, unique, note: "Código do banco (ex.: FEBRABAN '001' ou internacional)"]
  name varchar(100) [not null]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "BankAccounts" {
  id uuid [pk, default: `gen_random_uuid()`]
  tenant_id uuid [not null, ref: > "Tenants".id]
  bank_id uuid [not null, ref: > "Banks".id]
  account_number varchar(20) [not null]
  balance double [not null, default: 0]
  balance_date timestamp [not null]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

Table "Ceps" {
  cep varchar(9) [pk]
  tenant_id uuid [not null, ref: > "Tenants".id]
  logradouro varchar(255) [not null]
  bairro varchar(100) [not null]
  cidade varchar(100) [not null]
  estado varchar(2) [not null]
  origem_registro varchar(10) [not null, default: `'auto'`]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]
}

// Relacionamentos
Ref: "Users".tenant_id > "Tenants".id [delete: cascade]
Ref: "Users".role_id > "Roles".id [delete: set null]
Ref: "Projects".tenant_id > "Tenants".id [delete: cascade]
Ref: "Entities".tenant_id > "Tenants".id [delete: cascade]
Ref: "Entities".address_id > "Addresses".id [delete: set null]
Ref: "Addresses".tenant_id > "Tenants".id [delete: cascade]
Ref: "Bills".tenant_id > "Tenants".id [delete: cascade]
Ref: "Bills".category_id > "Categories".id [delete: set null]
Ref: "Bills".subcategory_id > "Categories".id [delete: set null]
Ref: "Bills".project_id > "Projects".id [delete: set null]
Ref: "Bills".entity_id > "Entities".id [delete: set null]
Ref: "Bills".bank_account_id > "BankAccounts".id [delete: set null]
Ref: "Bills".parent_bill_id > "Bills".id [delete: cascade]
Ref: "Transactions".tenant_id > "Tenants".id [delete: cascade]
Ref: "Transactions".bill_id > "Bills".id [delete: cascade]
Ref: "Transactions".bank_account_id > "BankAccounts".id [delete: set null]
Ref: "Categories".tenant_id > "Tenants".id [delete: cascade]
Ref: "Categories".parent_category_id > "Categories".id [delete: set null]
Ref: "Roles".tenant_id > "Tenants".id [delete: cascade]
Ref: "Permissions".tenant_id > "Tenants".id [delete: cascade]
Ref: "Permissions".role_id > "Roles".id [delete: cascade]
Ref: "Notifications".tenant_id > "Tenants".id [delete: cascade]
Ref: "Notifications".user_id > "Users".id [delete: set null]
Ref: "BankAccounts".tenant_id > "Tenants".id [delete: cascade]
Ref: "BankAccounts".bank_id > "Banks".id [delete: restrict]
Ref: "Ceps".tenant_id > "Tenants".id [delete: cascade]
```

### **Script DDL para PostgreSQL**

```sql
-- Extensão para UUID e criptografia
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Tabela Tenants
CREATE TABLE "Tenants" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabela Users
CREATE TABLE "Users" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role_id UUID,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES "Roles"(id) ON DELETE SET NULL
);

-- Tabela Projects
CREATE TABLE "Projects" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    budget DOUBLE PRECISION,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE
);

-- Tabela Entities
CREATE TABLE "Entities" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('customer', 'supplier')),
    name VARCHAR(255) NOT NULL,
    address_id UUID,
    phone VARCHAR(20),
    contact VARCHAR(100),
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (address_id) REFERENCES "Addresses"(id) ON DELETE SET NULL
);

-- Tabela Addresses
CREATE TABLE "Addresses" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    cep VARCHAR(9),
    logradouro VARCHAR(255) NOT NULL,
    numero VARCHAR(20) NOT NULL,
    complemento VARCHAR(100),
    bairro VARCHAR(100) NOT NULL,
    cidade VARCHAR(100) NOT NULL,
    estado VARCHAR(2) NOT NULL,
    pais VARCHAR(50) NOT NULL DEFAULT 'Brasil',
    origem_dado VARCHAR(10) NOT NULL DEFAULT 'manual' CHECK (origem_dado IN ('manual', 'auto')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE
);

-- Tabela Bills
CREATE TABLE "Bills" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('receivable', 'payable')),
    amount DOUBLE PRECISION NOT NULL,
    paid_amount DOUBLE PRECISION NOT NULL DEFAULT 0,
    due_date TIMESTAMP NOT NULL,
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'partial', 'paid', 'overdue')),
    description TEXT,
    payment_method VARCHAR(50),
    category_id UUID,
    subcategory_id UUID,
    project_id UUID,
    entity_id UUID,
    bank_account_id UUID,
    recurrence VARCHAR(50),
    installments INTEGER,
    parent_bill_id UUID,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES "Categories"(id) ON DELETE SET NULL,
    FOREIGN KEY (subcategory_id) REFERENCES "Categories"(id) ON DELETE SET NULL,
    FOREIGN KEY (project_id) REFERENCES "Projects"(id) ON DELETE SET NULL,
    FOREIGN KEY (entity_id) REFERENCES "Entities"(id) ON DELETE SET NULL,
    FOREIGN KEY (bank_account_id) REFERENCES "BankAccounts"(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_bill_id) REFERENCES "Bills"(id) ON DELETE CASCADE
);

-- Tabela Transactions
CREATE TABLE "Transactions" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    bill_id UUID NOT NULL,
    amount DOUBLE PRECISION NOT NULL,
    transaction_date TIMESTAMP NOT NULL,
    bank_account_id UUID,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (bill_id) REFERENCES "Bills"(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES "BankAccounts"(id) ON DELETE SET NULL
);

-- Tabela Categories
CREATE TABLE "Categories" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('receivable', 'payable')),
    parent_category_id UUID,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_category_id) REFERENCES "Categories"(id) ON DELETE SET NULL
);

-- Tabela Roles
CREATE TABLE "Roles" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE
);

-- Tabela Permissions
CREATE TABLE "Permissions" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    role_id UUID NOT NULL,
    action VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES "Roles"(id) ON DELETE CASCADE
);

-- Tabela Notifications
CREATE TABLE "Notifications" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    user_id UUID,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('email', 'in-app', 'both')),
    status VARCHAR(50) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'sent')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES "Users"(id) ON DELETE SET NULL
);

-- Tabela Banks (Global)
CREATE TABLE "Banks" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabela BankAccounts
CREATE TABLE "BankAccounts" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    bank_id UUID NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    balance DOUBLE PRECISION NOT NULL DEFAULT 0,
    balance_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_id) REFERENCES "Banks"(id) ON DELETE RESTRICT
);

-- Tabela Ceps
CREATE TABLE "Ceps" (
    cep VARCHAR(9) PRIMARY KEY,
    tenant_id UUID NOT NULL,
    logradouro VARCHAR(255) NOT NULL,
    bairro VARCHAR(100) NOT NULL,
    cidade VARCHAR(100) NOT NULL,
    estado VARCHAR(2) NOT NULL,
    origem_registro VARCHAR(10) NOT NULL DEFAULT 'auto' CHECK (origem_registro IN ('manual', 'auto')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES "Tenants"(id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX idx_bills_tenant_due_date ON "Bills" (tenant_id, due_date);
CREATE INDEX idx_bills_project ON "Bills" (project_id);
CREATE INDEX idx_users_tenant ON "Users" (tenant_id);
CREATE INDEX idx_roles_tenant ON "Roles" (tenant_id);
CREATE INDEX idx_transactions_bill ON "Transactions" (bill_id);
```

---

## API RESTful

- **Base URL**: `/api/v1/`.
- **Endpoints Exemplo**:
  - `POST /api/v1/auth/login`: Autentica usuário.
  - `POST /api/v1/payments`: Cria conta a pagar/receber.
  - `GET /api/v1/reports/cashFlow`: Gera relatório de fluxo de caixa.
  - `POST /api/v1/bank-accounts`: Cadastra conta bancária.

### Autenticação
- **Método**: JWT (JSON Web Token).
- **Configuração**: Tokens assinados com `JWT_SECRET`, expiração de 1 hora.
- **Fluxo**: Login retorna token no header `Authorization: Bearer <token>`.

### Autorização
- **RBAC**: Papéis personalizáveis por tenant com permissões granulares (ex.: `create:transaction`).
- **Implementação**: Guarda `RbacGuard` verifica permissões.

### Rate Limiting
- **Configuração**: 100 requisições por IP por minuto (global), ajustável por endpoint.
- **Ferramenta**: `@nestjs/throttler` + ALB/Nginx.

---

## Fluxos Principais

### **Diagrama de Cadastro de Conta a Pagar/Receber**

Este diagrama representa o cadastro de uma conta a pagar única, com pagamento imediato, vinculada a uma conta bancária.

```mermaid
sequenceDiagram
    participant U as Usuário
    participant F as Frontend (Next.js)
    participant B as Backend (NestJS)
    participant DB as Banco de Dados (PostgreSQL)
    participant N as Notificações

    U->>F: Preenche formulário para conta única
    Note over F,B: Dados: { tenant_id: "T1", type: "payable", amount: 1000, due_date: "2025-02-21", description: "Compra", bank_account_id: "BA1", paid: true }
    F->>B: POST /api/v1/bills
    
    B->>DB: Insere "Bills" (id: B1, status: "paid")
    alt Conta marcada como paga
        B->>DB: Insere "Transactions" (id: T1, bill_id: "B1", type: "expense", amount: 1000)
        B->>DB: Atualiza "BankAccounts" (id: "BA1", balance: balance - 1000)
        B->>N: Notifica: "Conta a pagar de 1000 BRL paga"
    end
    DB-->>B: Confirma inserções e atualização
    N-->>U: Notificação in-app e e-mail
    B-->>F: Retorna sucesso: { message: "Conta registrada e paga" }
    F-->>U: Exibe confirmação: "Conta de 1000 BRL paga"
```

### **Diagrama de Cadastro de Endereço**

Este diagrama representa o fluxo de cadastro de um endereço com consulta de CEP e fallback manual.

```mermaid
sequenceDiagram
    participant U as Usuário
    participant F as Frontend (Next.js)
    participant B as Backend (NestJS)
    participant DB as Banco de Dados (PostgreSQL)
    participant A as API Externa (ViaCEP)

    U->>F: Insere CEP: "12345-678"
    Note over F,B: Requisição: { tenant_id: "T1", cep: "12345-678" }
    F->>B: POST /api/v1/addresses/cep
    B->>DB: Consulta "Ceps" WHERE tenant_id = 'T1'
    alt CEP encontrado
        DB-->>B: Dados locais
        B-->>F: Retorna dados do CEP
        F-->>U: Preenche formulário automaticamente
    else CEP não encontrado
        B->>A: Consulta ViaCEP
        alt Dados retornados
            A-->>B: Dados do CEP
            B->>DB: Insere em "Ceps" (origem: "auto")
            B-->>F: Retorna dados do CEP
            F-->>U: Preenche formulário
        else Falha na API
            A-->>B: Erro ou sem dados
            B-->>F: Retorna erro
            F-->>U: Alerta: "Preencha manualmente"
            U->>F: Preenche manualmente
        end
    end
    
    U->>F: Confirma cadastro da entidade
    Note over F,B: Dados: { tenant_id: "T1", type: "supplier", name: "Fornecedor XYZ", address: { cep: "12345-678", logradouro: "Rua Teste", numero: "123", bairro: "Vila Nova", cidade: "Campinas", estado: "SP", origem_dado: "manual" } }
    F->>B: POST /api/v1/entities
    B->>DB: Insere "Address" (id: A1) e "Entity" (id: E1)
    DB-->>B: Confirma inserção
    B-->>F: Retorna sucesso
    F-->>U: Exibe confirmação: "Entidade cadastrada"
```

### **Diagrama de Inclusão de Conta a Pagar Parcelada**

Este diagrama ilustra o processo de inclusão de uma conta a pagar parcelada (ex.: 9.000 reais em 5 parcelas de 1.800 reais), com a opção de pagamento imediato da primeira parcela.

```mermaid
sequenceDiagram
    participant U as Usuário
    participant F as Frontend (Next.js)
    participant B as Backend (NestJS)
    participant DB as Banco de Dados (PostgreSQL)
    participant N as Notificações

    U->>F: Preenche formulário para conta parcelada
    Note over F,B: Dados: { tenant_id: "T1", type: "payable", amount: 9000, due_date: "2025-03-01", installments: 5, bank_account_id: "BA1", description: "Compra de equipamentos", pay_first_installment: true }
    F->>B: POST /api/v1/bills
    
    B->>DB: Insere "Bills" (Pai) (id: B1, status: "pending")
    B->>B: Calcula parcelas: 9000 / 5 = 1800 BRL por parcela
    loop Para cada parcela (1 a 5)
        B->>DB: Insere "Bills" (Filho) (id: B1-n, amount: 1800, status: n=1 && pay_first_installment ? "paid" : "pending", parent_bill_id: "B1")
    end
    
    alt Primeira parcela paga
        B->>DB: Insere "Transactions" (id: T1, bill_id: "B1-1", amount: 1800)
        B->>DB: Atualiza "BankAccounts" (id: "BA1", balance: balance - 1800)
        B->>N: Notifica: "Parcela 1 de 1800 BRL paga"
    end
    
    DB-->>B: Confirma inserções e atualizações
    B->>N: Notifica: "Conta parcelada de 9000 BRL registrada"
    N-->>U: Notificação in-app e e-mail
    B-->>F: Retorna sucesso: { message: "Conta parcelada registrada" }
    F-->>U: Exibe confirmação: "Conta registrada em 5 parcelas"

    Note over DB: Parcelas: B1-1 (paid), B1-2 a B1-5 (pending)
```

### **Diagrama de Inclusão de Conta a Pagar Parcelada considerando o pagamento parcial de uma parcela**
```mermaid
sequenceDiagram
    participant U as Usuário
    participant F as Frontend (Next.js)
    participant B as Backend (NestJS)
    participant DB as Banco de Dados (PostgreSQL)
    participant N as Notificações

    Note over U,F: Parcela inicial: 1800 BRL (id: B1-1, tenant_id: T1, due_date: 2025-03-01, paid_amount: 0, bank_account_id: BA1)

    U->>F: Solicita pagar 1300 BRL da parcela B1-1
    Note over F,B: Requisição: { partial_amount: 1300, bank_account_id: "BA1" }
    F->>B: PUT /api/v1/bills/B1-1
    B->>DB: Consulta "Bills" WHERE id = 'B1-1' (verifica paid_amount atual: 0)
    B->>DB: Atualiza "Bills" (id: B1-1, paid_amount: 0 + 1300 = 1300, status: "partial")
    B->>DB: Insere "Transactions" (id: T1, tenant_id: "T1", bill_id: "B1-1", amount: 1300, transaction_date: NOW(), bank_account_id: "BA1", description: "Pagamento parcial parcela B1-1")
    B->>DB: Atualiza "BankAccounts" (id: "BA1", balance: balance - 1300, balance_date: NOW())
    DB-->>B: Confirma atualização e inserção
    B->>N: Envia notificação: "Pagamento parcial de 1300 BRL registrado, 500 BRL pendentes"
    N-->>U: Notificação in-app e e-mail: "Parcela B1-1 parcialmente paga, saldo restante: 500 BRL"
    B-->>F: Retorna sucesso: { message: "Pagamento parcial registrado", paid_amount: 1300 }
    F-->>U: Exibe confirmação: "1300 BRL pagos, 500 BRL pendentes"

    Note over B,DB: Parcela B1-1: amount: 1800, paid_amount: 1300, status: partial

    U->>F: Solicita pagar os 500 BRL restantes da parcela B1-1
    Note over F,B: Requisição: { partial_amount: 500, bank_account_id: "BA1" }
    F->>B: PUT /api/v1/bills/B1-1
    B->>DB: Consulta "Bills" WHERE id = 'B1-1' (verifica paid_amount atual: 1300)
    B->>B: Calcula novo paid_amount: 1300 + 500 = 1800
    B->>DB: Atualiza "Bills" (id: B1-1, paid_amount: 1800, status: "paid")
    B->>DB: Insere "Transactions" (id: T2, tenant_id: "T1", bill_id: "B1-1", amount: 500, transaction_date: NOW(), bank_account_id: "BA1", description: "Pagamento final parcela B1-1")
    B->>DB: Atualiza "BankAccounts" (id: "BA1", balance: balance - 500, balance_date: NOW())
    DB-->>B: Confirma atualização e inserção
    B->>N: Envia notificação: "Parcela B1-1 de 1800 BRL totalmente paga"
    N-->>U: Notificação in-app e e-mail: "Parcela B1-1 quitada"
    B-->>F: Retorna sucesso: { message: "Parcela B1-1 totalmente paga" }
    F-->>U: Exibe confirmação: "Parcela de 1800 BRL quitada"

    Note over B,DB: Verifica status do "pai" (B1)
    alt Todas as parcelas pagas (B1-1 a B1-5)
        B->>DB: Consulta COUNT(*) FROM "Bills" WHERE parent_bill_id = 'B1' AND paid_amount < amount
        DB-->>B: Retorna 0 (todas pagas)
        B->>DB: Atualiza "Bills" (id: B1, status: "paid")
        B->>N: Envia notificação: "Conta a pagar de 9000 BRL totalmente paga"
        N-->>U: Notificação in-app e e-mail: "Pagamento concluído"
    end
```


---

## Segurança

- **Autenticação**: JWT, stateless.
- **Criptografia em Repouso**: `pgcrypto` para campos sensíveis (ex.: `contact_info` em `Address`).
- **HTTPS**: Configurado via ALB (AWS) ou Nginx (VPS).
- **RLS**: Isolamento por `tenant_id` em todas as tabelas exceto `Bank`.

---

## Infraestrutura

- **Contêineres**: Docker com frontend, backend, PostgreSQL, Prometheus e Grafana.
- **Deploy**:
  1. **AWS**: ECS/Fargate, RDS, ECR, ALB.
  2. **VPS**: Servidor único com Nginx e Docker Compose.
- **Docker Compose (Produção)**:
  ```yaml
  version: '3.8'
  services:
    frontend: { build: ./frontend, ports: ["3000:3000"] }
    backend: { build: ./backend, ports: ["4000:4000"] }
    db: { image: postgres:15, ports: ["5432:5432"] }
    prometheus: { image: prom/prometheus:latest, ports: ["9090:9090"] }
    grafana: { image: grafana/grafana:latest, ports: ["3001:3000"] }
  volumes:
    db-data:
  ```

---

## CI/CD

- **Ferramenta**: GitHub Actions.
- **Pipeline**:
  ```yaml
  name: Deploy
  on: push: { branches: ["main"] }
  jobs:
    test:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v3
        - run: npm test --prefix backend
        - run: npm test --prefix frontend
        - run: npm run test:e2e
    deploy:
      needs: test
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v3
        - if: env.DEPLOY_TARGET == 'aws'
          run: |
            aws ecr get-login-password | docker login --username AWS --password-stdin ${{ secrets.ECR_REGISTRY }}
            docker build -t ${{ secrets.ECR_REGISTRY }}/frontend:latest frontend
            docker push ${{ secrets.ECR_REGISTRY }}/frontend:latest
            aws ecs update-service --cluster finance-cluster --service frontend-service --force-new-deployment
        - if: env.DEPLOY_TARGET == 'vps'
          run: ssh ${VPS_USER}@${VPS_HOST} "cd /app && git pull && docker-compose up -d --build"
  ```

---

## Testes

- **Unitários**: Jest (backend e frontend).
- **Integração**: Jest (APIs com Prisma).
- **E2E**: Playwright (fluxos críticos).
- **Cobertura**: 80%+ unitários, 100% integração crítica.

---

## Monitoramento

- **Ferramentas**: Prometheus e Grafana.
- **Métricas**:
  - Erros: `finance_app_errors_total`.
  - Desempenho: `http_request_duration_seconds`.
  - APM: `finance_transactions_created_total`.
  - HTTP: `prometheus_http_requests_total`.
  - Banco de Dados: `pg_stat_database_queries`.

---

## Documentação

- **Ferramenta**: Swagger (OpenAPI).
- **Estrutura**: Tags como "Autenticação", "Payments", "BankAccounts".
- **Acesso**: `/api/v1/api-docs`.

---

## Considerações Finais

- **Escalabilidade**: Stateless, suporta múltiplas instâncias.
- **Segurança**: JWT, RLS (exceto `Bank`), criptografia.
- **Flexibilidade**: Pagamentos únicos/parcelados/parciais, categorias hierárquicas, controle de saldo bancário.
- **Manutenção**: Versionamento e CI/CD facilitam atualizações.

---