#!/bin/sh

# G<PERSON><PERSON>r que o diretório node_modules exista
if [ ! -d "node_modules" ] || [ -z "$(ls -A node_modules)" ]; then
  echo "Instalando dependências..."
  npm install --legacy-peer-deps
else
  echo "Verificando dependências..."
  # Verifica se package.json foi modificado e reinstala se necessário
  if [ package.json -nt node_modules/.package-lock.json ]; then
    echo "Atualizando dependências..."
    npm install --legacy-peer-deps
  fi
fi

# Instala date-fns e react-day-picker se não estiverem presentes
if [ ! -d "node_modules/date-fns" ] || [ ! -d "node_modules/react-day-picker" ]; then
  echo "Instalando date-fns e react-day-picker..."
  npm install date-fns react-day-picker --legacy-peer-deps
fi

# Inicia o servidor de desenvolvimento
echo "Iniciando servidor de desenvolvimento..."
echo "API URL: $VITE_API_URL"
exec npm run dev -- --host 0.0.0.0 --port 3001
