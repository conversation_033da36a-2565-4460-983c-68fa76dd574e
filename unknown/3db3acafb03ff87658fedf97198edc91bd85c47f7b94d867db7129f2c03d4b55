declare const tokenStorage: {
  /**
   * Salva os tokens de autenticação no localStorage
   */
  setTokens: (tokens: { accessToken: string; refreshToken: string }) => void;
  
  /**
   * Recupera o token de acesso do localStorage
   */
  getAccessToken: () => string | null;
  
  /**
   * Recupera o token de atualização do localStorage
   */
  getRefreshToken: () => string | null;
  
  /**
   * Limpa os tokens de autenticação do localStorage
   */
  clearTokens: () => void;
  
  /**
   * Verifica se existem tokens armazenados
   */
  hasTokens: () => boolean;
};

export default tokenStorage;
