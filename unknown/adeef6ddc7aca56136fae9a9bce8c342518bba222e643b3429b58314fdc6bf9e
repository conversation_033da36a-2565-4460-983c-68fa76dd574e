import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsArray } from 'class-validator';

export class RolePermissionDto {
  @ApiProperty({
    description: 'ID do papel',
    example: '123e4567-e89b-12d3-a456-************',
  })
  roleId: string;

  @ApiProperty({
    description: 'ID da permissão',
    example: '123e4567-e89b-12d3-a456-************',
  })
  permissionId: string;
}

export class AssignPermissionsDto {
  @ApiProperty({
    description: 'IDs das permissões a serem atribuídas ao papel',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  permissionIds: string[];
}

export class RemovePermissionsDto {
  @ApiProperty({
    description: 'IDs das permissões a serem removidas do papel',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  permissionIds: string[];
}
