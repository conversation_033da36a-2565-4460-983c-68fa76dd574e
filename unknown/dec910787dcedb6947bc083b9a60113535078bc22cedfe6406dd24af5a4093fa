// backend/src/routes/accounts-receivable/accounts-receivable.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateAccountReceivableDto } from './dto/create-account-receivable.dto';
import { UpdateAccountReceivableDto } from './dto/update-account-receivable.dto';
import { Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

// Define status constants (optional but good practice)
enum AccountReceivableStatus {
  PENDING = 'pending',
  RECEIVED = 'received',
  PARTIALLY_RECEIVED = 'partially_received',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
}

// Define a type for AccountsReceivable until Prisma Client is regenerated
type AccountsReceivable = {
  id: string;
  companyId: string;
  description: string;
  entityId: string;
  dueDate: Date;
  amount: Prisma.Decimal;
  receivedAmount: Prisma.Decimal;
  currencyId: string;
  status: string;
  categoryId?: string | null;
  projectId?: string | null;
  paymentMethodId?: string | null;
  bankAccountId?: string | null;
  recurrenceTypeId?: string | null;
  invoiceNumber?: string | null;
  notes?: string | null;
  interestAmount?: Prisma.Decimal | null;
  discountAmount?: Prisma.Decimal | null;
  installments?: number | null;
  installmentNumber?: number | null;
  parentId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
};

// Define a type for the authenticated user payload
interface AuthenticatedUser {
  id: string;
  companyId: string;
}

@Injectable()
export class AccountsReceivableService {
  constructor(private prisma: PrismaService) {}

  /**
   * Create a new account receivable
   */
  async create(createDto: CreateAccountReceivableDto, user: AuthenticatedUser): Promise<AccountsReceivable> {
    const { companyId } = user;

    // 1. Validate related entities
    // Verify entity exists and belongs to the company
    const entity = await this.prisma.entity.findFirst({
      where: {
        id: createDto.entityId,
        companyId,
        deletedAt: null
      },
    });

    if (!entity) {
      throw new BadRequestException('Entity not found or does not belong to your company.');
    }

    // Verify currency exists
    const currency = await this.prisma.currency.findUnique({
      where: { id: createDto.currencyId },
    });

    if (!currency) {
      throw new BadRequestException('Currency not found.');
    }

    // 2. Prepare data
    const data = {
      description: createDto.description,
      dueDate: new Date(createDto.dueDate), // Convert string date to Date object
      amount: new Decimal(createDto.amount), // Convert number to Decimal
      receivedAmount: new Decimal(0), // Initial received amount is 0
      status: AccountReceivableStatus.PENDING, // Initial status
      invoiceNumber: createDto.invoiceNumber,
      notes: createDto.notes,
      installments: createDto.installments,
      installmentNumber: createDto.installmentNumber,
      interestAmount: createDto.interestAmount ? new Decimal(createDto.interestAmount) : undefined,
      discountAmount: createDto.discountAmount ? new Decimal(createDto.discountAmount) : undefined,

      // Relações obrigatórias usando connect
      company: { connect: { id: companyId } },
      entity: { connect: { id: createDto.entityId } },
      currency: { connect: { id: createDto.currencyId } },

      // Relações opcionais usando connect
      ...(createDto.categoryId && { category: { connect: { id: createDto.categoryId } } }),
      ...(createDto.projectId && { project: { connect: { id: createDto.projectId } } }),
      ...(createDto.paymentMethodId && { paymentMethod: { connect: { id: createDto.paymentMethodId } } }),
      ...(createDto.bankAccountId && { bankAccount: { connect: { id: createDto.bankAccountId } } }),
      ...(createDto.recurrenceTypeId && { recurrenceType: { connect: { id: createDto.recurrenceTypeId } } }),
      ...(createDto.parentId && { parent: { connect: { id: createDto.parentId } } }),
    };

    // 3. Create the account receivable
    try {
      console.log('Creating account receivable with data:', JSON.stringify(data, null, 2));
      const result = await this.prisma['accountsReceivable'].create({ data });
      console.log('Account receivable created successfully:', result.id);
      return result;
    } catch (error) {
      console.error('Error creating account receivable:', error);
      throw new InternalServerErrorException(`Could not create account receivable: ${error.message}`);
    }
  }

  /**
   * Find all accounts receivable with filtering and pagination
   */
  async findAll(
    user: AuthenticatedUser,
    { page = 1, limit = 10, status, dueDateFrom, dueDateTo, entityId }: {
        page?: number;
        limit?: number;
        status?: string;
        dueDateFrom?: string;
        dueDateTo?: string;
        entityId?: string;
    } = {}
  ): Promise<{ data: AccountsReceivable[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
    const skip = (page - 1) * limit;

    // Build where clause for filtering
    const where = {
      companyId,
      deletedAt: null,
      // Add filters if provided
      ...(status && { status }),
      ...(entityId && { entityId }),
      ...(dueDateFrom || dueDateTo
        ? {
            dueDate: {
              ...(dueDateFrom && { gte: new Date(dueDateFrom) }),
              ...(dueDateTo && { lte: new Date(dueDateTo) }),
            },
          }
        : {}),
    };

    try {
      const [data, total] = await this.prisma.$transaction([
        this.prisma['accountsReceivable'].findMany({
          where,
          skip,
          take: limit,
          orderBy: { dueDate: 'asc' }, // Default sort order
          // Include relations if needed for the list view
          // include: { entity: true, category: true }
        }),
        this.prisma['accountsReceivable'].count({ where }),
      ]);
      return { data, total, page, limit };
    } catch (error) {
      console.error('Error finding accounts receivable:', error);
      throw new InternalServerErrorException('Could not retrieve accounts receivable.');
    }
  }

  /**
   * Find one account receivable by ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<AccountsReceivable> {
    const { companyId } = user;
    try {
      const account = await this.prisma['accountsReceivable'].findFirst({
        where: { id, companyId, deletedAt: null },
      });

      if (!account) {
        throw new NotFoundException(`Account Receivable with ID ${id} not found.`);
      }
      return account;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding account receivable with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not retrieve account receivable.');
    }
  }

  /**
   * Update an account receivable
   */
  async update(id: string, updateDto: UpdateAccountReceivableDto, user: AuthenticatedUser): Promise<AccountsReceivable> {

    // 1. Ensure the record exists and belongs to the company
    await this.findOne(id, user); // This will throw if not found or not owned

    // 2. Prepare update data
    const data = {
      // Convert string date to Date object if provided
      ...(updateDto.dueDate && { dueDate: new Date(updateDto.dueDate) }),
      // Convert number to Decimal if provided
      ...(updateDto.amount !== undefined && { amount: new Decimal(updateDto.amount) }),
      ...(updateDto.interestAmount !== undefined && { interestAmount: new Decimal(updateDto.interestAmount) }),
      ...(updateDto.discountAmount !== undefined && { discountAmount: new Decimal(updateDto.discountAmount) }),
      ...(updateDto.description && { description: updateDto.description }),
      ...(updateDto.invoiceNumber && { invoiceNumber: updateDto.invoiceNumber }),
      ...(updateDto.notes && { notes: updateDto.notes }),
      ...(updateDto.installments !== undefined && { installments: updateDto.installments }),
      ...(updateDto.installmentNumber !== undefined && { installmentNumber: updateDto.installmentNumber }),
      // Connect optional relations only if they exist in the DTO
      ...(updateDto.entityId && { entity: { connect: { id: updateDto.entityId } } }),
      ...(updateDto.currencyId && { currency: { connect: { id: updateDto.currencyId } } }),
      ...(updateDto.categoryId && { category: { connect: { id: updateDto.categoryId } } }),
      ...(updateDto.projectId && { project: { connect: { id: updateDto.projectId } } }),
      ...(updateDto.paymentMethodId && { paymentMethod: { connect: { id: updateDto.paymentMethodId } } }),
      ...(updateDto.bankAccountId && { bankAccount: { connect: { id: updateDto.bankAccountId } } }),
      ...(updateDto.recurrenceTypeId && { recurrenceType: { connect: { id: updateDto.recurrenceTypeId } } }),
      ...(updateDto.parentId && { parent: { connect: { id: updateDto.parentId } } }),
    };

    // 3. Update the record
    try {
      return await this.prisma['accountsReceivable'].update({
        where: { id },
        data,
      });
    } catch (error) {
      console.error(`Error updating account receivable with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update account receivable.');
    }
  }

  /**
   * Soft delete an account receivable
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {

    // 1. Ensure the record exists and belongs to the company
    await this.findOne(id, user); // Verifies ownership and existence

    // 2. Perform soft delete
    try {
      await this.prisma['accountsReceivable'].update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error soft-deleting account receivable with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete account receivable.');
    }
  }

  // Método receive removido
  // O recebimento agora deve ser feito através do TransactionService
  // criando uma transação do tipo "income" vinculada à conta a receber
}
