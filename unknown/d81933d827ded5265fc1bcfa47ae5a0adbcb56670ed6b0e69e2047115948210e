import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsUUID } from 'class-validator';

export class BalanceSheetQueryDto {
  @ApiProperty({
    description: 'Data de referência para o balanço',
    example: '2025-01-31T23:59:59Z',
  })
  @IsDateString()
  date: string;

  @ApiPropertyOptional({
    description: 'ID da moeda (opcional, se não informado usa a moeda padrão)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  currencyId?: string;
}

export class BalanceSheetItemDto {
  @ApiProperty({
    description: 'Nome da categoria',
    example: 'Caixa e Equivalentes',
  })
  name: string;

  @ApiProperty({
    description: 'Valor',
    example: 15000.75,
  })
  value: number;

  @ApiPropertyOptional({
    description: 'Itens filhos (subcategorias)',
    type: [BalanceSheetItemDto],
  })
  items?: BalanceSheetItemDto[];
}

export class BalanceSheetResponseDto {
  @ApiProperty({
    description: 'Data de referência do balanço',
    example: '2025-01-31T23:59:59Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Ativos',
    type: BalanceSheetItemDto,
  })
  assets: BalanceSheetItemDto;

  @ApiProperty({
    description: 'Passivos',
    type: BalanceSheetItemDto,
  })
  liabilities: BalanceSheetItemDto;

  @ApiProperty({
    description: 'Patrimônio Líquido',
    type: BalanceSheetItemDto,
  })
  equity: BalanceSheetItemDto;

  @ApiProperty({
    description: 'Total de Ativos',
    example: 25000.50,
  })
  totalAssets: number;

  @ApiProperty({
    description: 'Total de Passivos',
    example: 10000.25,
  })
  totalLiabilities: number;

  @ApiProperty({
    description: 'Total de Patrimônio Líquido',
    example: 15000.25,
  })
  totalEquity: number;

  @ApiProperty({
    description: 'Código da moeda',
    example: 'BRL',
  })
  currencyCode: string;

  @ApiProperty({
    description: 'Símbolo da moeda',
    example: 'R$',
  })
  currencySymbol: string;
}
