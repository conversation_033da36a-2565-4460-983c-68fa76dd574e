import { cn } from '@/lib/utils';
import { ReactNode } from 'react';
import GlassCard from '../ui-custom/GlassCard';
import { Loader2 } from 'lucide-react';

interface DashboardCardProps {
  title: string;
  value: string;
  icon: ReactNode;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  valueColor?: string;
  subtitle?: string;
  className?: string;
  isLoading?: boolean;
  hasError?: boolean;
}

const DashboardCard = ({
  title,
  value,
  icon,
  trend,
  valueColor,
  subtitle,
  className,
  isLoading,
  hasError,
}: DashboardCardProps) => {
  const renderContent = () => {
    if (isLoading) {
      return <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />;
    }
    if (hasError) {
      return <span className="text-sm text-red-500">Erro</span>;
    }
    return value;
  };

  const renderSubtitle = () => {
    if (isLoading || hasError) return null;
    return subtitle;
  }

  const renderTrend = () => {
    if (isLoading || hasError) return null;
    return trend;
  }

  const currentTrend = renderTrend();

  return (
    <GlassCard className={cn('h-full', className)} hoverEffect>
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          <div className={cn(
            'text-2xl font-semibold tracking-tight mt-1',
            isLoading ? 'text-muted-foreground' :
            hasError ? 'text-red-500' :
            valueColor
          )}>
            {renderContent()}
          </div>
        </div>

        {currentTrend && (
          <div className={cn(
            'text-xs font-medium px-2 py-1 rounded-full',
            currentTrend.isPositive
              ? 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20'
              : 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20'
          )}>
            <span className="flex items-center">
              {currentTrend.isPositive ? '↑' : '↓'} {currentTrend.value}
            </span>
          </div>
        )}
      </div>

      {renderSubtitle() && (
        <div className="mt-2">
          <span className="text-xs text-muted-foreground">{renderSubtitle()}</span>
        </div>
      )}
    </GlassCard>
  );
};

export default DashboardCard;
