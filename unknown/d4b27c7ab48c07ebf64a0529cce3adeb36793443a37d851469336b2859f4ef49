import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { customPeriodService } from '@/services/api';
import { CreateCustomPeriodRequest, UpdateCustomPeriodRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar períodos personalizados com paginação
export const useCustomPeriods = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['customPeriods', { page, limit, search }],
    queryFn: () => customPeriodService.getCustomPeriods(page, limit, search),
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um período personalizado específico por ID
export const useCustomPeriod = (id: string) => {
  return useQuery({
    queryKey: ['customPeriods', id],
    queryFn: () => customPeriodService.getCustomPeriodById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo período personalizado
export const useCreateCustomPeriod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateCustomPeriodRequest) => customPeriodService.createCustomPeriod(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customPeriods'] });
      toast.success('Período personalizado criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar período personalizado. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um período personalizado existente
export const useUpdateCustomPeriod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateCustomPeriodRequest }) => 
      customPeriodService.updateCustomPeriod(id, data),
    onSuccess: (updatedCustomPeriod) => {
      queryClient.invalidateQueries({ queryKey: ['customPeriods'] });
      queryClient.setQueryData(['customPeriods', updatedCustomPeriod.id], updatedCustomPeriod);
      toast.success('Período personalizado atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar período personalizado. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um período personalizado
export const useDeleteCustomPeriod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => customPeriodService.deleteCustomPeriod(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['customPeriods'] });
      queryClient.removeQueries({ queryKey: ['customPeriods', id] });
      toast.success('Período personalizado excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir período personalizado. Por favor, tente novamente.'
      );
    }
  });
};
