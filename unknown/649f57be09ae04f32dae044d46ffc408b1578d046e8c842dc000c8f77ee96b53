
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import AccountsPayableStatusBadge from "./AccountsPayableStatusBadge";
import { formatCurrency } from "@/utils/currencyUtils";

interface AccountsPayableViewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  account: any | null;
}

export default function AccountsPayableViewModal({
  open,
  onOpenChange,
  account,
}: AccountsPayableViewModalProps) {
  if (!account) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Detalhes da Conta a Pagar</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informações Gerais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Descrição</p>
                <p className="font-medium">{account.description}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Entidade</p>
                <p className="font-medium">{account.entity}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="mt-1">
                  <AccountsPayableStatusBadge status={account.status} />
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Categoria</p>
                <p className="font-medium">{account.category}</p>
              </div>
              {account.project && (
                <div>
                  <p className="text-sm text-muted-foreground">Projeto</p>
                  <p className="font-medium">{account.project}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Valores e Datas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="font-medium text-lg">{formatCurrency(Math.abs(account.amount))}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Pago</p>
                <p className="font-medium">{formatCurrency(account.paidAmount)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Saldo</p>
                <p className="font-medium">{formatCurrency(Math.abs(account.amount) - account.paidAmount)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Data de Vencimento</p>
                <p className="font-medium">{format(account.dueDate, 'dd/MM/yyyy')}</p>
              </div>
              {account.paymentDate && (
                <div>
                  <p className="text-sm text-muted-foreground">Data de Pagamento</p>
                  <p className="font-medium">{format(account.paymentDate, 'dd/MM/yyyy')}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {account.bankAccountId && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg">Informações de Pagamento</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">Conta Bancária</p>
                  <p className="font-medium">Conta #{account.bankAccountId}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
