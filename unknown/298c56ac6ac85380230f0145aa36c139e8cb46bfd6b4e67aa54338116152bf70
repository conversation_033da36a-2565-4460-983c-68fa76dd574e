// backend/src/routes/currencies/dto/update-currency.dto.ts
import {
  IsString,
  IsBoolean,
  IsInt,
  IsOptional,
  MaxLength,
  Min,
  Max,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateCurrencyDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> da moeda (ISO 4217)',
    example: 'BRL',
    maxLength: 3
  })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  code?: string;

  @ApiPropertyOptional({
    description: 'Nome da moeda',
    example: 'Real Brasileiro',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: 'Símbolo da moeda',
    example: 'R$',
    maxLength: 5
  })
  @IsOptional()
  @IsString()
  @MaxLength(5)
  symbol?: string;

  @ApiPropertyOptional({
    description: 'Número de casas decimais',
    example: 2,
    minimum: 0,
    maximum: 10
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  decimalPlaces?: number;

  @ApiPropertyOptional({
    description: 'Indica se é a moeda padrão',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}
