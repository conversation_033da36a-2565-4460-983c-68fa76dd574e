import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class CashFlowQueryDto {
  @ApiProperty({
    description: 'Data de início do período',
    example: '2025-01-01T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Data de fim do período',
    example: '2025-01-31T23:59:59Z',
  })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: 'ID da moeda (opcional, se não informado usa a moeda padrão)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  currencyId?: string;
}

export class CashFlowItemDto {
  @ApiProperty({
    description: 'Data da transação',
    example: '2025-01-15T00:00:00Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Valor total de receitas no dia',
    example: 1500.75,
  })
  income: number;

  @ApiProperty({
    description: 'Valor total de despesas no dia',
    example: 750.25,
  })
  expense: number;

  @ApiProperty({
    description: 'Saldo do dia (receitas - despesas)',
    example: 750.50,
  })
  balance: number;
}

export class CashFlowResponseDto {
  @ApiProperty({
    description: 'Lista de itens do fluxo de caixa',
    type: [CashFlowItemDto],
  })
  items: CashFlowItemDto[];

  @ApiProperty({
    description: 'Total de receitas no período',
    example: 15000.75,
  })
  totalIncome: number;

  @ApiProperty({
    description: 'Total de despesas no período',
    example: 7500.25,
  })
  totalExpense: number;

  @ApiProperty({
    description: 'Saldo total do período (receitas - despesas)',
    example: 7500.50,
  })
  totalBalance: number;

  @ApiProperty({
    description: 'Data de início do período',
    example: '2025-01-01T00:00:00Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Data de fim do período',
    example: '2025-01-31T23:59:59Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Código da moeda',
    example: 'BRL',
  })
  currencyCode: string;

  @ApiProperty({
    description: 'Símbolo da moeda',
    example: 'R$',
  })
  currencySymbol: string;
}
