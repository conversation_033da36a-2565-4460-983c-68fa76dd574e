import { useLocation, useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

// Importar componentes extraídos
import { useAccountsReceivableForm } from '@/components/accounts-receivable/hooks/useAccountsReceivableForm';
import { AccountsReceivableForm } from '@/components/accounts-receivable/AccountsReceivableForm';
import { InstallmentsSection } from '@/components/accounts-receivable/InstallmentsSection';
import { ActionButtons } from '@/components/accounts-receivable/ActionButtons';

export default function AccountsReceivableDetail() {
  const { id, mode } = useParams();
  const location = useLocation();
  
  const {
    formData,
    refreshingFields,
    openCalendar,
    setOpenCalendar,
    openInstallmentCalendar,
    setOpenInstallmentCalendar,
    isReadOnly,
    isEditing,
    isNew,
    handleChange,
    finalAmount,
    handleBack,
    handleSubmit,
    refreshFieldOptions,
    handleEdit,
  } = useAccountsReceivableForm(id, mode);

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleBack} 
            className="mr-4"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {mode === "new" 
              ? "Nova Conta a Receber" 
              : mode === "edit" 
                ? "Editar Conta a Receber" 
                : "Detalhes da Conta a Receber"}
          </h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              {isReadOnly 
                ? "Informações da Conta" 
                : isEditing 
                  ? "Editar Informações" 
                  : "Informações da Nova Conta"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Formulário principal */}
                <AccountsReceivableForm
                  isReadOnly={isReadOnly}
                  formData={formData}
                  refreshingFields={refreshingFields}
                  openCalendar={openCalendar}
                  setOpenCalendar={setOpenCalendar}
                  openInstallmentCalendar={openInstallmentCalendar}
                  setOpenInstallmentCalendar={setOpenInstallmentCalendar}
                  handleChange={handleChange}
                  refreshFieldOptions={refreshFieldOptions}
                  finalAmount={finalAmount}
                />

                {/* Seção de parcelas, juros, descontos e valor final */}
                <InstallmentsSection
                  isReadOnly={isReadOnly}
                  formData={formData}
                  openInstallmentCalendar={openInstallmentCalendar}
                  setOpenInstallmentCalendar={setOpenInstallmentCalendar}
                  handleChange={handleChange}
                  finalAmount={finalAmount}
                />
              </div>
            </div>
            
            <Separator className="my-4" />
            
            {/* Botões de ação */}
            <ActionButtons
              isReadOnly={isReadOnly}
              isEditing={isEditing}
              id={id}
              onBack={handleBack}
              onSubmit={handleSubmit}
              onEdit={handleEdit}
            />
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
