import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsArray } from 'class-validator';

export class UserCompanyRoleDto {
  @ApiProperty({
    description: 'ID único da associação',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID do usuário',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({
    description: 'ID do papel',
    example: '123e4567-e89b-12d3-a456-************',
  })
  roleId: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateUserCompanyRoleDto {
  @ApiProperty({
    description: 'ID do usuário',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({
    description: 'ID do papel',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  roleId: string;
}

export class AssignRolesDto {
  @ApiProperty({
    description: 'IDs dos papéis a serem atribuídos ao usuário',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  roleIds: string[];
}

export class RemoveRolesDto {
  @ApiProperty({
    description: 'IDs dos papéis a serem removidos do usuário',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  roleIds: string[];
}

export class UserCompanyRoleListDto {
  @ApiProperty({
    description: 'Lista de associações usuário-empresa-papel',
    type: [UserCompanyRoleDto],
  })
  items: UserCompanyRoleDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
