import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>Footer,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar as CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import TransactionAmountField from "../transactions/TransactionAmountField";
import { parseCurrencyToNumber } from "@/utils/currencyUtils";

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (project: any) => void;
  project?: any;
}

const ProjectModal = ({ isOpen, onClose, onSave, project }: ProjectModalProps) => {
  const [formData, setFormData] = useState({
    name: "",
    budget: "0,00",
    startDate: new Date(),
    endDate: new Date(),
    status: "Planejado",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name,
        budget: project.budget ? project.budget.toFixed(2).replace('.', ',') : "0,00",
        startDate: new Date(project.startDate),
        endDate: new Date(project.endDate),
        status: project.status,
      });
    } else {
      // Reset form for new project
      setFormData({
        name: "",
        budget: "0,00",
        startDate: new Date(),
        endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
        status: "Planejado",
      });
    }
    setErrors({});
  }, [project, isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear error for this field when user types
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  const handleBudgetChange = (value: string) => {
    setFormData({ ...formData, budget: value });
    if (errors.budget) {
      setErrors({ ...errors, budget: "" });
    }
  };

  const handleStartDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData({ ...formData, startDate: date });
      if (errors.startDate) {
        setErrors({ ...errors, startDate: "" });
      }
    }
  };

  const handleEndDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData({ ...formData, endDate: date });
      if (errors.endDate) {
        setErrors({ ...errors, endDate: "" });
      }
    }
  };

  const handleStatusChange = (value: string) => {
    setFormData({ ...formData, status: value });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "O nome do projeto é obrigatório";
    }
    
    const budgetValue = parseCurrencyToNumber(formData.budget);
    if (isNaN(budgetValue) || budgetValue < 0) {
      newErrors.budget = "O orçamento deve ser um número positivo";
    }
    
    if (formData.endDate < formData.startDate) {
      newErrors.endDate = "A data de término não pode ser anterior à data de início";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSave({
        ...formData,
        budget: parseCurrencyToNumber(formData.budget),
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{project ? "Editar Projeto" : "Criar Novo Projeto"}</DialogTitle>
          <DialogDescription>
            {project 
              ? "Atualize as informações do projeto conforme necessário." 
              : "Preencha os detalhes para criar um novo projeto."}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome do Projeto</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={errors.name ? "border-destructive" : ""}
            />
            {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="budget">Orçamento</Label>
            <TransactionAmountField
              id="budget"
              label=""
              value={formData.budget}
              onChange={handleBudgetChange}
              isMainAmount={true}
              className={errors.budget ? "border-destructive" : ""}
            />
            {errors.budget && <p className="text-sm text-destructive">{errors.budget}</p>}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Data de Início</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      errors.startDate && "border-destructive"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.startDate ? format(formData.startDate, "dd/MM/yyyy") : "Selecione uma data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.startDate}
                    onSelect={handleStartDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.startDate && <p className="text-sm text-destructive">{errors.startDate}</p>}
            </div>
            
            <div className="space-y-2">
              <Label>Data de Término</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      errors.endDate && "border-destructive"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.endDate ? format(formData.endDate, "dd/MM/yyyy") : "Selecione uma data"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.endDate}
                    onSelect={handleEndDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.endDate && <p className="text-sm text-destructive">{errors.endDate}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select defaultValue={formData.status} onValueChange={handleStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Planejado">Planejado</SelectItem>
                <SelectItem value="Em Andamento">Em Andamento</SelectItem>
                <SelectItem value="Concluído">Concluído</SelectItem>
                <SelectItem value="Em Espera">Em Espera</SelectItem>
                <SelectItem value="Cancelado">Cancelado</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit">
              {project ? "Atualizar Projeto" : "Criar Projeto"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectModal;
