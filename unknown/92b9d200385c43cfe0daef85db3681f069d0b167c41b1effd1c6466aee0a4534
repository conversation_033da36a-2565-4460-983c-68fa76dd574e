// backend/src/routes/projects/dto/create-project.dto.ts
import {
  IsString,
  IsOptional,
  IsEnum,
  IsNumber,
  IsDateString,
  MaxLength,
  Min,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ProjectStatus {
  PLANNED = 'planned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
  CANCELED = 'canceled',
}

export class CreateProjectDto {
  @ApiProperty({
    description: 'Nome do projeto',
    example: 'Implementação do Sistema Financeiro',
    maxLength: 150
  })
  @IsString()
  @MaxLength(150)
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição do projeto',
    example: 'Projeto para implementação do sistema financeiro da empresa'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Orçamento do projeto',
    example: 10000.00,
    minimum: 0
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  budget?: number;

  @ApiPropertyOptional({
    description: 'Data de início do projeto',
    example: '2023-01-01'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de término do projeto',
    example: '2023-12-31'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Status do projeto',
    example: 'planned',
    enum: ProjectStatus,
    default: ProjectStatus.PLANNED
  })
  @IsOptional()
  @IsEnum(ProjectStatus, { message: 'Status deve ser planned, in_progress, completed, on_hold ou canceled' })
  status?: ProjectStatus = ProjectStatus.PLANNED;
}
