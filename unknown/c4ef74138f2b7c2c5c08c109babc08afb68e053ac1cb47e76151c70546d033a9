
import { useState, useRef, useEffect } from "react";
import { authService } from "@/services/api/authService";
import { Layout } from "@/components/layout/Layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  User,
  Mail,
  Shield,
  Calendar,
  PenSquare,
  CheckCircle,
  Key,
  Save,
  X,
  Upload,
  Loader2
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from "@/components/ui/sheet";
import { toast } from "sonner";
import { useUserProfile, useUpdateUserProfile } from "@/hooks/api/useUserProfile";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

// Schema de validação para o formulário de perfil
const profileFormSchema = z.object({
  username: z.string().min(3, "O nome de usuário deve ter pelo menos 3 caracteres"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  bio: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const Profile = () => {
  // Buscar dados do perfil usando React Query
  const { data: userData, isLoading, error } = useUserProfile();

  // Mutation para atualizar o perfil
  const updateProfileMutation = useUpdateUserProfile();

  // Estados para controlar modais e menus
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);
  const [openEditSheet, setOpenEditSheet] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Formulário para edição de perfil com validação
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: "",
      firstName: "",
      lastName: "",
      phone: "",
      bio: "",
    }
  });

  // Atualizar o formulário quando os dados do usuário forem carregados
  useEffect(() => {
    if (userData) {
      try {
        form.reset({
          username: userData.profile?.username || "",
          firstName: userData.profile?.firstName || "",
          lastName: userData.profile?.lastName || "",
          phone: userData.profile?.phone || "",
          bio: userData.profile?.preferences?.bio || "",
        });

        if (userData.profile?.avatarUrl) {
          setImagePreview(userData.profile.avatarUrl);
        }
      } catch (error) {
        console.error('Erro ao atualizar formulário com dados do usuário:', error);
        // Não deixar o erro interromper a renderização
      }
    }
  }, [userData, form]);

  // Estado para os campos de senha
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  // Manipuladores para atualização de senha
  const handlePasswordReset = async () => {
    // Validar campos
    if (!currentPassword) {
      setPasswordError("A senha atual é obrigatória");
      return;
    }
    if (!newPassword) {
      setPasswordError("A nova senha é obrigatória");
      return;
    }
    if (newPassword.length < 6) {
      setPasswordError("A nova senha deve ter pelo menos 6 caracteres");
      return;
    }
    if (newPassword !== confirmPassword) {
      setPasswordError("As senhas não coincidem");
      return;
    }

    setPasswordError("");
    setIsChangingPassword(true);

    try {
      // Chamar o serviço para alterar a senha
      await authService.changePassword(currentPassword, newPassword);

      // Limpar campos e fechar o modal
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setOpenPasswordDialog(false);

      // Exibir mensagem de sucesso
      setTimeout(() => {
        toast.success("Senha alterada com sucesso!");
      }, 0);
    } catch (error: any) {
      // Exibir mensagem de erro
      const errorMessage = error.response?.data?.message || "Erro ao alterar senha. Tente novamente.";
      setPasswordError(errorMessage);
      console.error("Erro ao alterar senha:", error);
    } finally {
      setIsChangingPassword(false);
    }
  };

  // Manipulador para selecionar imagem
  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validar o tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      toast.error("Por favor, selecione um arquivo de imagem válido.");
      return;
    }

    // Criar um URL temporário para a imagem selecionada
    const objectUrl = URL.createObjectURL(file);
    setImagePreview(objectUrl);
  };

  // Manipulador para abrir o seletor de arquivos
  const handleImageButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Manipulador para salvar o perfil editado
  const onSubmit = (data: ProfileFormValues) => {
    try {
      // Preparar os dados para atualização
      const profileData = {
        username: data.username,
        firstName: data.firstName,
        lastName: data.lastName,
        phone: data.phone,
        preferences: {
          bio: data.bio,
        },
        avatarUrl: imagePreview || userData?.profile?.avatarUrl,
      };

      // Chamar a mutação para atualizar o perfil
      updateProfileMutation.mutate(profileData, {
        onSuccess: () => {
          setOpenEditSheet(false);
          // Usar setTimeout para evitar atualizações de estado durante a renderização
          setTimeout(() => {
            toast.success("Perfil atualizado com sucesso!");
          }, 0);
        },
        onError: (error: any) => {
          // Usar setTimeout para evitar atualizações de estado durante a renderização
          setTimeout(() => {
            toast.error(`Erro ao atualizar perfil: ${error.message || 'Tente novamente mais tarde'}`);
          }, 0);
        }
      });
    } catch (error) {
      console.error('Erro ao processar dados do formulário:', error);
      // Usar setTimeout para evitar atualizações de estado durante a renderização
      setTimeout(() => {
        toast.error('Erro ao processar dados do formulário. Tente novamente.');
      }, 0);
    }
  };

  // Formatar a data de criação do usuário
  const formatMemberSince = (dateString?: string) => {
    if (!dateString) return "";
    try {
      return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch (e) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto py-6 max-w-6xl flex justify-center items-center min-h-[60vh]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg">Carregando informações do perfil...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !userData) {
    return (
      <Layout>
        <div className="container mx-auto py-6 max-w-6xl">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-red-700 mb-2">Erro ao carregar perfil</h2>
            <p className="text-red-600">
              Não foi possível carregar as informações do seu perfil. Por favor, tente novamente mais tarde.
            </p>
            <Button
              className="mt-4"
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Tentar novamente
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex items-center mb-8">
          <User className="h-8 w-8 text-blue-600 mr-3" />
          <h1 className="text-3xl font-bold">Meu Perfil</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Informações Pessoais */}
          <Card className="border shadow-sm">
            <CardContent className="p-6">
              <h2 className="text-2xl font-semibold mb-2">Informações Pessoais</h2>
              <p className="text-gray-500 mb-8">Visualize e gerencie suas informações de perfil</p>

              <div className="flex justify-center mb-8">
                <Avatar className="h-32 w-32 bg-blue-100 text-blue-600 text-5xl">
                  {userData.profile?.avatarUrl ?
                    <AvatarImage src={userData.profile.avatarUrl} alt={userData.profile.username || ''} /> :
                    null
                  }
                  <AvatarFallback>
                    {userData.profile?.firstName?.charAt(0) || userData.profile?.username?.charAt(0) || userData.email.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <User className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-gray-500 text-sm">Nome de Usuário</p>
                    <p className="font-medium">{userData.profile?.username || 'Não definido'}</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <Mail className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-gray-500 text-sm">E-mail</p>
                    <p className="font-medium">{userData.email}</p>
                  </div>
                </div>

                {userData.role && (
                  <div className="flex items-start gap-4">
                    <Shield className="h-5 w-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-gray-500 text-sm">Nível de Acesso</p>
                      <p className="font-medium text-orange-500">{userData.role.name}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-start gap-4">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-gray-500 text-sm">Membro desde</p>
                    <p className="font-medium">{formatMemberSince(userData.createdAt)}</p>
                  </div>
                </div>
              </div>

              <Button
                className="mt-8 w-full flex items-center justify-center gap-2"
                variant="outline"
                onClick={() => setOpenEditSheet(true)}
              >
                <PenSquare className="h-4 w-4" />
                Editar Perfil
              </Button>
            </CardContent>
          </Card>

          {/* Preferências e Segurança */}
          <div className="space-y-6">
            <Card className="border shadow-sm">
              <CardContent className="p-6">
                <h2 className="text-2xl font-semibold mb-2">Preferências e Segurança</h2>
                <p className="text-gray-500 mb-6">Gerencie suas preferências e configurações de segurança</p>

                <div className="bg-amber-50 border border-amber-100 rounded-lg p-6 mt-4">
                  <h3 className="text-xl font-medium text-amber-800 mb-4">Segurança da Conta</h3>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Key className="h-5 w-5 text-amber-700" />
                        <span className="text-amber-800 font-medium">Senha</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-amber-200 bg-amber-100 hover:bg-amber-200 text-amber-900"
                        onClick={() => setOpenPasswordDialog(true)}
                      >
                        Redefinir Senha
                      </Button>
                    </div>

                    <div className="text-sm text-amber-800">
                      Mantenha sua senha forte e segura. Recomendamos alterá-la periodicamente.
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border shadow-sm bg-blue-50">
              <CardContent className="p-6">
                <h3 className="text-2xl font-semibold text-blue-800 mb-4">Status da Conta</h3>

                <div className="flex items-center gap-2 text-green-600 mb-3">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">Conta Ativa</span>
                </div>

                <p className="text-blue-700">
                  Sua conta está ativa e funcionando normalmente.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Modal para redefinição de senha */}
      <Dialog open={openPasswordDialog} onOpenChange={setOpenPasswordDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Redefinir Senha</DialogTitle>
            <DialogDescription>
              Preencha os campos abaixo para alterar sua senha atual.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            {passwordError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm mb-4">
                {passwordError}
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="current-password">Senha Atual</Label>
              <Input
                id="current-password"
                type="password"
                placeholder="Digite sua senha atual"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">Nova Senha</Label>
              <Input
                id="new-password"
                type="password"
                placeholder="Digite a nova senha"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirmar Nova Senha</Label>
              <Input
                id="confirm-password"
                type="password"
                placeholder="Confirme a nova senha"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <DialogClose asChild>
              <Button type="button" variant="outline">Cancelar</Button>
            </DialogClose>
            <Button
              type="button"
              onClick={handlePasswordReset}
              disabled={isChangingPassword}
            >
              {isChangingPassword ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Alterando...
                </>
              ) : (
                "Alterar Senha"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Painel lateral para edição de perfil */}
      <Sheet open={openEditSheet} onOpenChange={setOpenEditSheet}>
        <SheetContent className="sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Editar Perfil</SheetTitle>
            <SheetDescription>
              Atualize suas informações pessoais. Clique em salvar quando terminar.
            </SheetDescription>
          </SheetHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-6">
              <div className="flex flex-col items-center mb-6">
                <Avatar className="h-24 w-24 bg-blue-100 text-blue-600 text-4xl mb-4">
                  {imagePreview ? <AvatarImage src={imagePreview} alt="Preview" /> :
                   userData.profile?.avatarUrl ? <AvatarImage src={userData.profile.avatarUrl} alt={userData.profile.username || ''} /> : null}
                  <AvatarFallback>
                    {userData.profile?.firstName?.charAt(0) || userData.profile?.username?.charAt(0) || userData.email.charAt(0)}
                  </AvatarFallback>
                </Avatar>

                <div className="mt-2">
                  <input
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleImageButtonClick}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    Selecionar Imagem
                  </Button>
                </div>
              </div>

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome de Usuário</FormLabel>
                    <FormControl>
                      <Input placeholder="Seu nome de usuário" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome</FormLabel>
                      <FormControl>
                        <Input placeholder="Seu nome" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sobrenome</FormLabel>
                      <FormControl>
                        <Input placeholder="Seu sobrenome" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefone</FormLabel>
                    <FormControl>
                      <Input placeholder="(00) 00000-0000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Biografia</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Conte um pouco sobre você"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpenEditSheet(false)}
                  className="mr-2"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Button>
                <Button type="submit">
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Alterações
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </Layout>
  );
};

export default Profile;
