import {
  Injectable,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { CreateUserDto, UpdateUserDto, UserDto } from '../models/user.model';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { QueryUtil } from '../utils/query.util';
import { mapToUserDto } from '../utils/mappers.util';

@Injectable()
export class UserManagerService {
  private readonly logger = new Logger(UserManagerService.name);

  constructor(
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Cria um novo usuário com perfil
   * @param createUserDto Dados para criação do usuário
   * @returns Usuário criado
   */
  async create(createUserDto: CreateUserDto): Promise<UserDto> {
    try {
      // Verificar se o email já existe
      if (await this.queryUtil.checkUserExistsByEmail(createUserDto.email)) {
        throw new ConflictException('Email já existe');
      }

      // Verificar se o username já existe
      if (
        createUserDto.profile?.username &&
        (await this.queryUtil.checkProfileExistsByUsername(
          createUserDto.profile.username,
        ))
      ) {
        throw new ConflictException('Nome de usuário já existe');
      }

      // Criptografar senha
      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

      // Criar usuário e perfil em uma transação
      return await this.prisma.$transaction(async (tx) => {
        // Gerar UUID para o novo usuário
        const newUserId = uuidv4();

        // Criar usuário usando SQL nativo para evitar problemas de tipagem
        await tx.$executeRaw`
          INSERT INTO "users" (id, email, password, status, created_at, updated_at) 
          VALUES (${newUserId}::uuid, ${createUserDto.email}, ${hashedPassword}, ${
            createUserDto.status || 'active'
          }, NOW(), NOW())
        `;

        // Criar perfil usando SQL nativo
        await tx.$executeRaw`
          INSERT INTO "profiles" (id, username, first_name, last_name, phone, avatar_url, preferences, is_active, created_at, updated_at)
          VALUES (
            ${newUserId}::uuid, 
            ${createUserDto.profile?.username || null}, 
            ${createUserDto.profile?.firstName || null}, 
            ${createUserDto.profile?.lastName || null}, 
            ${createUserDto.profile?.phone || null}, 
            ${createUserDto.profile?.avatarUrl || null}, 
            ${createUserDto.profile?.preferences || null}, 
            ${createUserDto.profile?.isActive === false ? false : true}, 
            NOW(), 
            NOW()
          )
        `;

        // Buscar o usuário e perfil criados
        const userRaw = await this.queryUtil.findUserById(newUserId);
        return mapToUserDto(userRaw);
      });
    } catch (error) {
      this.logger.error(`Erro ao criar usuário: ${error.message}`, error.stack);
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Falha ao criar usuário');
    }
  }

  /**
   * Atualiza um usuário
   * @param id ID do usuário
   * @param updateUserDto Dados para atualização do usuário
   * @returns Usuário atualizado
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserDto> {
    try {
      // Verificar se o usuário existe
      await this.queryUtil.findUserById(id);

      // Verificar se o email já existe (se foi fornecido)
      if (
        updateUserDto.email &&
        (await this.prisma.$queryRaw<{ id: string }[]>`
          SELECT id FROM users 
          WHERE email = ${updateUserDto.email} 
          AND id != ${id}::uuid
          AND deleted_at IS NULL
        `).length > 0
      ) {
        throw new ConflictException('Email já está em uso');
      }

      // Iniciar transação para atualizar usuário e perfil
      return await this.prisma.$transaction(async (tx) => {
        // Atualizar campos do usuário
        if (updateUserDto.email || updateUserDto.status || updateUserDto.password) {
          const updateFields: string[] = [];
          const updateValues: any[] = [];

          if (updateUserDto.email) {
            updateFields.push('email = ?');
            updateValues.push(updateUserDto.email);
          }

          if (updateUserDto.status) {
            updateFields.push('status = ?');
            updateValues.push(updateUserDto.status);
          }

          if (updateUserDto.password) {
            const hashedPassword = await bcrypt.hash(updateUserDto.password, 10);
            updateFields.push('password = ?');
            updateValues.push(hashedPassword);
          }

          updateFields.push('updated_at = NOW()');

          // Executar atualização do usuário
          if (updateFields.length > 0) {
            const updateQuery = `
              UPDATE users 
              SET ${updateFields.join(', ')} 
              WHERE id = $${updateValues.length + 1}::uuid
            `;
            await tx.$executeRawUnsafe(updateQuery, ...updateValues, id);
          }
        }

        // Buscar o usuário atualizado
        const updatedUser = await this.queryUtil.findUserById(id);
        return mapToUserDto(updatedUser);
      });
    } catch (error) {
      this.logger.error(`Erro ao atualizar usuário: ${error.message}`, error.stack);
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Falha ao atualizar usuário');
    }
  }

  /**
   * Remove um usuário (exclusão lógica)
   * @param id ID do usuário
   */
  async remove(id: string): Promise<void> {
    try {
      // Verificar se o usuário existe
      await this.queryUtil.findUserById(id);

      // Remover usuário logicamente
      await this.prisma.$transaction(async (tx) => {
        // Marcar perfil como excluído
        await tx.$executeRaw`
          UPDATE profiles 
          SET deleted_at = NOW() 
          WHERE id = ${id}::uuid
        `;

        // Marcar usuário como excluído
        await tx.$executeRaw`
          UPDATE users 
          SET deleted_at = NOW() 
          WHERE id = ${id}::uuid
        `;
      });
    } catch (error) {
      this.logger.error(`Erro ao remover usuário: ${error.message}`, error.stack);
      throw new BadRequestException('Falha ao remover usuário');
    }
  }
} 