import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Building2, Lock, Settings as SettingsIcon, Users } from "lucide-react";
import { Button } from "@/components/ui/button";

const SecuritySettings = () => {
  const navigate = useNavigate();
  
  return (
    <Layout>
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-8">
          <SettingsIcon className="h-8 w-8 text-primary mr-3" />
          <h1 className="text-3xl font-bold">Configurações</h1>
        </div>
        
        <div className="flex border-b mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/companies")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-700"
          >
            <Building2 className="h-5 w-5" />
            <span>Empresas</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/users")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-700"
          >
            <Users className="h-5 w-5" />
            <span>Usuários</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/security")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-primary text-primary font-medium"
          >
            <Lock className="h-5 w-5" />
            <span>Segurança</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/general")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-700"
          >
            <SettingsIcon className="h-5 w-5" />
            <span>Geral</span>
          </Button>
        </div>
        
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <Lock className="h-6 w-6" />
            Configurações de Segurança
          </h2>
        </div>
        
        <div className="bg-white dark:bg-dark-card rounded-lg border shadow dark:border-dark-card-light p-8">
          <p className="text-center text-gray-500 dark:text-gray-400">Configurações de segurança serão implementadas em breve.</p>
        </div>
      </div>
    </Layout>
  );
};

export default SecuritySettings;
