import { ApiProperty } from '@nestjs/swagger';

export class CustomPeriodDto {
  @ApiProperty({
    description: 'ID único do período personalizado',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do período personalizado',
    example: 'Primeiro Trimestre 2025',
  })
  name: string;

  @ApiProperty({
    description: 'Data de início do período',
    example: '2025-01-01',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Data de fim do período',
    example: '2025-03-31',
  })
  endDate: Date;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  companyId: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2025-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2025-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CustomPeriodListDto {
  @ApiProperty({
    description: 'Lista de períodos personalizados',
    type: [CustomPeriodDto],
  })
  items: CustomPeriodDto[];

  @ApiProperty({
    description: 'Total de registros',
    example: 10,
  })
  total: number;

  @ApiProperty({
    description: 'Página atual',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Limite de registros por página',
    example: 10,
  })
  limit: number;
}
