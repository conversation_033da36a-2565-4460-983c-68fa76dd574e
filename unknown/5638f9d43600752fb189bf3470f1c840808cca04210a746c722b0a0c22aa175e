// backend/src/routes/banks/banks.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { BanksService } from './banks.service';
import { CreateBankDto } from './dto/create-bank.dto';
import { UpdateBankDto } from './dto/update-bank.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('banks')
@Controller('banks')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
export class BanksController {
  constructor(private readonly banksService: BanksService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo banco' })
  @ApiBody({ type: CreateBankDto })
  @ApiResponse({
    status: 201,
    description: 'Banco criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  create(@Body() createBankDto: CreateBankDto) {
    return this.banksService.create(createBankDto);
  }

  @Get()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os bancos' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number })
  @ApiResponse({ status: 200, description: 'Lista de bancos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite de itens por página inválido');
    }

    return this.banksService.findAll({
      page: pageNumber,
      limit: limitNumber,
    });
  }

  @Get(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter um banco pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do banco', type: String })
  @ApiResponse({ status: 200, description: 'Banco encontrado' })
  @ApiResponse({ status: 404, description: 'Banco não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.banksService.findOne(id);
  }

  @Put(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um banco' })
  @ApiParam({ name: 'id', description: 'ID do banco', type: String })
  @ApiBody({ type: UpdateBankDto })
  @ApiResponse({ status: 200, description: 'Banco atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Banco não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBankDto: UpdateBankDto,
  ) {
    return this.banksService.update(id, updateBankDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um banco' })
  @ApiParam({ name: 'id', description: 'ID do banco', type: String })
  @ApiResponse({ status: 204, description: 'Banco removido com sucesso' })
  @ApiResponse({ status: 400, description: 'Não é possível remover o banco' })
  @ApiResponse({ status: 404, description: 'Banco não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.banksService.remove(id);
  }
}
