import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/api';
import { CreateUserRequest, UpdateUserRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar usuários com paginação
export const useUsers = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['users', { page, limit, search }],
    queryFn: () => userService.getUsers(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um usuário específico por ID
export const useUser = (id: string) => {
  return useQuery({
    queryKey: ['users', id],
    queryFn: () => userService.getUserById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo usuário
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateUserRequest) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('Usuário criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar usuário. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um usuário existente
export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateUserRequest }) => 
      userService.updateUser(id, data),
    onSuccess: (updatedUser) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.setQueryData(['users', updatedUser.id], updatedUser);
      toast.success('Usuário atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar usuário. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um usuário
export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.removeQueries({ queryKey: ['users', id] });
      toast.success('Usuário excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir usuário. Por favor, tente novamente.'
      );
    }
  });
};
