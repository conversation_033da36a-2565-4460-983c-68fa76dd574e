import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateRoleDto,
  RoleDto,
  UpdateRoleDto,
  RoleListDto,
} from './dto/role.dto';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('roles')
@Controller('companies/:companyId/roles')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiBody({ type: CreateRoleDto })
  @ApiResponse({
    status: 201,
    description: 'Papel criado com sucesso',
    type: RoleDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @ApiResponse({ status: 409, description: 'Papel já existe' })
  async create(
    @Param('companyId') companyId: string,
    @Body() createRoleDto: CreateRoleDto,
  ): Promise<RoleDto> {
    // Garantir que o companyId do path seja usado
    createRoleDto.companyId = companyId;
    return this.rolesService.create(createRoleDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os papéis de uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de papéis',
    type: RoleListDto,
  })
  async findAll(
    @Param('companyId') companyId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<RoleListDto> {
    return this.rolesService.findAll(companyId, page, limit);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um papel pelo ID' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiResponse({
    status: 200,
    description: 'Papel encontrado',
    type: RoleDto,
  })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  async findOne(
    @Param('companyId') companyId: string,
    @Param('id') id: string,
  ): Promise<RoleDto> {
    const role = await this.rolesService.findOne(id);

    // Verificar se o papel pertence à empresa
    if (role.companyId !== companyId) {
      throw new NotFoundException('Papel não encontrado nesta empresa');
    }

    return role;
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiBody({ type: UpdateRoleDto })
  @ApiResponse({
    status: 200,
    description: 'Papel atualizado com sucesso',
    type: RoleDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  @ApiResponse({ status: 409, description: 'Nome já existe' })
  async update(
    @Param('companyId') companyId: string,
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<RoleDto> {
    const role = await this.rolesService.findOne(id);

    // Verificar se o papel pertence à empresa
    if (role.companyId !== companyId) {
      throw new NotFoundException('Papel não encontrado nesta empresa');
    }

    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um papel' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiParam({ name: 'id', description: 'ID do papel' })
  @ApiResponse({ status: 204, description: 'Papel removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Papel não encontrado' })
  @ApiResponse({ status: 409, description: 'Papel está em uso' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('companyId') companyId: string,
    @Param('id') id: string,
  ): Promise<void> {
    const role = await this.rolesService.findOne(id);

    // Verificar se o papel pertence à empresa
    if (role.companyId !== companyId) {
      throw new NotFoundException('Papel não encontrado nesta empresa');
    }

    return this.rolesService.remove(id);
  }

  @Post('setup')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configurar papel de administrador padrão' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiResponse({
    status: 201,
    description: 'Papel de administrador configurado com sucesso',
    type: RoleDto,
  })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  @HttpCode(HttpStatus.CREATED)
  async setup(@Param('companyId') companyId: string): Promise<RoleDto> {
    return this.rolesService.createDefaultAdminRole(companyId);
  }
}
