import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { companyService } from '@/services/api';
import { CreateCompanyRequest, UpdateCompanyRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar empresas com paginação
export const useCompanies = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['companies', { page, limit, search }],
    queryFn: () => companyService.getCompanies(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar uma empresa específica por ID
export const useCompany = (id: string) => {
  return useQuery({
    queryKey: ['companies', id],
    queryFn: () => companyService.getCompanyById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar configurações de uma empresa
export const useCompanySettings = (id: string) => {
  return useQuery({
    queryKey: ['companySettings', id],
    queryFn: () => companyService.getCompanySettings(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar uma nova empresa
export const useCreateCompany = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateCompanyRequest) => companyService.createCompany(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast.success('Empresa criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar empresa. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma empresa existente
export const useUpdateCompany = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateCompanyRequest }) => 
      companyService.updateCompany(id, data),
    onSuccess: (updatedCompany) => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      queryClient.setQueryData(['companies', updatedCompany.id], updatedCompany);
      toast.success('Empresa atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar empresa. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma empresa
export const useDeleteCompany = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => companyService.deleteCompany(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      queryClient.removeQueries({ queryKey: ['companies', id] });
      toast.success('Empresa excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir empresa. Por favor, tente novamente.'
      );
    }
  });
};
