import {
  Table,
} from "@/components/ui/table";
import { useState, useMemo } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Card } from "@/components/ui/card";
import PaymentForm from "@/components/transactions/PaymentForm";
import AccountsPayableTableFilters from "./AccountsPayableTableFilters";
import AccountsPayableTableHeader from "./AccountsPayableTableHeader";
import AccountsPayableTableBody from "./AccountsPayableTableBody";

interface AccountsPayableTableProps {
  onEdit: (item: any) => void;
  onView: (item: any) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filter: string;
  onFilterChange: (filter: string) => void;
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  selectedAccountId: string;
  onAccountChange: (accountId: string) => void;
  selectedEntityId: string;
  onEntityChange: (entityId: string) => void;
  accounts: Array<{ id: string; name: string }>;
  onClearFilters: () => void;
  onClearAllFilters: () => void;
  customDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  onDateRangeChange?: (startDate?: Date, endDate?: Date) => void;
  data: any[];
  totalItems: number;
  isLoading?: boolean;
}

export default function AccountsPayableTable({
  onEdit,
  onView,
  searchTerm,
  setSearchTerm,
  filter,
  onFilterChange,
  selectedPeriod,
  onPeriodChange,
  selectedAccountId,
  onAccountChange,
  selectedEntityId,
  onEntityChange,
  accounts,
  onClearFilters,
  onClearAllFilters,
  customDateRange,
  onDateRangeChange,
  data,
  totalItems,
  isLoading
}: AccountsPayableTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // Filtros agora são aplicados no backend, mas mantemos a lógica para filtros adicionais no frontend
  const filteredData = useMemo(() => {
    // Se não houver dados, retornar array vazio
    if (!data || data.length === 0) return [];

    // Aplicar filtros adicionais que não foram aplicados no backend
    return data.filter(item => {
      // Filtro de conta bancária (não aplicado no backend)
      let matchesAccount = true;
      if (selectedAccountId !== "all") {
        matchesAccount = item.bankAccountId === selectedAccountId;
      }

      return matchesAccount;
    });
  }, [data, selectedAccountId]);

  const handlePay = (item: any) => {
    setSelectedItem(item);
    setIsPaymentModalOpen(true);
  };

  return (
    <>
      <Card className="overflow-hidden border shadow-sm">
        <AccountsPayableTableFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filter={filter}
          onFilterChange={onFilterChange}
          selectedPeriod={selectedPeriod}
          onPeriodChange={onPeriodChange}
          selectedAccountId={selectedAccountId}
          onAccountChange={onAccountChange}
          selectedEntityId={selectedEntityId}
          onEntityChange={onEntityChange}
          accounts={accounts}
          onClearFilters={onClearFilters}
          onClearAllFilters={onClearAllFilters}
          customDateRange={customDateRange}
          onDateRangeChange={onDateRangeChange}
        />

        <div className="overflow-x-auto">
          <Table>
            <AccountsPayableTableHeader />
            <AccountsPayableTableBody
              filteredData={filteredData}
              onEdit={onEdit}
              handlePay={handlePay}
              handleView={onView}
            />
          </Table>
        </div>

        <div className="p-4 border-t">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious href="#" />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" isActive>1</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationNext href="#" />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </Card>

      <PaymentForm
        open={isPaymentModalOpen}
        onOpenChange={setIsPaymentModalOpen}
        transaction={selectedItem}
      />
    </>
  );
}
