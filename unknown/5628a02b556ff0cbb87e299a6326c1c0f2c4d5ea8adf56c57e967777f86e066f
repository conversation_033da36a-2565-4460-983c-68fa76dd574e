#!/bin/bash

# Compilar os scripts TypeScript
echo "Compilando scripts TypeScript..."
npx tsc -p tsconfig.json

# Verificar se a compilação foi bem-sucedida
if [ $? -ne 0 ]; then
  echo "Erro ao compilar os scripts TypeScript."
  exit 1
fi

# Copiar o arquivo SQL para a pasta dist
echo "Copiando arquivo SQL para a pasta dist..."
mkdir -p dist/scripts
cp src/scripts/create-roles-table.sql dist/scripts/

# Executar os scripts na ordem correta
echo "Executando script de configuração de papéis..."
node dist/scripts/setup-roles.js

echo "Executando script de associação de usuários às empresas..."
node dist/scripts/associate-users-companies.js

echo "Configuração concluída com sucesso!"
