# Documentação Técnica - Arquitetura do Sistema de Gestão Financeira Multi-Tenant

## 1. Visão Geral da Arquitetura

O Sistema de Gestão Financeira Multi-Tenant é construído com uma arquitetura moderna, escalável e stateless, dividida em frontend e backend, que se comunicam via uma API RESTful. Ele suporta múltiplos tenants com isolamento de dados, utilizando uma combinação de tecnologias robustas para garantir desempenho, segurança e flexibilidade.

```mermaid
graph TB
    subgraph "Front-end"
        A[React.js com Next.js] --> B[Redux Toolkit]
        B --> C[RTK Query]
        C --> D[API Integration]
    end
    
    subgraph "Back-end"
        E[NestJS API] --> F[Controllers]
        F --> G[Services]
        G --> H[Prisma ORM]
        H --> I[(PostgreSQL)]
    end
    
    D <--> E
```

---

## 2. Front-end

### 2.1 Tecnologias Principais

- **React.js com Next.js (v14+)**
  - Framework para construção de interfaces modernas e server-side rendering.
  - Uso de Functional Components e React Hooks.
  - Pages Router ou App Router para navegação otimizada.

- **TypeScript**
  - Tipagem estática para segurança e manutenibilidade.
  - Interfaces bem definidas para modelos como `Bill`, `Transaction`, `BankAccount`.
  - Configuração rigorosa com `tsconfig.json`.

### 2.2 Gerenciamento de Estado

- **Redux Toolkit**
  - Store centralizada para estado global (ex.: tenant selecionado, usuário autenticado).
  - Slices para modularidade (ex.: `billsSlice`, `transactionsSlice`).
  - Reducers e ações tipados para consistência.

- **RTK Query**
  - Cache automático para dados da API (ex.: listas de contas, relatórios).
  - Invalidação de cache em operações de escrita (ex.: após registrar pagamento).
  - Hooks como `useGetBillsQuery` para integração simplificada.

### 2.3 Roteamento e Navegação

- **Next.js Routing**
  - Roteamento baseado em arquivos (ex.: `/bills`, `/projects`).
  - Lazy loading de páginas com `dynamic` imports.
  - Rotas protegidas via middleware de autenticação.

### 2.4 UI/UX

- **Tailwind CSS (com shadcn/ui)**
  - Estilização utility-first para design responsivo.
  - Componentes pré-construídos (ex.: tabelas, formulários).
  - Design system consistente e customizável.

- **Recharts**
  - Gráficos interativos para relatórios (ex.: fluxo de caixa, despesas por projeto).
  - Suporte a personalização e responsividade.

### 2.5 Utilitários

- **Lucide React**: Ícones modernos e leves.
- **Numeral.js**: Formatação de valores monetários (ex.: R$ 1.234,56).
- **Axios**: Cliente HTTP para chamadas à API RESTful.

---

## 3. Back-end

### 3.1 Tecnologias Principais

- **Node.js com NestJS**
  - Framework estruturado para APIs escaláveis.
  - Uso de decorators para rotas, validação e injeção de dependências.
  - Middlewares para autenticação (JWT) e logs.

### 3.2 Banco de Dados

- **PostgreSQL (v15+)**
  - Banco relacional com suporte a multi-tenancy via RLS.
  - Extensão `pgcrypto` para geração de UUIDs.
  - Índices otimizados (ex.: `tenant_id`, `due_date`).

- **Prisma ORM**
  - Mapeamento tipo-seguro para tabelas como `Bills`, `Transactions`, `BankAccounts`.
  - Migrações automáticas com `prisma migrate`.
  - Query builder para consultas complexas (ex.: relatórios).

```mermaid
erDiagram
    Tenants ||--o{ Users : owns
    Tenants ||--o{ Bills : owns
    Bills ||--o{ Transactions : has
    BankAccounts ||--o{ Transactions : updates
    Tenants {
        uuid id PK
        string name
    }
    Users {
        uuid id PK
        uuid tenant_id FK
        string email
        string password
    }
    Bills {
        uuid id PK
        uuid tenant_id FK
        string type
        decimal amount
        decimal paid_amount
        timestamp due_date
    }
    Transactions {
        uuid id PK
        uuid tenant_id FK
        uuid bill_id FK
        decimal amount
        timestamp transaction_date
        uuid bank_account_id FK
    }
    BankAccounts {
        uuid id PK
        uuid tenant_id FK
        uuid bank_id FK
        string account_number
        decimal balance
    }
```

### 3.3 Autenticação e Segurança

- **JWT (JSON Web Tokens)**
  - Autenticação stateless com expiração de 1 hora.
  - Refresh tokens para sessões prolongadas.
  - Payload inclui `tenant_id` e `user_id`.

### 3.4 Estrutura Modular
```
src/
├── auth/           # Lógica de autenticação
├── bills/          # Módulo de contas (controllers, services)
├── transactions/   # Módulo de transações
├── bank-accounts/  # Módulo de contas bancárias
├── entities/       # Módulo de entidades
├── projects/       # Módulo de projetos
├── roles/          # Módulo de acesso
├── common/         # Utilitários e middlewares
├── prisma/         # Configuração do Prisma
└── main.ts         # Entry point
```

---

## 4. Padrões e Práticas

### 4.1 Design Patterns

- **Repository Pattern**: Abstração da camada de dados via Prisma.
- **Service Pattern**: Lógica de negócios encapsulada em serviços (ex.: `BillsService`).
- **Decorator Pattern**: Uso de decorators no NestJS para validação e autenticação.
- **Observer Pattern**: Notificações disparadas por eventos (ex.: pagamento registrado).

### 4.2 Princípios SOLID
- **Single Responsibility**: Cada módulo (`bills`, `transactions`) tem uma única responsabilidade.
- **Open/Closed**: Extensível via serviços e middlewares.
- **Liskov Substitution**: Interfaces genéricas para serviços.
- **Interface Segregation**: APIs específicas por módulo.
- **Dependency Inversion**: Injeção de dependências no NestJS.

### 4.3 Práticas de Desenvolvimento
- **Code Style**: ESLint + Prettier configurados com regras rigorosas.
- **Testing**: Jest para unitários e integração, Playwright para E2E.
- **CI/CD**: GitHub Actions com pipeline de testes e deploy.
- **Documentation**: Swagger/OpenAPI para documentação de endpoints.

---

## 5. API RESTful

### 5.1 Endpoints Principais

```
POST   /api/v1/auth/login           # Autenticação
POST   /api/v1/users                # Registro de usuário
GET    /api/v1/bills                # Lista contas
POST   /api/v1/bills                # Cria conta
PUT    /api/v1/bills/:id            # Atualiza conta (ex.: pagamento)
GET    /api/v1/transactions         # Lista transações
POST   /api/v1/transactions         # Registra transação
GET    /api/v1/bank-accounts        # Lista contas bancárias
POST   /api/v1/bank-accounts        # Cria conta bancária
GET    /api/v1/reports/cash-flow    # Relatório de fluxo de caixa
```

### 5.2 Padrões de API
- **Versionamento**: `/api/v1/` para compatibilidade futura.
- **Respostas**: Formato JSON padronizado `{ success: boolean, data: any, error: string }`.
- **Status Codes**: 200 (OK), 201 (Created), 400 (Bad Request), 401 (Unauthorized), etc.
- **Rate Limiting**: 100 requisições por IP/minuto.
- **Validação**: Usando `@nestjs/class-validator`.

---

## 6. Segurança

- **HTTPS**: Obrigatório em todas as comunicações.
- **Autenticação**: JWT com `tenant_id` e `user_id` no payload.
- **Autorização**: RLS no PostgreSQL e guards no NestJS.
- **Sanitização**: Validação de entradas com `class-validator`.
- **CORS**: Configurado para origens permitidas.
- **Logs**: Registro de operações sensíveis (ex.: logins, pagamentos).

---

## 7. Escalabilidade

- **Arquitetura Stateless**: Sem estado no servidor, permitindo múltiplas instâncias.
- **Caching**: RTK Query no frontend, Redis opcional no backend.
- **Otimização de Consultas**: Índices em `tenant_id`, `due_date`, `bill_id`.
- **Load Balancing**: Suportado via AWS ALB ou Nginx.
- **Containerização**: Docker para deploy em ECS/Fargate ou VPS.

---