import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import {
  SystemPermissionDto,
  CreateSystemPermissionDto,
  UpdateSystemPermissionDto,
  SystemPermissionListDto,
} from './dto/system-permission.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SystemPermissionsService {
  private readonly logger = new Logger(SystemPermissionsService.name);

  constructor(private prisma: PrismaService) {}

  async create(
    createSystemPermissionDto: CreateSystemPermissionDto,
  ): Promise<SystemPermissionDto> {
    try {
      // Verificar se já existe uma permissão com o mesmo código
      const existingPermission = await this.prisma.systemPermission.findUnique({
        where: { code: createSystemPermissionDto.code },
      });

      if (existingPermission) {
        throw new ConflictException(
          `Já existe uma permissão com o código ${createSystemPermissionDto.code}`,
        );
      }

      // Criar a permissão
      const systemPermission = await this.prisma.systemPermission.create({
        data: {
          id: uuidv4(),
          code: createSystemPermissionDto.code,
          name: createSystemPermissionDto.name,
          description: createSystemPermissionDto.description,
          module: createSystemPermissionDto.module,
        },
      });

      return {
        id: systemPermission.id,
        code: systemPermission.code,
        name: systemPermission.name,
        description: systemPermission.description || undefined,
        module: systemPermission.module,
        createdAt: systemPermission.createdAt,
        updatedAt: systemPermission.updatedAt,
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(
        `Erro ao criar permissão do sistema: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(
    page = 1,
    limit = 10,
    module?: string,
  ): Promise<SystemPermissionListDto> {
    try {
      const skip = (page - 1) * limit;

      // Construir o filtro
      const where = module ? { module } : {};

      // Contar total de permissões
      const total = await this.prisma.systemPermission.count({ where });

      // Buscar permissões paginadas
      const systemPermissions = await this.prisma.systemPermission.findMany({
        where,
        skip,
        take: limit,
        orderBy: { code: 'asc' },
      });

      return {
        items: systemPermissions.map((permission) => ({
          id: permission.id,
          code: permission.code,
          name: permission.name,
          description: permission.description || undefined,
          module: permission.module,
          createdAt: permission.createdAt,
          updatedAt: permission.updatedAt,
        })),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Erro ao listar permissões do sistema: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: string): Promise<SystemPermissionDto> {
    try {
      const systemPermission = await this.prisma.systemPermission.findUnique({
        where: { id },
      });

      if (!systemPermission) {
        throw new NotFoundException('Permissão do sistema não encontrada');
      }

      return {
        id: systemPermission.id,
        code: systemPermission.code,
        name: systemPermission.name,
        description: systemPermission.description || undefined,
        module: systemPermission.module,
        createdAt: systemPermission.createdAt,
        updatedAt: systemPermission.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar permissão do sistema: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findByCode(code: string): Promise<SystemPermissionDto> {
    try {
      const systemPermission = await this.prisma.systemPermission.findUnique({
        where: { code },
      });

      if (!systemPermission) {
        throw new NotFoundException('Permissão do sistema não encontrada');
      }

      return {
        id: systemPermission.id,
        code: systemPermission.code,
        name: systemPermission.name,
        description: systemPermission.description || undefined,
        module: systemPermission.module,
        createdAt: systemPermission.createdAt,
        updatedAt: systemPermission.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar permissão do sistema por código: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async update(
    id: string,
    updateSystemPermissionDto: UpdateSystemPermissionDto,
  ): Promise<SystemPermissionDto> {
    try {
      // Verificar se a permissão existe
      const existingPermission = await this.prisma.systemPermission.findUnique({
        where: { id },
      });

      if (!existingPermission) {
        throw new NotFoundException('Permissão do sistema não encontrada');
      }

      // Atualizar a permissão
      const updatedPermission = await this.prisma.systemPermission.update({
        where: { id },
        data: {
          name: updateSystemPermissionDto.name,
          description: updateSystemPermissionDto.description,
          module: updateSystemPermissionDto.module,
        },
      });

      return {
        id: updatedPermission.id,
        code: updatedPermission.code,
        name: updatedPermission.name,
        description: updatedPermission.description || undefined,
        module: updatedPermission.module,
        createdAt: updatedPermission.createdAt,
        updatedAt: updatedPermission.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao atualizar permissão do sistema: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Verificar se a permissão existe
      const existingPermission = await this.prisma.systemPermission.findUnique({
        where: { id },
        include: { permissions: true },
      });

      if (!existingPermission) {
        throw new NotFoundException('Permissão do sistema não encontrada');
      }

      // Verificar se a permissão está sendo usada
      if (existingPermission.permissions.length > 0) {
        throw new ConflictException(
          'Esta permissão está em uso e não pode ser removida',
        );
      }

      // Remover a permissão
      await this.prisma.systemPermission.delete({ where: { id } });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(
        `Erro ao remover permissão do sistema: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Método para criar permissões padrão do sistema
  async createDefaultSystemPermissions(): Promise<void> {
    const defaultPermissions = [
      {
        code: 'companies.view',
        name: 'Visualizar empresas',
        description: 'Permite visualizar dados da empresa',
        module: 'companies',
      },
      {
        code: 'companies.edit',
        name: 'Editar empresas',
        description: 'Permite editar dados da empresa',
        module: 'companies',
      },
      {
        code: 'users.view',
        name: 'Visualizar usuários',
        description: 'Permite visualizar usuários da empresa',
        module: 'users',
      },
      {
        code: 'users.create',
        name: 'Criar usuários',
        description: 'Permite criar novos usuários',
        module: 'users',
      },
      {
        code: 'users.edit',
        name: 'Editar usuários',
        description: 'Permite editar usuários existentes',
        module: 'users',
      },
      {
        code: 'users.delete',
        name: 'Remover usuários',
        description: 'Permite remover usuários',
        module: 'users',
      },
      {
        code: 'roles.view',
        name: 'Visualizar papéis',
        description: 'Permite visualizar papéis',
        module: 'roles',
      },
      {
        code: 'roles.create',
        name: 'Criar papéis',
        description: 'Permite criar novos papéis',
        module: 'roles',
      },
      {
        code: 'roles.edit',
        name: 'Editar papéis',
        description: 'Permite editar papéis existentes',
        module: 'roles',
      },
      {
        code: 'roles.delete',
        name: 'Remover papéis',
        description: 'Permite remover papéis',
        module: 'roles',
      },
      {
        code: 'accounts_payable.view',
        name: 'Visualizar contas a pagar',
        description: 'Permite visualizar contas a pagar',
        module: 'finance',
      },
      {
        code: 'accounts_payable.create',
        name: 'Criar contas a pagar',
        description: 'Permite criar contas a pagar',
        module: 'finance',
      },
      {
        code: 'accounts_payable.edit',
        name: 'Editar contas a pagar',
        description: 'Permite editar contas a pagar',
        module: 'finance',
      },
      {
        code: 'accounts_payable.delete',
        name: 'Remover contas a pagar',
        description: 'Permite remover contas a pagar',
        module: 'finance',
      },
      {
        code: 'accounts_receivable.view',
        name: 'Visualizar contas a receber',
        description: 'Permite visualizar contas a receber',
        module: 'finance',
      },
      {
        code: 'accounts_receivable.create',
        name: 'Criar contas a receber',
        description: 'Permite criar contas a receber',
        module: 'finance',
      },
      {
        code: 'accounts_receivable.edit',
        name: 'Editar contas a receber',
        description: 'Permite editar contas a receber',
        module: 'finance',
      },
      {
        code: 'accounts_receivable.delete',
        name: 'Remover contas a receber',
        description: 'Permite remover contas a receber',
        module: 'finance',
      },
      {
        code: 'transactions.view',
        name: 'Visualizar transações',
        description: 'Permite visualizar transações',
        module: 'finance',
      },
      {
        code: 'transactions.create',
        name: 'Criar transações',
        description: 'Permite criar transações',
        module: 'finance',
      },
      {
        code: 'transactions.edit',
        name: 'Editar transações',
        description: 'Permite editar transações',
        module: 'finance',
      },
      {
        code: 'transactions.delete',
        name: 'Remover transações',
        description: 'Permite remover transações',
        module: 'finance',
      },
      {
        code: 'reports.view',
        name: 'Visualizar relatórios',
        description: 'Permite visualizar relatórios',
        module: 'reports',
      },
      {
        code: 'reports.export',
        name: 'Exportar relatórios',
        description: 'Permite exportar relatórios',
        module: 'reports',
      },
    ];

    for (const permission of defaultPermissions) {
      try {
        // Verificar se a permissão já existe
        const existingPermission = await this.prisma.systemPermission.findUnique({
          where: { code: permission.code },
        });

        if (!existingPermission) {
          await this.prisma.systemPermission.create({
            data: {
              id: uuidv4(),
              ...permission,
            },
          });
        }
      } catch (error) {
        this.logger.error(
          `Erro ao criar permissão padrão ${permission.code}: ${error.message}`,
          error.stack,
        );
      }
    }
  }
}
