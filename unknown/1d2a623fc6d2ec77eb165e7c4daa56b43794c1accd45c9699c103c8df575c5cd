import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { projectService } from '@/services/api';
import { CreateProjectRequest, UpdateProjectRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar projetos com paginação
export const useProjects = (page = 1, limit = 10, search?: string, status?: string) => {
  return useQuery({
    queryKey: ['projects', { page, limit, search, status }],
    queryFn: () => projectService.getProjects(page, limit, search, status),
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um projeto específico por ID
export const useProject = (id: string) => {
  return useQuery({
    queryKey: ['projects', id],
    queryFn: () => projectService.getProjectById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo projeto
export const useCreateProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateProjectRequest) => projectService.createProject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Projeto criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar projeto. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um projeto existente
export const useUpdateProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateProjectRequest }) => 
      projectService.updateProject(id, data),
    onSuccess: (updatedProject) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.setQueryData(['projects', updatedProject.id], updatedProject);
      toast.success('Projeto atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar projeto. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um projeto
export const useDeleteProject = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => projectService.deleteProject(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.removeQueries({ queryKey: ['projects', id] });
      toast.success('Projeto excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir projeto. Por favor, tente novamente.'
      );
    }
  });
};
