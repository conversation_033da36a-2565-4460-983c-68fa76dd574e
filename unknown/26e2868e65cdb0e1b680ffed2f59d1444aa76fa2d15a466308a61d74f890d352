import { useState, useCallback, useEffect } from 'react';
import { useApiCache } from './useApiCache';
import monitoringService from '@/services/api/monitoringService';

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    currentPage: number;
    itemsPerPage: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

interface UsePaginatedApiOptions {
  initialParams?: Partial<PaginationParams>;
  cacheDuration?: number;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  enabled?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export function usePaginatedApi<T>(
  endpoint: string,
  options: UsePaginatedApiOptions = {}
) {
  // Parâmetros de paginação padrão
  const defaultParams: PaginationParams = {
    page: 1,
    limit: 10,
    sortOrder: 'desc',
    ...options.initialParams
  };

  // Estado para os parâmetros de paginação
  const [params, setParams] = useState<PaginationParams>(defaultParams);
  
  // Construir a chave de cache baseada no endpoint e nos parâmetros
  const getCacheKey = useCallback(() => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          queryParams.set(key, JSON.stringify(value));
        } else {
          queryParams.set(key, String(value));
        }
      }
    });
    
    return `${endpoint}?${queryParams.toString()}`;
  }, [endpoint, params]);
  
  // Função para buscar dados paginados
  const fetchPaginatedData = useCallback(async () => {
    monitoringService.recordEvent('paginated_request_start', { 
      endpoint, 
      page: params.page,
      limit: params.limit
    });
    
    try {
      // Construir a URL com os parâmetros de paginação
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (key === 'filters' && typeof value === 'object') {
            // Tratar filtros como parâmetros separados
            Object.entries(value).forEach(([filterKey, filterValue]) => {
              if (filterValue !== undefined && filterValue !== null) {
                queryParams.set(filterKey, String(filterValue));
              }
            });
          } else if (typeof value === 'object') {
            queryParams.set(key, JSON.stringify(value));
          } else {
            queryParams.set(key, String(value));
          }
        }
      });
      
      const url = `${endpoint}?${queryParams.toString()}`;
      
      // Usar o serviço de API para fazer a requisição
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar dados: ${response.status}`);
      }
      
      const result = await response.json();
      
      monitoringService.recordEvent('paginated_request_success', { 
        endpoint, 
        page: params.page,
        totalItems: result.meta?.totalItems,
        itemCount: result.data?.length
      });
      
      return result as PaginatedResponse<T>;
    } catch (error) {
      monitoringService.recordEvent('paginated_request_error', { 
        endpoint, 
        page: params.page,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
      throw error;
    }
  }, [endpoint, params]);
  
  // Usar o hook de cache para gerenciar os dados
  const {
    data,
    loading,
    error,
    refetch,
    invalidateCache
  } = useApiCache<PaginatedResponse<T>>(
    getCacheKey(),
    fetchPaginatedData,
    {
      cacheDuration: options.cacheDuration,
      revalidateOnFocus: options.revalidateOnFocus,
      revalidateOnReconnect: options.revalidateOnReconnect,
      onError: options.onError,
      enabled: options.enabled,
      errorMessage: 'Falha ao carregar dados paginados.'
    }
  );
  
  // Efeito para chamar o callback de sucesso quando os dados forem carregados
  useEffect(() => {
    if (data && options.onSuccess) {
      options.onSuccess(data);
    }
  }, [data, options.onSuccess]);
  
  // Funções para navegação entre páginas
  const goToPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);
  
  const nextPage = useCallback(() => {
    if (data?.meta.hasNextPage) {
      setParams(prev => ({ ...prev, page: prev.page + 1 }));
    }
  }, [data?.meta.hasNextPage]);
  
  const previousPage = useCallback(() => {
    if (data?.meta.hasPreviousPage) {
      setParams(prev => ({ ...prev, page: prev.page - 1 }));
    }
  }, [data?.meta.hasPreviousPage]);
  
  // Função para alterar o limite de itens por página
  const setLimit = useCallback((limit: number) => {
    setParams(prev => ({ ...prev, limit, page: 1 }));
  }, []);
  
  // Função para alterar a ordenação
  const setSorting = useCallback((sortBy: string, sortOrder: 'asc' | 'desc' = 'asc') => {
    setParams(prev => ({ ...prev, sortBy, sortOrder, page: 1 }));
  }, []);
  
  // Função para definir um termo de busca
  const setSearch = useCallback((search: string) => {
    setParams(prev => ({ ...prev, search, page: 1 }));
  }, []);
  
  // Função para aplicar filtros
  const setFilters = useCallback((filters: Record<string, any>) => {
    setParams(prev => ({ ...prev, filters, page: 1 }));
  }, []);
  
  // Função para resetar todos os parâmetros para o estado inicial
  const resetParams = useCallback(() => {
    setParams(defaultParams);
  }, [defaultParams]);
  
  return {
    // Dados e estado
    data,
    loading,
    error,
    params,
    
    // Funções de navegação
    goToPage,
    nextPage,
    previousPage,
    
    // Funções de configuração
    setLimit,
    setSorting,
    setSearch,
    setFilters,
    resetParams,
    
    // Funções de controle de cache
    refetch,
    invalidateCache
  };
}
