import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  Matches,
  Length,
  MaxLength,
  MinLength,
  IsUppercase,
  Validate
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ZipCodeFormatValidator, BrazilianStateValidator } from '../validators/zip-code.validator';

export class CreateZipCodeDto {
  @ApiProperty({ description: 'CEP', example: '01310-100' })
  @IsString()
  @IsNotEmpty()
  @Validate(ZipCodeFormatValidator)
  @Transform(({ value }) => {
    // Formata o CEP para o padrão XXXXX-XXX se for fornecido sem hífen
    if (/^\d{8}$/.test(value)) {
      return `${value.substring(0, 5)}-${value.substring(5)}`;
    }
    return value;
  })
  zipCode: string;

  @ApiProperty({ description: 'Rua/Logradouro', example: '<PERSON>ni<PERSON> Paulista' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'O logradouro deve ter pelo menos 3 caracteres' })
  @MaxLength(255, { message: 'O logradouro deve ter no máximo 255 caracteres' })
  @Transform(({ value }) => value.trim())
  street: string;

  @ApiProperty({ description: 'Bairro', example: 'Bela Vista' })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'O bairro deve ter pelo menos 2 caracteres' })
  @MaxLength(100, { message: 'O bairro deve ter no máximo 100 caracteres' })
  @Transform(({ value }) => value.trim())
  neighborhood: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo' })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'A cidade deve ter pelo menos 2 caracteres' })
  @MaxLength(100, { message: 'A cidade deve ter no máximo 100 caracteres' })
  @Transform(({ value }) => value.trim())
  city: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP' })
  @IsString()
  @IsNotEmpty()
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  @Validate(BrazilianStateValidator)
  @Transform(({ value }) => value.toUpperCase().trim())
  state: string;

  @ApiProperty({
    description: 'Origem do registro',
    example: 'manual',
    enum: ['auto', 'manual'],
    default: 'manual'
  })
  @IsEnum(['auto', 'manual'], { message: 'A origem deve ser "auto" ou "manual"' })
  registrationOrigin: string = 'manual';
}
