# Product Requirement Document (PRD) – Sistema de Gestão Financeira Multi-Tenant

## 1. Visão Geral

### 1.1 Propósito
O Sistema de Gestão Financeira Multi-Tenant é uma solução projetada para pequenas e médias empresas, permitindo o gerenciamento eficiente das finanças em um ambiente multi-tenant. Ele oferece controle detalhado de contas a pagar e receber, projetos, transações financeiras, saldos bancários, entidades (clientes e fornecedores), e acesso seguro por tenant, com relatórios e notificações em tempo real.

### 1.2 Objetivos
- Proporcionar uma visão integrada e em tempo real da saúde financeira de cada tenant.
- Garantir isolamento de dados entre empresas (tenants) com segurança robusta.
- Simplificar o planejamento financeiro (contas a pagar/receber) e o registro de movimentações efetivas.
- Oferecer flexibilidade para suportar pagamentos únicos, parcelados e parciais.
- Integrar controle de saldos bancários com bancos globais e relatórios personalizados.

### 1.3 Público-Alvo
- Pequenas e médias empresas que necessitam de uma solução financeira acessível e escalável.
- Usuários finais: Proprietários, gerentes financeiros e equipes administrativas dessas empresas.

---

## 2. Requisitos Funcionais

### 2.1 Multi-Tenancy
- **ID**: RF-001
- **Descrição**: O sistema deve suportar múltiplos tenants, isolando os dados de cada empresa.
- **Detalhes**: Cada tenant terá seus próprios usuários, contas, projetos, entidades e saldos bancários, isolados via Row-Level Security (RLS) no PostgreSQL.

### 2.2 Gestão de Contas a Pagar e Receber
- **ID**: RF-002
- **Descrição**: Permitir o cadastro, acompanhamento e pagamento de contas a pagar e receber.
- **Detalhes**:
  - Suporte a contas únicas (imediato, futuro ou atrasado), parceladas ou parciais.
  - Campos: Tipo (`payable`, `receivable`), valor total, valor pago, data de vencimento, status (`pending`, `partial`, `paid`, `overdue`), descrição, método de pagamento, categoria, subcategoria, projeto, entidade, conta bancária, recorrência, número de parcelas.
  - Opção de marcar como "paga/recebida" no cadastro, gerando uma transação efetiva.

### 2.3 Registro de Transações
- **ID**: RF-003
- **Descrição**: Registrar movimentações financeiras efetivas (pagamentos/recebimentos) vinculadas a contas.
- **Detalhes**:
  - Campos: Conta associada (`bill_id`), valor, data da transação, conta bancária, descrição.
  - Atualiza automaticamente o saldo em `BankAccounts`.

### 2.4 Gestão de Projetos
- **ID**: RF-004
- **Descrição**: Gerenciar projetos com rastreamento financeiro.
- **Detalhes**:
  - Campos: Nome, orçamento, datas de início e término.
  - Vinculação opcional a contas em `Bills`.

### 2.5 Controle de Entidades
- **ID**: RF-005
- **Descrição**: Gerenciar clientes e fornecedores com informações detalhadas.
- **Detalhes**:
  - Campos: Tipo (`customer`, `supplier`), nome, endereço, telefone, contato, email.
  - Endereços em tabela separada (`Addresses`) com suporte a CEP (manual ou automático via API).

### 2.6 Controle de Contas Bancárias
- **ID**: RF-006
- **Descrição**: Gerenciar contas bancárias por tenant, integradas a bancos globais.
- **Detalhes**:
  - Bancos globais (`Banks`): Código (ex.: FEBRABAN ou internacional), nome.
  - Contas (`BankAccounts`): Número da conta, saldo, data do saldo, vinculada a um banco.
  - Saldo atualizado por transações em `Transactions`.

### 2.7 Relatórios Financeiros
- **ID**: RF-007
- **Descrição**: Gerar relatórios financeiros customizáveis.
- **Detalhes**:
  - Tipos: Fluxo de caixa, despesas por projeto/empresa/conta bancária, contas a pagar/receber, despesas x receitas.
  - Filtros: Período, tenant, projeto, conta bancária.

### 2.8 Notificações
- **ID**: RF-008
- **Descrição**: Enviar notificações em tempo real para eventos financeiros.
- **Detalhes**:
  - Tipos: Email, in-app ou ambos.
  - Eventos: Vencimento de contas, pagamentos efetuados, atrasos.
  - Campos: Título, mensagem, status (`unread`, `read`, `sent`).

### 2.9 Controle de Acesso (RBAC)
- **ID**: RF-009
- **Descrição**: Gerenciar acesso por tenant com permissões granulares.
- **Detalhes**:
  - Papéis (`Roles`) e permissões (`Permissions`) personalizáveis por tenant.
  - Permissões no formato `<ação>:<recurso>` (ex.: `create:bill`).

### 2.10 Categorias
- **ID**: RF-010
- **Descrição**: Classificar contas com categorias hierárquicas.
- **Detalhes**:
  - Campos: Nome, tipo de transação (`receivable`, `payable`), categoria pai (opcional).
  - Filtragem por tipo no cadastro de contas.

### 2.11 Cache de CEPs
- **ID**: RF-011
- **Descrição**: Armazenar CEPs localmente por tenant para preenchimento de endereços.
- **Detalhes**:
  - Campos: CEP, logradouro, bairro, cidade, estado, origem (`manual` ou `auto`).
  - Suporte a enriquecimento manual em background.

---

## 3. Requisitos Não Funcionais

### 3.1 Segurança
- **Autenticação**: JWT com expiração de 1 hora.
- **Autorização**: RBAC por tenant, com RLS no PostgreSQL.
- **Criptografia**: Dados sensíveis (ex.: senhas) criptografados com bcrypt; endereços podem usar `pgcrypto`.
- **HTTPS**: Todas as comunicações via HTTPS.

### 3.2 Escalabilidade
- **Arquitetura Stateless**: Suporte a múltiplas instâncias sem estado.
- **Contêineres**: Implantação via Docker para escalabilidade horizontal.

### 3.3 Desempenho
- **Rate Limiting**: 100 requisições por IP por minuto, ajustável.
- **Índices**: Otimização de consultas com índices em `tenant_id`, `due_date`, etc.

### 3.4 Disponibilidade
- **Uptime**: 99.9% com deploy em AWS (ECS/RDS) ou VPS robusta.
- **Monitoramento**: Prometheus e Grafana para métricas em tempo real.

---

## 4. Especificações Técnicas

### 4.1 Stack Tecnológica
- **Frontend**: React com Next.js, TypeScript, Tailwind CSS (shadcn/ui).
- **Backend**: Node.js com NestJS, TypeScript.
- **Banco de Dados**: PostgreSQL com Prisma ORM.
- **Infraestrutura**: Docker, AWS (ECS, RDS, ECR, ALB) ou VPS com Nginx.
- **CI/CD**: GitHub Actions.
- **Monitoramento**: Prometheus e Grafana.
- **Documentação**: Swagger (OpenAPI).
- **APIs Externas**: ViaCEP para CEPs.

### 4.2 Modelo de Dados (Resumo)
```
- Tenants: Empresas (id, name, created_at, updated_at)
- Users: Usuários por tenant (id, tenant_id, email, password, role_id, ...)
- Projects: Projetos por tenant (id, tenant_id, name, budget, start_date, ...)
- Entities: Clientes/fornecedores (id, tenant_id, type, name, address_id, ...)
- Addresses: Endereços (id, tenant_id, cep, logradouro, numero, bairro, ...)
- Bills: Contas a pagar/receber (id, tenant_id, type, amount, paid_amount, ...)
- Transactions: Pagamentos/recebimentos (id, tenant_id, bill_id, amount, ...)
- Categories: Categorias hierárquicas (id, tenant_id, name, transaction_type, ...)
- Roles: Papéis por tenant (id, tenant_id, name, description, ...)
- Permissions: Permissões (id, tenant_id, role_id, action, ...)
- Notifications: Notificações (id, tenant_id, user_id, title, message, ...)
- Banks: Bancos globais (id, code, name, ...)
- BankAccounts: Contas bancárias (id, tenant_id, bank_id, account_number, balance, ...)
- Ceps: Cache de CEPs (cep, tenant_id, logradouro, bairro, ...)
```

### 4.3 API RESTful
- **Base URL**: `/api/v1/`
- **Exemplos**:
  - `POST /api/v1/auth/login`: Autenticação.
  - `POST /api/v1/bills`: Cadastro de conta.
  - `PUT /api/v1/bills/{id}`: Pagamento de conta.
  - `POST /api/v1/bank-accounts`: Cadastro de conta bancária.

---

## 5. Fluxos Principais

### 5.1 Cadastro de Conta a Pagar/Receber
- **Passos**:
  1. Usuário preenche formulário com tipo, valor, data de vencimento, etc.
  2. Se marcada como "paga", registra em `Bills` e insere uma transação em `Transactions`.
  3. Atualiza saldo em `BankAccounts` via `Transactions`.
  4. Envia notificação de confirmação.

### 5.2 Inclusão de Conta Parcelada
- **Passos**:
  1. Usuário define valor total, número de parcelas e data inicial.
  2. Sistema cria registro "pai" em `Bills` e registros "filhos" para cada parcela.
  3. Se a primeira parcela for paga, registra em `Transactions` e atualiza saldo.

### 5.3 Pagamento Parcial de Conta
- **Passos**:
  1. Usuário solicita pagamento parcial de uma parcela.
  2. Atualiza `paid_amount` e `status` em `Bills`.
  3. Insere transação em `Transactions` com o valor pago.
  4. Atualiza saldo em `BankAccounts` e envia notificação.

---

## 6. Critérios de Aceitação

- **RF-002**: Usuário pode cadastrar uma conta única e marcá-la como paga, refletindo no saldo bancário em menos de 2 segundos.
- **RF-003**: Pagamentos parciais são registrados com atualização correta de `paid_amount` e saldo.
- **RF-007**: Relatórios são gerados em menos de 5 segundos para 1 ano de dados.
- **RF-009**: Apenas usuários com permissão `create:bill` podem cadastrar contas.

---

## 7. Considerações de Implementação

- **Escalabilidade**: Arquitetura stateless com Docker para múltiplas instâncias.
- **Segurança**: JWT, RLS, HTTPS, criptografia de dados sensíveis.
- **Testes**: Unitários (Jest), integração (Jest), E2E (Playwright), cobertura > 80%.
- **Monitoramento**: Métricas como erros, desempenho e consultas ao banco.

---

## 8. Cronograma Estimado
- **Fase 1 (2 meses)**: Backend (APIs, modelo de dados), autenticação/autorização.
- **Fase 2 (1,5 meses)**: Frontend (interface de contas, projetos, entidades).
- **Fase 3 (1 mês)**: Relatórios, notificações, testes e deploy inicial.

---

## 9. Riscos e Mitigações
- **Risco**: Sobrecarga no banco de dados com muitos tenants.
  - **Mitigação**: Índices otimizados e escalabilidade horizontal.
- **Risco**: Falhas na API de CEPs.
  - **Mitigação**: Fallback para preenchimento manual e cache local.

---