import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { FilterOption, FilterState } from '@/hooks/useFilters';
import { CalendarIcon, X, Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface AdvancedFiltersProps {
  filters: FilterOption[];
  filterState: FilterState;
  onFilterChange: (id: string, value: any) => void;
  onClearFilters: () => void;
  onApplyFilters?: () => void;
  activeFilterCount?: number;
  className?: string;
  showFilterCount?: boolean;
  title?: string;
  collapsible?: boolean;
  initiallyCollapsed?: boolean;
}

export function AdvancedFilters({
  filters,
  filterState,
  onFilterChange,
  onClearFilters,
  onApplyFilters,
  activeFilterCount = 0,
  className,
  showFilterCount = true,
  title = 'Filtros',
  collapsible = true,
  initiallyCollapsed = false,
}: AdvancedFiltersProps) {
  const [isCollapsed, setIsCollapsed] = useState(initiallyCollapsed);
  
  // Função para renderizar o componente de filtro com base no tipo
  const renderFilterInput = (filter: FilterOption) => {
    const value = filterState[filter.id];
    
    switch (filter.type) {
      case 'text':
        return (
          <Input
            id={filter.id}
            value={value || ''}
            onChange={(e) => onFilterChange(filter.id, e.target.value)}
            placeholder={filter.placeholder}
            className="w-full"
          />
        );
        
      case 'select':
        return (
          <Select
            value={value !== null && value !== undefined ? String(value) : ''}
            onValueChange={(val) => onFilterChange(filter.id, val)}
          >
            <SelectTrigger>
              <SelectValue placeholder={filter.placeholder || 'Selecione...'} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos</SelectItem>
              {filter.options?.map((option) => (
                <SelectItem key={String(option.value)} value={String(option.value)}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
        
      case 'date':
        return (
          <div className="grid gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !value && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {value ? format(value, 'PPP', { locale: ptBR }) : <span>Selecione uma data</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={value}
                  onSelect={(date) => onFilterChange(filter.id, date)}
                  initialFocus
                  locale={ptBR}
                />
              </PopoverContent>
            </Popover>
            {value && (
              <Button
                variant="ghost"
                size="sm"
                className="mt-1"
                onClick={() => onFilterChange(filter.id, null)}
              >
                <X className="mr-2 h-3 w-3" />
                Limpar
              </Button>
            )}
          </div>
        );
        
      case 'dateRange':
        return (
          <div className="grid gap-2">
            <div className="flex flex-col space-y-2">
              <Label className="text-xs">De:</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !value?.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {value?.from ? (
                      format(value.from, 'PPP', { locale: ptBR })
                    ) : (
                      <span>Selecione a data inicial</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={value?.from}
                    onSelect={(date) =>
                      onFilterChange(filter.id, { ...value, from: date })
                    }
                    initialFocus
                    locale={ptBR}
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="flex flex-col space-y-2">
              <Label className="text-xs">Até:</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !value?.to && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {value?.to ? (
                      format(value.to, 'PPP', { locale: ptBR })
                    ) : (
                      <span>Selecione a data final</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={value?.to}
                    onSelect={(date) =>
                      onFilterChange(filter.id, { ...value, to: date })
                    }
                    initialFocus
                    locale={ptBR}
                    disabled={(date) =>
                      value?.from ? date < value.from : false
                    }
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            {(value?.from || value?.to) && (
              <Button
                variant="ghost"
                size="sm"
                className="mt-1"
                onClick={() => onFilterChange(filter.id, { from: null, to: null })}
              >
                <X className="mr-2 h-3 w-3" />
                Limpar intervalo
              </Button>
            )}
          </div>
        );
        
      case 'number':
        return (
          <Input
            id={filter.id}
            type="number"
            value={value !== null && value !== undefined ? value : ''}
            onChange={(e) => {
              const val = e.target.value === '' ? null : Number(e.target.value);
              onFilterChange(filter.id, val);
            }}
            placeholder={filter.placeholder}
            className="w-full"
          />
        );
        
      case 'numberRange':
        const min = filter.options?.[0]?.value as number || 0;
        const max = filter.options?.[1]?.value as number || 100;
        
        return (
          <div className="space-y-4">
            <div className="flex justify-between">
              <div className="flex-1 pr-2">
                <Label className="text-xs">Mínimo:</Label>
                <Input
                  type="number"
                  value={value?.from !== null && value?.from !== undefined ? value.from : ''}
                  onChange={(e) => {
                    const val = e.target.value === '' ? null : Number(e.target.value);
                    onFilterChange(filter.id, { ...value, from: val });
                  }}
                  className="w-full"
                />
              </div>
              <div className="flex-1 pl-2">
                <Label className="text-xs">Máximo:</Label>
                <Input
                  type="number"
                  value={value?.to !== null && value?.to !== undefined ? value.to : ''}
                  onChange={(e) => {
                    const val = e.target.value === '' ? null : Number(e.target.value);
                    onFilterChange(filter.id, { ...value, to: val });
                  }}
                  className="w-full"
                />
              </div>
            </div>
            
            {(value?.from !== null || value?.to !== null) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFilterChange(filter.id, { from: null, to: null })}
              >
                <X className="mr-2 h-3 w-3" />
                Limpar intervalo
              </Button>
            )}
          </div>
        );
        
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={filter.id}
              checked={!!value}
              onCheckedChange={(checked) => onFilterChange(filter.id, checked)}
            />
            <Label htmlFor={filter.id}>{value ? 'Sim' : 'Não'}</Label>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-lg">{title}</CardTitle>
            {showFilterCount && activeFilterCount > 0 && (
              <Badge variant="secondary">{activeFilterCount}</Badge>
            )}
          </div>
          
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <>
          <CardContent className="grid gap-6">
            {filters.map((filter) => (
              <div key={filter.id} className="grid gap-2">
                <Label htmlFor={filter.id}>{filter.label}</Label>
                {renderFilterInput(filter)}
              </div>
            ))}
          </CardContent>
          
          <CardFooter className="flex justify-between pt-3">
            <Button variant="outline" onClick={onClearFilters}>
              Limpar filtros
            </Button>
            
            {onApplyFilters && (
              <Button onClick={onApplyFilters}>
                Aplicar filtros
              </Button>
            )}
          </CardFooter>
        </>
      )}
    </Card>
  );
}
