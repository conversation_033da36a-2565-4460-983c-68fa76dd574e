import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UserDto } from '../models/user.model';
import { QueryUtil } from '../utils/query.util';
import { mapToUserDto } from '../utils/mappers.util';
import { UserRawResult } from '../utils/sql-transforms.util';

@Injectable()
export class UserQueryService {
  private readonly logger = new Logger(UserQueryService.name);

  constructor(
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Busca um usuário pelo ID
   * @param id ID do usuário
   * @returns Dados do usuário
   */
  async findById(id: string): Promise<UserDto> {
    try {
      const user = await this.queryUtil.findUserById(id);
      return mapToUserDto(user);
    } catch (error) {
      this.logger.error(`Erro ao buscar usuário por ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Busca um usuário pelo email
   * @param email Email do usuário
   * @returns Dados do usuário
   */
  async findByEmail(email: string): Promise<UserDto> {
    try {
      const user = await this.queryUtil.findUserByEmail(email);
      return mapToUserDto(user);
    } catch (error) {
      this.logger.error(`Erro ao buscar usuário por email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lista usuários paginados associados a uma empresa
   * @param companyId ID da empresa
   * @param page Número da página
   * @param limit Limite de registros por página
   * @returns Lista paginada de usuários
   */
  async findAllByCompany(companyId: string, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;

      // Buscar usuários associados à empresa
      const userCompanyRoles = await this.prisma.$queryRaw<UserRawResult[]>`
        SELECT u.id, u.email, u.status, u.created_at, u.updated_at,
               p.username, p.first_name, p.last_name, p.phone, p.avatar_url, p.preferences, p.is_active
        FROM "user_company_roles" ucr
        JOIN "users" u ON ucr.user_id = u.id
        LEFT JOIN "profiles" p ON u.id = p.id
        WHERE ucr.company_id = ${companyId}::uuid
        AND u.deleted_at IS NULL
        ORDER BY u.email
        LIMIT ${limit} OFFSET ${skip}
      `;

      // Transformar resultados para o formato DTO
      const users = userCompanyRoles.map(mapToUserDto);

      // Contar total de usuários
      const totalResult = await this.prisma.$queryRaw<{ total: string }[]>`
        SELECT COUNT(*) as total
        FROM "user_company_roles" ucr
        JOIN "users" u ON ucr.user_id = u.id
        WHERE ucr.company_id = ${companyId}::uuid
        AND u.deleted_at IS NULL
      `;

      const total = totalResult.length > 0 ? Number(totalResult[0].total) : 0;

      return {
        data: users,
        meta: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Erro ao listar usuários: ${error.message}`, error.stack);
      throw error;
    }
  }
} 