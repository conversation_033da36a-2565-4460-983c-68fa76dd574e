import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UpdateProfileDto } from '../models/profile.model';
import { UserProfileDto } from '../models/user.model';
import { QueryUtil } from '../utils/query.util';
import { mapToUserProfileDto } from '../utils/mappers.util';
import { UserRawResult } from '../utils/sql-transforms.util';

@Injectable()
export class UserProfileService {
  private readonly logger = new Logger(UserProfileService.name);

  constructor(
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Obtém o perfil de um usuário
   * @param userId ID do usuário
   * @returns Perfil do usuário
   */
  async getProfile(userId: string): Promise<UserProfileDto> {
    try {
      const user = await this.queryUtil.findUserById(userId);
      return mapToUserProfileDto(user);
    } catch (error) {
      this.logger.error(`Erro ao buscar perfil: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Atualiza o perfil de um usuário
   * @param userId ID do usuário
   * @param updateProfileDto Dados para atualização do perfil
   * @returns Perfil atualizado
   */
  async updateProfile(
    userId: string,
    updateProfileDto: UpdateProfileDto,
  ): Promise<UserProfileDto> {
    try {
      // Verificar se o usuário existe
      await this.queryUtil.findUserById(userId);

      // Verificar se o nome de usuário já existe para outro usuário
      if (updateProfileDto.username) {
        const existingUsernameResult = await this.prisma.$queryRaw<{ id: string }[]>`
          SELECT id FROM profiles
          WHERE username = ${updateProfileDto.username}
          AND id != ${userId}::uuid
          AND deleted_at IS NULL
        `;

        if (existingUsernameResult.length > 0) {
          throw new ConflictException('Nome de usuário já existe');
        }
      }

      // Construir os campos a serem atualizados
      const updates: string[] = [];
      const values: any[] = [];

      if (updateProfileDto.username !== undefined) {
        updates.push('username = ?');
        values.push(updateProfileDto.username);
      }

      if (updateProfileDto.firstName !== undefined) {
        updates.push('first_name = ?');
        values.push(updateProfileDto.firstName);
      }

      if (updateProfileDto.lastName !== undefined) {
        updates.push('last_name = ?');
        values.push(updateProfileDto.lastName);
      }

      if (updateProfileDto.phone !== undefined) {
        updates.push('phone = ?');
        values.push(updateProfileDto.phone);
      }

      if (updateProfileDto.avatarUrl !== undefined) {
        updates.push('avatar_url = ?');
        values.push(updateProfileDto.avatarUrl);
      }

      if (updateProfileDto.preferences !== undefined) {
        try {
          // Garantir que preferences seja um JSON válido
          const preferencesJson = typeof updateProfileDto.preferences === 'string'
            ? JSON.parse(updateProfileDto.preferences)
            : updateProfileDto.preferences;

          this.logger.debug(`Processando preferences: ${JSON.stringify(preferencesJson)}`);

          updates.push('preferences = ?');
          values.push(preferencesJson);
        } catch (error) {
          this.logger.error(`Erro ao processar preferences: ${error.message}`, error.stack);
          throw new BadRequestException('Formato inválido para o campo preferences');
        }
      }

      if (updateProfileDto.isActive !== undefined) {
        updates.push('is_active = ?');
        values.push(updateProfileDto.isActive);
      }

      if (updates.length === 0) {
        throw new BadRequestException('Nenhum campo fornecido para atualização');
      }

      updates.push('updated_at = NOW()');

      // Construir a consulta de atualização
      const updateQuery = `
        UPDATE profiles
        SET ${updates.join(', ')}
        WHERE id = $${values.length + 1}::uuid
        RETURNING *
      `;

      // Executar a atualização
      try {
        this.logger.debug(`Executando query: ${updateQuery}`);
        this.logger.debug(`Valores: ${JSON.stringify(values)}`);
        await this.prisma.$executeRawUnsafe(updateQuery, ...values, userId);
      } catch (dbError) {
        this.logger.error(`Erro na execução da query: ${dbError.message}`, dbError.stack);
        throw new BadRequestException(`Erro ao atualizar perfil: ${dbError.message}`);
      }

      // Buscar o usuário atualizado
      const updatedUser = await this.queryUtil.findUserById(userId);
      return mapToUserProfileDto(updatedUser);
    } catch (error) {
      this.logger.error(`Erro ao atualizar perfil: ${error.message}`, error.stack);
      throw error;
    }
  }
}