import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Length,
  MinLength,
  MaxLength,
  Validate,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { BrazilianStateValidator } from '../validators/zip-code.validator';

export class SearchAddressDto {
  @ApiProperty({
    description: 'Logradouro (rua, avenida, etc.)',
    example: 'Avenida Paulista',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'O logradouro deve ter pelo menos 3 caracteres' })
  @MaxLength(255, { message: 'O logradouro deve ter no máximo 255 caracteres' })
  @Transform(({ value }) => value.trim())
  street: string;

  @ApiProperty({
    description: 'Cidade',
    example: 'São Paulo',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'A cidade deve ter pelo menos 2 caracteres' })
  @MaxLength(100, { message: 'A cidade deve ter no máximo 100 caracteres' })
  @Transform(({ value }) => value.trim())
  city: string;

  @ApiProperty({
    description: 'Estado (UF)',
    example: 'SP',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  @Validate(BrazilianStateValidator)
  @Transform(({ value }) => value.toUpperCase().trim())
  state: string;
}
