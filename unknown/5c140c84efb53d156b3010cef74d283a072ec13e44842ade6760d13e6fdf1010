import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('health')
@Controller('health')
export class HealthController {
  private startTime: Date;

  constructor() {
    this.startTime = new Date();
  }

  @Get()
  @ApiOperation({ summary: 'Verificar status da API' })
  @ApiResponse({
    status: 200,
    description: 'API funcionando corretamente',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        message: { type: 'string', example: 'API funcionando corretamente' },
        timestamp: { type: 'string', example: '2025-03-27T18:00:00.000Z' },
        uptime: { type: 'string', example: '2h 30m 15s' },
        version: { type: 'string', example: '1.0.0' },
      },
    },
  })
  checkHealth() {
    const now = new Date();
    const uptime = this.formatUptime(now.getTime() - this.startTime.getTime());

    return {
      status: 'ok',
      message: 'API funcionando corretamente',
      timestamp: now.toISOString(),
      uptime,
      version: process.env.npm_package_version || '1.0.0',
    };
  }

  private formatUptime(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    const remainingHours = hours % 24;
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;

    let uptime = '';
    if (days > 0) uptime += `${days}d `;
    if (remainingHours > 0 || days > 0) uptime += `${remainingHours}h `;
    if (remainingMinutes > 0 || remainingHours > 0 || days > 0)
      uptime += `${remainingMinutes}m `;
    uptime += `${remainingSeconds}s`;

    return uptime;
  }
}
