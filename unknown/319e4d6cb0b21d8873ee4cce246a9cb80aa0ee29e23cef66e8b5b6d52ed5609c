import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class RoleDto {
  @ApiProperty({
    description: 'ID único do papel',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({ description: 'Nome do papel', example: 'Administrador' })
  name: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de sistema',
    example: true,
    default: false,
  })
  isSystemRole: boolean;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateRoleDto {
  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({ description: 'Nome do papel', example: 'Administrador' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de sistema',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isSystemRole?: boolean;
}

export class UpdateRoleDto {
  @ApiProperty({
    description: 'Nome do papel',
    example: 'Administrador',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de sistema',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isSystemRole?: boolean;
}
