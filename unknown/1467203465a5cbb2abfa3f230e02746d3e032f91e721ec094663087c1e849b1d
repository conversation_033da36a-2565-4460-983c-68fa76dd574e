# FluxoMax

## Running the Project with Docker

This section provides updated instructions to set up and run the project using Docker. Follow the steps below to ensure a smooth setup process.

## Prerequisites

- Ensure Docker and Docker Compose are installed on your system.
- Verify the following versions are available:
  - Node.js: 22.13.1 (used in the backend Dockerfile)
  - Bun: 1.2 (used in the frontend Dockerfile)

## Environment Variables

- Backend service:
  - Located in `./backend/.env`.
  - Ensure the file contains the necessary configurations.
- Database service:
  - Environment variables are defined in the `docker-compose.yml` file:
    - `POSTGRES_USER`: postgres
    - `POSTGRES_PASSWORD`: postgres
    - `POSTGRES_DB`: fluxomax

## Build and Run Instructions

1. Navigate to the project's root directory.
2. Execute the following command to build and start the services:

   ```bash
   docker compose up --build
   ```

3. Access the services via the following ports:
   - Backend: [http://localhost:3000](http://localhost:3000)
   - Frontend: [http://localhost:3001](http://localhost:3001)

## Special Configuration

- The backend service depends on the database service; ensure the database is running before accessing the backend.
- The frontend service depends on the backend service; ensure the backend is running before accessing the frontend.

## Exposed Ports

- Backend: 3000
- Frontend: 3001
- Database: 5432

For further details, refer to the respective `Dockerfile` and `docker-compose.yml` files.

## Development Environment with Hot-Reloading

For development purposes, a special environment has been set up with volume mounting and hot-reloading capabilities. This allows you to make changes to the code and see them reflected immediately without having to rebuild the Docker containers.

### Features

- **Volume Mounting**: Source code directories are mounted into the containers, allowing real-time code changes to be reflected.
- **Nodemon**: Backend uses Nodemon to automatically restart the server when code changes are detected.
- **Vite Dev Server**: Frontend uses Vite's development server with hot module replacement (HMR).

### Using the Development Environment

1. Navigate to the project's root directory.
2. Use the provided development script to manage the environment:

   ```bash
   # Iniciar o ambiente de desenvolvimento
   ./dev.sh start

   # Parar o ambiente de desenvolvimento
   ./dev.sh stop

   # Reiniciar o ambiente de desenvolvimento
   ./dev.sh restart

   # Reconstruir e iniciar o ambiente de desenvolvimento
   ./dev.sh rebuild

   # Ver logs de um serviço específico (backend, frontend ou database)
   ./dev.sh logs <service>
   ```

3. Acesse os serviços através das seguintes portas:
   - Backend: [http://localhost:3000](http://localhost:3000)
   - Frontend: [http://localhost:3001](http://localhost:3001)
   - Documentação da API (Swagger): [http://localhost:3000/api/docs](http://localhost:3000/api/docs)

### Hot-Reloading

O ambiente de desenvolvimento está configurado para atualização automática do código:

- **Backend**: Qualquer alteração nos arquivos `.ts` ou `.json` dentro da pasta `backend/src` será detectada pelo Nodemon, que reiniciará automaticamente o servidor.

- **Frontend**: Qualquer alteração nos arquivos dentro da pasta `frontend/src` será detectada pelo Vite, que atualizará automaticamente a interface sem necessidade de recarregar a página inteira (Hot Module Replacement).
   # Make the script executable (first time only)
   chmod +x ./dev.sh

   # Start the development environment
   ./dev.sh start

   # View logs from all services
   ./dev.sh logs

   # View logs from a specific service
   ./dev.sh logs:backend
   ./dev.sh logs:frontend
   ./dev.sh logs:db

   # Rebuild and restart the environment
   ./dev.sh rebuild

   # Stop the development environment
   ./dev.sh stop
   ```

3. Access the services via the following ports:
   - Backend: [http://localhost:3000](http://localhost:3000)
   - Frontend: [http://localhost:3001](http://localhost:3001)

### How It Works

- The development environment uses `docker-compose.dev.yaml` which includes volume mounts for both frontend and backend.
- Backend changes trigger automatic server restarts via Nodemon.
- Frontend changes trigger hot module replacement via Vite's dev server.
- Database data is persisted in a Docker volume, just like in the production setup.

### Working with Prisma

When making changes to the Prisma schema (`backend/prisma/schema.prisma`), you need to generate migrations and update the Prisma Client:

```bash
# Generate a migration (replace 'migration_name' with a descriptive name)
./dev.sh exec:backend npx prisma migrate dev --name migration_name

# Apply migrations to the database
./dev.sh exec:backend npx prisma migrate deploy

# Generate Prisma Client after schema changes
./dev.sh exec:backend npx prisma generate
```

Alternatively, you can use the provided script to regenerate the Prisma Client:

```bash
# Make the script executable (first time only)
chmod +x ./regenerate-prisma.sh

# Run the script to regenerate Prisma Client
./regenerate-prisma.sh
```

For more details, refer to `Dockerfile.dev` files in the backend and frontend directories and the `docker-compose.dev.yaml` file in the project root.
