// backend/src/routes/entities/entity-addresses.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Req,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { EntityAddressesService } from './entity-addresses.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { EntitiesRolesGuard } from './entities-roles.guard';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import { CreateEntityAddressDto } from './dto/create-entity-address.dto';
import { UpdateEntityAddressDto } from './dto/update-entity-address.dto';
import { AddressDto } from '../../models/address.model';

@ApiTags('entity-addresses')
@ApiResponse({ status: 401, description: 'Não autorizado' })
@Controller('entities/:entityId/addresses')
@UseGuards(JwtAuthGuard, EntitiesRolesGuard)
export class EntityAddressesController {
  constructor(private readonly entityAddressesService: EntityAddressesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.SALES_MANAGER, Role.PURCHASING_MANAGER)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um endereço para uma entidade' })
  @ApiParam({ name: 'entityId', description: 'ID da entidade' })
  @ApiBody({ type: CreateEntityAddressDto })
  @ApiResponse({
    status: 201,
    description: 'Endereço criado com sucesso',
    type: AddressDto
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 404, description: 'Entidade não encontrada' })
  create(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Body() createAddressDto: CreateEntityAddressDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entityAddressesService.createEntityAddress(entityId, createAddressDto, user);
  }

  @Get()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os endereços de uma entidade' })
  @ApiParam({ name: 'entityId', description: 'ID da entidade' })
  @ApiResponse({
    status: 200,
    description: 'Lista de endereços da entidade',
    type: [AddressDto]
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 404, description: 'Entidade não encontrada' })
  findAll(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entityAddressesService.findAddressesByEntityId(entityId, user);
  }

  @Get(':addressId')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um endereço específico de uma entidade' })
  @ApiParam({ name: 'entityId', description: 'ID da entidade' })
  @ApiParam({ name: 'addressId', description: 'ID do endereço' })
  @ApiResponse({
    status: 200,
    description: 'Endereço encontrado',
    type: AddressDto
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 404, description: 'Entidade ou endereço não encontrado' })
  findOne(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Param('addressId', ParseUUIDPipe) addressId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entityAddressesService.findEntityAddressById(addressId, entityId, user);
  }

  @Put(':addressId')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE,
         Role.FINANCE_MANAGER, Role.SALES_MANAGER, Role.PURCHASING_MANAGER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um endereço de uma entidade' })
  @ApiParam({ name: 'entityId', description: 'ID da entidade' })
  @ApiParam({ name: 'addressId', description: 'ID do endereço' })
  @ApiBody({ type: UpdateEntityAddressDto })
  @ApiResponse({
    status: 200,
    description: 'Endereço atualizado com sucesso',
    type: AddressDto
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 404, description: 'Entidade ou endereço não encontrado' })
  update(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Param('addressId', ParseUUIDPipe) addressId: string,
    @Body() updateAddressDto: UpdateEntityAddressDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entityAddressesService.updateEntityAddress(addressId, entityId, updateAddressDto, user);
  }

  @Delete(':addressId')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um endereço de uma entidade' })
  @ApiParam({ name: 'entityId', description: 'ID da entidade' })
  @ApiParam({ name: 'addressId', description: 'ID do endereço' })
  @ApiResponse({ status: 204, description: 'Endereço removido com sucesso' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  @ApiResponse({ status: 404, description: 'Entidade ou endereço não encontrado' })
  remove(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Param('addressId', ParseUUIDPipe) addressId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entityAddressesService.removeEntityAddress(addressId, entityId, user);
  }
}
