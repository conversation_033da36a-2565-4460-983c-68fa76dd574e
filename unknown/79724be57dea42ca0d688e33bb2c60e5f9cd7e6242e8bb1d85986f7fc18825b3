
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface TransactionActionsProps {
  onNewTransaction: () => void;
}

export default function TransactionActions({ 
  onNewTransaction
}: TransactionActionsProps) {
  return (
    <div className="flex gap-2">
      <Button onClick={onNewTransaction} className="bg-[#007FFF] hover:bg-[#0066CC] text-white">
        <Plus className="mr-2 h-4 w-4" />
        Nova Transação
      </Button>
    </div>
  );
}
