import { Module } from '@nestjs/common';
import { UsersController } from '../controllers/users.controller';
import { UsersService } from '../services/users.service';
import { PrismaService } from '../services/prisma.service';
import { UserManagerService } from '../services/user-manager.service';
import { UserQueryService } from '../services/user-query.service';
import { UserProfileService } from '../services/user-profile.service';
import { UtilsModule } from '../utils/utils.module';

@Module({
  imports: [UtilsModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    UserManagerService,
    UserQueryService, 
    UserProfileService,
    PrismaService
  ],
  exports: [
    UsersService,
    UserManagerService,
    UserQueryService,
    UserProfileService
  ],
})
export class UsersModule {}
