import { Modu<PERSON> } from '@nestjs/common';
import { RolesController } from './roles.controller';
import { RolesService } from './roles.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { RolePermissionsController } from './role-permissions.controller';
import { RolePermissionsService } from './role-permissions.service';

@Module({
  imports: [PrismaModule],
  controllers: [RolesController, RolePermissionsController],
  providers: [RolesService, RolePermissionsService],
  exports: [RolesService, RolePermissionsService],
})
export class RolesModule {}
