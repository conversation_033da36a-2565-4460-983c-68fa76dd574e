import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, MapPin, Trash2 } from "lucide-react";
import { AddressSearchModal } from "@/components/address/AddressSearchModal";
import { AddressFormModal } from "@/components/address/AddressFormModal";

// Tipos de endereço predefinidos
const addressTypes = [
  { value: "main", label: "Principal" },
  { value: "billing", label: "Cobrança" },
  { value: "shipping", label: "Entrega" },
];

interface AddressFieldArrayProps {
  form: UseFormReturn<any>;
  name: string;
  companyId?: string;
}

export function AddressFieldArray({ form, name, companyId = "" }: AddressFieldArrayProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [currentAddressIndex, setCurrentAddressIndex] = useState<number | null>(null);

  // Acessa os endereços existentes no formulário
  const addresses = form.watch(name) || [];

  // Adiciona um novo endereço vazio
  const addEmptyAddress = () => {
    const updatedAddresses = [
      ...addresses,
      {
        address_id: "",
        address_type: "main",
        street: "",
        number: "",
        complement: "",
        neighborhood: "",
        city: "",
        state: "",
        zip_code: "",
      },
    ];
    form.setValue(name, updatedAddresses, { shouldValidate: true });
  };

  // Remove um endereço específico
  const removeAddress = (index: number) => {
    const updatedAddresses = [...addresses];
    updatedAddresses.splice(index, 1);
    form.setValue(name, updatedAddresses, { shouldValidate: true });
  };

  // Abre o modal de pesquisa para um endereço específico
  const openAddressSearch = (index: number) => {
    setCurrentAddressIndex(index);
    setIsSearchOpen(true);
  };

  // Seleciona um endereço da pesquisa
  const handleSelectAddress = (selectedAddress: any) => {
    if (currentAddressIndex !== null) {
      const currentAddresses = [...addresses];
      
      // Atualiza com os dados do endereço selecionado, mantendo o número e complemento
      currentAddresses[currentAddressIndex] = {
        ...currentAddresses[currentAddressIndex],
        address_id: selectedAddress.id,
        street: selectedAddress.street,
        neighborhood: selectedAddress.neighborhood,
        city: selectedAddress.city,
        state: selectedAddress.state,
        zip_code: selectedAddress.zip_code,
      };
      
      form.setValue(name, currentAddresses, { shouldValidate: true });
    }
    setIsSearchOpen(false);
  };

  // Cria novo endereço e abre o formulário
  const handleCreateNewAddress = () => {
    setIsSearchOpen(false);
    setIsFormOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium">Endereços</h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addEmptyAddress}
          className="gap-2"
        >
          <Plus className="h-4 w-4" />
          Adicionar Endereço
        </Button>
      </div>

      {addresses.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-md">
          <MapPin className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            Nenhum endereço cadastrado. Clique em "Adicionar Endereço" para começar.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {addresses.map((address: any, index: number) => (
            <div key={index} className="border rounded-md p-4 relative">
              {/* Botão para remover endereço */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => removeAddress(index)}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {/* Tipo de endereço */}
                <FormField
                  control={form.control}
                  name={`${name}.${index}.address_type`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Endereço</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {addressTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Pesquisa de Endereço */}
                <FormItem>
                  <FormLabel>Endereço</FormLabel>
                  <FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full justify-start text-left"
                      onClick={() => openAddressSearch(index)}
                    >
                      {address.street ? (
                        <div className="truncate">
                          {address.street}, {address.neighborhood} - {address.city}/{address.state}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Buscar endereço...</span>
                      )}
                    </Button>
                  </FormControl>
                </FormItem>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* CEP (readonly) */}
                <FormField
                  control={form.control}
                  name={`${name}.${index}.zip_code`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CEP</FormLabel>
                      <FormControl>
                        <Input {...field} readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Número */}
                <FormField
                  control={form.control}
                  name={`${name}.${index}.number`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Ex: 123" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Complemento */}
                <FormField
                  control={form.control}
                  name={`${name}.${index}.complement`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Complemento</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Apto, Sala, etc." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Campos invisíveis para manter os dados */}
              <input type="hidden" {...form.register(`${name}.${index}.street`)} />
              <input type="hidden" {...form.register(`${name}.${index}.neighborhood`)} />
              <input type="hidden" {...form.register(`${name}.${index}.city`)} />
              <input type="hidden" {...form.register(`${name}.${index}.state`)} />
              <input type="hidden" {...form.register(`${name}.${index}.address_id`)} />
            </div>
          ))}
        </div>
      )}

      {/* Modal de busca de endereços */}
      <AddressSearchModal
        open={isSearchOpen}
        onOpenChange={setIsSearchOpen}
        onSelect={handleSelectAddress}
        onCreateNew={handleCreateNewAddress}
      />

      {/* Modal de cadastro de novo endereço */}
      <AddressFormModal
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        onSave={(address) => {
          handleSelectAddress(address);
          setIsFormOpen(false);
        }}
        companyId={companyId}
      />
    </div>
  );
}
