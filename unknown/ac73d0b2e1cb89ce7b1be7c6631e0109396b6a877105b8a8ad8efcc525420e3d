import { useState, useEffect, useMemo } from "react";
import { usePostalCodeService, PostalCode } from "@/hooks/usePostalCodeService";
import { PostalCodeModal } from "@/components/postal-code/PostalCodeModal";
import { DeleteConfirmationDialog } from "@/components/postal-code/DeleteConfirmationDialog";
import { ImportCSVDialog } from "@/components/postal-code/ImportCSVDialog";
import { toast } from "@/hooks/use-toast";
import { Layout } from "@/components/layout/Layout";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  FileText,
  Plus,
  Search,
  Edit,
  Trash2,
  MoreVertical,
  FileUp,
  ArrowUpDown,
  ChevronDown,
  X
} from "lucide-react";

const ITEMS_PER_PAGE = 10;

// Brazilian states for filtering
const brazilianStates = [
  { value: 'AC', label: 'Acre' },
  { value: 'AL', label: 'Alagoas' },
  { value: 'AP', label: 'Amapá' },
  { value: 'AM', label: 'Amazonas' },
  { value: 'BA', label: 'Bahia' },
  { value: 'CE', label: 'Ceará' },
  { value: 'DF', label: 'Distrito Federal' },
  { value: 'ES', label: 'Espírito Santo' },
  { value: 'GO', label: 'Goiás' },
  { value: 'MA', label: 'Maranhão' },
  { value: 'MT', label: 'Mato Grosso' },
  { value: 'MS', label: 'Mato Grosso do Sul' },
  { value: 'MG', label: 'Minas Gerais' },
  { value: 'PA', label: 'Pará' },
  { value: 'PB', label: 'Paraíba' },
  { value: 'PR', label: 'Paraná' },
  { value: 'PE', label: 'Pernambuco' },
  { value: 'PI', label: 'Piauí' },
  { value: 'RJ', label: 'Rio de Janeiro' },
  { value: 'RN', label: 'Rio Grande do Norte' },
  { value: 'RS', label: 'Rio Grande do Sul' },
  { value: 'RO', label: 'Rondônia' },
  { value: 'RR', label: 'Roraima' },
  { value: 'SC', label: 'Santa Catarina' },
  { value: 'SP', label: 'São Paulo' },
  { value: 'SE', label: 'Sergipe' },
  { value: 'TO', label: 'Tocantins' }
];

export default function PostalCodesPage() {
  const postalCodeService = usePostalCodeService();
  const [postalCodes, setPostalCodes] = useState<PostalCode[]>([]);
  const [filteredPostalCodes, setFilteredPostalCodes] = useState<PostalCode[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [stateFilter, setStateFilter] = useState<string>("");
  const [originFilter, setOriginFilter] = useState<string>("");
  
  const [editingPostalCode, setEditingPostalCode] = useState<Partial<PostalCode> | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const [deletePostalCodeId, setDeletePostalCodeId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  
  const [sortConfig, setSortConfig] = useState<{
    key: keyof PostalCode;
    direction: 'ascending' | 'descending';
  } | null>(null);

  // Fetch postal codes on component mount
  useEffect(() => {
    fetchPostalCodes();
  }, []);

  // Apply filters when filter values change
  useEffect(() => {
    applyFilters();
  }, [searchTerm, stateFilter, originFilter, postalCodes, sortConfig]);

  const fetchPostalCodes = async () => {
    setIsLoading(true);
    try {
      const data = await postalCodeService.getPostalCodes();
      setPostalCodes(data);
      setFilteredPostalCodes(data);
    } catch (error) {
      console.error("Error fetching postal codes:", error);
      toast({
        title: "Erro",
        description: "Falha ao carregar os CEPs.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...postalCodes];
    
    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(pc => 
        pc.zip_code.toLowerCase().includes(term) ||
        pc.street.toLowerCase().includes(term) ||
        pc.neighborhood.toLowerCase().includes(term) ||
        pc.city.toLowerCase().includes(term)
      );
    }
    
    // Apply state filter
    if (stateFilter) {
      filtered = filtered.filter(pc => pc.state === stateFilter);
    }
    
    // Apply origin filter
    if (originFilter) {
      filtered = filtered.filter(pc => pc.registration_origin === originFilter);
    }
    
    // Apply sorting
    if (sortConfig) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    
    setFilteredPostalCodes(filtered);
    // Reset to first page when filters change
    setCurrentPage(1);
  };

  const handleSort = (key: keyof PostalCode) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    
    if (sortConfig && sortConfig.key === key) {
      direction = sortConfig.direction === 'ascending' ? 'descending' : 'ascending';
    }
    
    setSortConfig({ key, direction });
  };

  const handleAddPostalCode = () => {
    setEditingPostalCode(null);
    setIsModalOpen(true);
  };

  const handleEditPostalCode = (postalCode: PostalCode) => {
    setEditingPostalCode(postalCode);
    setIsModalOpen(true);
  };

  const handleDeletePostalCode = (id: string) => {
    setDeletePostalCodeId(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!deletePostalCodeId) return;
    
    try {
      await postalCodeService.deletePostalCode(deletePostalCodeId);
      setPostalCodes(postalCodes.filter(pc => pc.id !== deletePostalCodeId));
      toast({
        title: "Sucesso",
        description: "CEP excluído com sucesso.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error deleting postal code:", error);
      toast({
        title: "Erro",
        description: "Falha ao excluir o CEP.",
        variant: "destructive"
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletePostalCodeId(null);
    }
  };

  const handleSavePostalCode = (savedPostalCode: PostalCode) => {
    // Check if we're updating or adding
    if (editingPostalCode?.id) {
      // Update existing postal code
      setPostalCodes(postalCodes.map(pc => 
        pc.id === savedPostalCode.id ? savedPostalCode : pc
      ));
    } else {
      // Add new postal code
      setPostalCodes([...postalCodes, savedPostalCode]);
    }
  };

  const handleImportCSV = async (csvContent: string) => {
    try {
      const result = await postalCodeService.importFromCSV(csvContent);
      
      // Refresh the postal codes list
      if (result.success > 0) {
        fetchPostalCodes();
      }
      
      return result;
    } catch (error) {
      console.error("Error importing CSV:", error);
      toast({
        title: "Erro",
        description: "Falha ao importar o arquivo CSV.",
        variant: "destructive"
      });
      return { success: 0, failed: 0 };
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStateFilter("");
    setOriginFilter("");
    setSortConfig(null);
  };

  // Paginate the data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredPostalCodes.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [filteredPostalCodes, currentPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredPostalCodes.length / ITEMS_PER_PAGE);

  // Generate page numbers for pagination
  const pageNumbers = useMemo(() => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are less than maxVisiblePages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show a subset of pages with ellipsis
      if (currentPage <= 3) {
        // Near the start
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push(0); // Represents ellipsis
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        pages.push(1);
        pages.push(0); // Represents ellipsis
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Somewhere in the middle
        pages.push(1);
        pages.push(0); // Represents ellipsis
        pages.push(currentPage - 1);
        pages.push(currentPage);
        pages.push(currentPage + 1);
        pages.push(0); // Represents ellipsis
        pages.push(totalPages);
      }
    }
    
    return pages;
  }, [currentPage, totalPages]);

  return (
    <Layout>
      <div className="container mx-auto py-6 max-w-7xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Códigos de Endereçamento Postal (CEPs)
          </h1>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
              <FileUp className="h-4 w-4 mr-2" />
              Importar CSV
            </Button>
            <Button onClick={handleAddPostalCode}>
              <Plus className="h-4 w-4 mr-2" />
              Novo CEP
            </Button>
          </div>
        </div>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Gerenciamento de CEPs</CardTitle>
            <CardDescription>
              Cadastre e gerencie códigos de endereçamento postal para uso no sistema.
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {/* Filters */}
            <div className="mb-6 space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar CEP, logradouro, bairro ou cidade..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Select value={stateFilter} onValueChange={setStateFilter}>
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="Estado" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos</SelectItem>
                      {brazilianStates.map((state) => (
                        <SelectItem key={state.value} value={state.value}>
                          {state.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select value={originFilter} onValueChange={setOriginFilter}>
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="Origem" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                      <SelectItem value="auto">Automático</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  {(searchTerm || stateFilter || originFilter) && (
                    <Button variant="outline" onClick={clearFilters} size="icon">
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
            
            {/* Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('zip_code')}>
                      <div className="flex items-center space-x-1">
                        <span>CEP</span>
                        {sortConfig?.key === 'zip_code' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('street')}>
                      <div className="flex items-center space-x-1">
                        <span>Logradouro</span>
                        {sortConfig?.key === 'street' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('neighborhood')}>
                      <div className="flex items-center space-x-1">
                        <span>Bairro</span>
                        {sortConfig?.key === 'neighborhood' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('city')}>
                      <div className="flex items-center space-x-1">
                        <span>Cidade</span>
                        {sortConfig?.key === 'city' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('state')}>
                      <div className="flex items-center space-x-1">
                        <span>Estado</span>
                        {sortConfig?.key === 'state' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('registration_origin')}>
                      <div className="flex items-center space-x-1">
                        <span>Origem</span>
                        {sortConfig?.key === 'registration_origin' && (
                          <ArrowUpDown className={`h-4 w-4 ${sortConfig.direction === 'ascending' ? 'rotate-180' : ''}`} />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center">
                          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                          <p className="mt-2 text-sm text-gray-500">Carregando CEPs...</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : paginatedData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10">
                        <p className="text-gray-500">Nenhum CEP encontrado</p>
                        {(searchTerm || stateFilter || originFilter) && (
                          <Button 
                            variant="link" 
                            onClick={clearFilters}
                            className="mt-2"
                          >
                            Limpar filtros
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedData.map((postalCode) => (
                      <TableRow key={postalCode.id}>
                        <TableCell className="font-medium">{postalCode.zip_code}</TableCell>
                        <TableCell>{postalCode.street}</TableCell>
                        <TableCell>{postalCode.neighborhood}</TableCell>
                        <TableCell>{postalCode.city}</TableCell>
                        <TableCell>{postalCode.state}</TableCell>
                        <TableCell>
                          <Badge variant={postalCode.registration_origin === 'auto' ? "secondary" : "outline"}>
                            {postalCode.registration_origin === 'auto' ? 'Automático' : 'Manual'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleEditPostalCode(postalCode)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => handleDeletePostalCode(postalCode.id)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination */}
            {filteredPostalCodes.length > 0 && (
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Mostrando {paginatedData.length} de {filteredPostalCodes.length} resultados
                </div>
                
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                    
                    {pageNumbers.map((pageNumber, index) => (
                      pageNumber === 0 ? (
                        <PaginationItem key={`ellipsis-${index}`}>
                          <div className="flex h-9 w-9 items-center justify-center">
                            <ChevronDown className="h-4 w-4 rotate-90" />
                          </div>
                        </PaginationItem>
                      ) : (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink
                            onClick={() => setCurrentPage(pageNumber)}
                            isActive={currentPage === pageNumber}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      )
                    ))}
                    
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Modal for adding/editing postal codes */}
      <PostalCodeModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSave={handleSavePostalCode}
        postalCode={editingPostalCode || undefined}
      />
      
      {/* Confirmation dialog for deleting postal codes */}
      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Excluir CEP"
        description="Tem certeza que deseja excluir este CEP? Esta ação não pode ser desfeita."
      />
      
      {/* Dialog for importing CSV */}
      <ImportCSVDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        onImport={handleImportCSV}
      />
    </Layout>
  );
}
