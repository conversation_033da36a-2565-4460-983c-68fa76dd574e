import React, { useState, useEffect } from 'react';
import { usePaginatedApi } from '@/hooks/usePaginatedApi';
import { useFilters } from '@/hooks/useFilters';
import { DataTable } from '@/components/ui/data-table';
import { AdvancedFilters } from '@/components/filter/AdvancedFilters';
import { QuickFilters } from '@/components/filter/QuickFilters';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

// Importar componentes extraídos
import { TransactionListItem } from '@/types/transaction';
import { quickFilters, transactionFilters } from '@/components/transactions/constants/transactionFilters';
import { getTransactionColumns } from '@/components/transactions/TransactionColumns';
import { TransactionSummaryCards } from '@/components/transactions/TransactionSummaryCards';
import { TransactionListHeader } from '@/components/transactions/TransactionListHeader';
import { TransactionTabContent } from '@/components/transactions/TransactionTabContent';

export default function TransactionList() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  
  // Configuração dos filtros
  const {
    filters,
    setFilter,
    setFilters,
    clearFilters,
    hasActiveFilters,
    getActiveFilterCount,
    getApiParams,
  } = useFilters({
    filters: transactionFilters,
    syncWithUrl: true,
  });
  
  // Configuração da paginação e busca de dados
  const {
    data,
    loading,
    error,
    params,
    goToPage,
    setLimit,
    setSorting,
    setSearch,
    setFilters: setPaginationFilters,
    refetch,
  } = usePaginatedApi<TransactionListItem>('/transactions', {
    initialParams: {
      page: 1,
      limit: 10,
      sortBy: 'date',
      sortOrder: 'desc',
    },
    revalidateOnFocus: true,
    onError: (err) => {
      toast.error('Erro ao carregar transações');
      console.error('Erro ao carregar transações:', err);
    },
  });
  
  // Aplicar filtros à paginação
  const applyFilters = () => {
    const apiParams = getApiParams();
    setPaginationFilters({ filters: apiParams });
  };
  
  // Efeito para aplicar o filtro de tab ativa
  useEffect(() => {
    if (activeTab === 'all') {
      setFilter('type', null);
    } else if (activeTab === 'income') {
      setFilter('type', 'INCOME');
    } else if (activeTab === 'expense') {
      setFilter('type', 'EXPENSE');
    }
    
    applyFilters();
  }, [activeTab]);
  
  // Obter colunas para a tabela
  const columns = getTransactionColumns();
  
  // Função para criar nova transação
  const handleCreateTransaction = () => {
    navigate('/transactions/new');
  };
  
  // Função para visualizar detalhes da transação
  const handleRowClick = (transaction: TransactionListItem) => {
    navigate(`/transactions/${transaction.id}`);
  };
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <TransactionListHeader
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        getActiveFilterCount={getActiveFilterCount}
        getApiParams={getApiParams}
        activeTab={activeTab}
        onCreateTransaction={handleCreateTransaction}
      />
      
      <QuickFilters
        filters={quickFilters}
        values={{
          status: filters.status,
          description: filters.description,
        }}
        onFilterChange={setFilter}
        onClearFilters={clearFilters}
        onSearch={applyFilters}
        searchLabel="Buscar"
        className="mb-6"
      />
      
      <TransactionTabContent
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        data={data}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {showFilters && (
          <div className="md:col-span-1">
            <AdvancedFilters
              filters={transactionFilters}
              filterState={filters}
              onFilterChange={setFilter}
              onClearFilters={clearFilters}
              onApplyFilters={applyFilters}
              activeFilterCount={getActiveFilterCount()}
              title="Filtros de Transações"
            />
          </div>
        )}
        
        <div className={showFilters ? 'md:col-span-3' : 'md:col-span-4'}>
          <DataTable
            columns={columns}
            data={data}
            loading={loading}
            onPageChange={goToPage}
            onItemsPerPageChange={setLimit}
            onSortChange={setSorting}
            onSearchChange={setSearch}
            sortBy={params.sortBy}
            sortOrder={params.sortOrder}
            searchPlaceholder="Pesquisar transações..."
            noDataMessage="Nenhuma transação encontrada"
            onRowClick={handleRowClick}
            showSearch={true}
          />
        </div>
      </div>
    </div>
  );
}
