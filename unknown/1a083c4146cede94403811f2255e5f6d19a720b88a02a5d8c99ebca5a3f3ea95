import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import monitoringService from '@/services/api/monitoringService';
import api from '@/services/api/axios';
import { useApiError } from './useApiError';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  etag?: string;
}

interface ApiCacheOptions {
  cacheDuration?: number; // Duração do cache em milissegundos
  revalidateOnFocus?: boolean; // Revalidar quando a janela ganhar foco
  revalidateOnReconnect?: boolean; // Revalidar quando a conexão for restabelecida
  retryCount?: number; // Número de tentativas em caso de falha
  retryDelay?: number; // Atraso entre tentativas em milissegundos
  onError?: (error: any) => void; // Callback para tratamento de erro personalizado
  errorMessage?: string; // Mensagem de erro personalizada
  staleWhileRevalidate?: boolean; // Retornar dados obsoletos enquanto revalida
  cacheKey?: string; // Chave de cache personalizada
  enabled?: boolean; // Se o hook está habilitado
}

const DEFAULT_OPTIONS: ApiCacheOptions = {
  cacheDuration: 5 * 60 * 1000, // 5 minutos
  revalidateOnFocus: true,
  revalidateOnReconnect: true,
  retryCount: 3,
  retryDelay: 1000,
  errorMessage: 'Falha ao carregar dados. Tente novamente mais tarde.',
  staleWhileRevalidate: true,
  enabled: true
};

// Função para limpar o cache expirado periodicamente
const cleanupCache = () => {
  const now = Date.now();
  const expiredKeys: string[] = [];
  
  globalCache.forEach((item, key) => {
    if (item.expiresAt < now) {
      expiredKeys.push(key);
    }
  });
  
  if (expiredKeys.length > 0) {
    expiredKeys.forEach(key => globalCache.delete(key));
    monitoringService.recordEvent('cache_cleanup', { expiredCount: expiredKeys.length });
  }
};

// Configurar limpeza periódica do cache
setInterval(cleanupCache, 60 * 1000); // Limpar a cada minuto

// Cache global para compartilhar entre instâncias do hook
const globalCache = new Map<string, CacheItem<any>>();

// Mapa para rastrear requisições pendentes e evitar duplicação
const pendingRequests = new Map<string, Promise<any>>();

export function useApiCache<T>(
  key: string,
  fetchFn: () => Promise<T>,
  options: ApiCacheOptions = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);
  const { handleApiError } = useApiError();
  
  // Usar useRef para armazenar callbacks
  const callbacksRef = useRef({ onError: options.onError });
  
  // Atualizar callbacks quando mudam
  useEffect(() => {
    callbacksRef.current = { onError: options.onError };
  }, [options.onError]);

  // Mesclar opções padrão com as opções fornecidas
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  
  // Usar a chave personalizada ou a chave fornecida
  const cacheKeyToUse = mergedOptions.cacheKey || key;

  // Verificar se o cache está expirado
  const isCacheExpired = useCallback((cacheItem: CacheItem<T> | undefined): boolean => {
    if (!cacheItem) return true;
    return Date.now() > cacheItem.expiresAt;
  }, []);

  // Função para buscar dados
  const fetchData = useCallback(async (force = false): Promise<void> => {
    // Verificar se o hook está habilitado
    if (!mergedOptions.enabled) {
      return;
    }
    
    // Registrar evento de início da requisição
    monitoringService.recordEvent('api_request_start', { key: cacheKeyToUse, force });
    
    // Verificar se já existe uma requisição pendente
    if (!force && pendingRequests.has(cacheKeyToUse)) {
      monitoringService.recordEvent('api_request_deduped', { key: cacheKeyToUse });
      try {
        const result = await pendingRequests.get(cacheKeyToUse);
        setData(result);
      } catch (error) {
        // Erro já será tratado pela requisição original
      }
      return;
    }
    
    // Verificar se já temos dados em cache e se não estão expirados
    const cachedData = globalCache.get(cacheKeyToUse) as CacheItem<T> | undefined;
    
    if (!force && cachedData && !isCacheExpired(cachedData)) {
      setData(cachedData.data);
      monitoringService.recordEvent('api_cache_hit', { key: cacheKeyToUse });
      return;
    }

    // Se temos dados obsoletos e staleWhileRevalidate está ativado
    if (mergedOptions.staleWhileRevalidate && cachedData && !loading) {
      setData(cachedData.data);
      setIsValidating(true);
      monitoringService.recordEvent('api_stale_data_used', { key: cacheKeyToUse });
    } else {
      setLoading(true);
    }
    
    setError(null);

    // Criar a promessa da requisição
    const requestPromise = (async () => {
      try {
        const startTime = Date.now();
        const result = await fetchFn();
        const duration = Date.now() - startTime;
        
        // Armazenar no cache
        const cacheItem: CacheItem<T> = {
          data: result,
          timestamp: Date.now(),
          expiresAt: Date.now() + (mergedOptions.cacheDuration || 0)
        };
        
        globalCache.set(cacheKeyToUse, cacheItem);
        setData(result);
        setRetryCount(0);
        
        monitoringService.recordEvent('api_request_success', { 
          key: cacheKeyToUse, 
          duration,
          dataSize: JSON.stringify(result).length
        });
        
        return result;
      } catch (err: any) {
        console.error(`Erro ao buscar dados para ${cacheKeyToUse}:`, err);
        
        // Tentar novamente se ainda não atingiu o limite de tentativas
        if (retryCount < (mergedOptions.retryCount || 0)) {
          setRetryCount(prev => prev + 1);
          monitoringService.recordEvent('api_request_retry', { 
            key: cacheKeyToUse, 
            attempt: retryCount + 1,
            maxAttempts: mergedOptions.retryCount
          });
          
          setTimeout(() => {
            fetchData(force);
          }, mergedOptions.retryDelay);
          return cachedData?.data || null;
        }
        
        // Garantir que o erro seja uma instância de Error
        const errorObj = err instanceof Error ? err : new Error(err?.message || 'Erro desconhecido');
        const handledError = handleApiError(errorObj);
        setError(handledError);
        
        // Chamar callback de erro personalizado, se fornecido
        if (callbacksRef.current.onError) {
          callbacksRef.current.onError(handledError);
        } else {
          // Exibir mensagem de erro padrão
          toast.error(mergedOptions.errorMessage);
        }
        
        monitoringService.recordEvent('api_request_error', { 
          key: cacheKeyToUse, 
          error: handledError.message,
          status: err.response?.status
        });
        
        throw handledError;
      } finally {
        setLoading(false);
        setIsValidating(false);
        
        // Remover da lista de requisições pendentes
        setTimeout(() => {
          pendingRequests.delete(cacheKeyToUse);
        }, 2000); // Deduplicar por 2 segundos
      }
    })();
    
    // Armazenar a promessa para deduplicar requisições
    pendingRequests.set(cacheKeyToUse, requestPromise);
  }, [cacheKeyToUse, fetchFn, isCacheExpired, retryCount, mergedOptions, loading, handleApiError]);

  // Função para invalidar o cache e forçar uma nova busca
  const invalidateCache = useCallback((): void => {
    globalCache.delete(cacheKeyToUse);
    monitoringService.recordEvent('cache_invalidated', { key: cacheKeyToUse });
    fetchData(true);
  }, [cacheKeyToUse, fetchData]);

  // Função para atualizar o cache sem fazer uma nova requisição
  const updateCache = useCallback((updater: (oldData: T | null) => T): void => {
    const newData = updater(data);
    
    if (newData) {
      const cacheItem: CacheItem<T> = {
        data: newData,
        timestamp: Date.now(),
        expiresAt: Date.now() + (mergedOptions.cacheDuration || 0)
      };
      
      globalCache.set(cacheKeyToUse, cacheItem);
      setData(newData);
      monitoringService.recordEvent('cache_updated', { key: cacheKeyToUse });
    }
  }, [cacheKeyToUse, data, mergedOptions.cacheDuration]);

  // Efeito para buscar dados na montagem do componente
  useEffect(() => {
    if (mergedOptions.enabled) {
      fetchData();
    }
  }, [fetchData, mergedOptions.enabled]);

  // Efeito para revalidar quando a janela ganhar foco
  useEffect(() => {
    if (!mergedOptions.revalidateOnFocus || !mergedOptions.enabled) return;
    
    const handleFocus = () => {
      // Só revalidar se os dados estiverem obsoletos
      const cachedData = globalCache.get(cacheKeyToUse) as CacheItem<T> | undefined;
      if (!cachedData || isCacheExpired(cachedData)) {
        monitoringService.recordEvent('api_revalidate_on_focus', { key: cacheKeyToUse });
        fetchData();
      }
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [fetchData, mergedOptions.revalidateOnFocus, mergedOptions.enabled, cacheKeyToUse, isCacheExpired]);

  // Efeito para revalidar quando a conexão for restabelecida
  useEffect(() => {
    if (!mergedOptions.revalidateOnReconnect || !mergedOptions.enabled) return;
    
    const handleOnline = () => {
      monitoringService.recordEvent('api_revalidate_on_reconnect', { key: cacheKeyToUse });
      fetchData(true); // Forçar revalidação ao reconectar
    };
    
    window.addEventListener('online', handleOnline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [fetchData, mergedOptions.revalidateOnReconnect, mergedOptions.enabled, cacheKeyToUse]);

  return {
    data,
    loading,
    error,
    isValidating,
    refetch: () => fetchData(true),
    invalidateCache,
    updateCache
  };
}
