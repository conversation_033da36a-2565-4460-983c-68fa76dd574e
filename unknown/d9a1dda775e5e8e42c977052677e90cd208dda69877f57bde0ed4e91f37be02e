import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../../services/prisma.service';
import { ZipCodeDto, CreateZipCodeDto, SearchAddressDto } from './dto';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { Prisma } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

interface ViaCepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
  erro?: boolean;
}

@Injectable()
export class ZipCodesService {
  private readonly cacheTtlDays: number;

  constructor(
    private prisma: PrismaService,
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    // Obter TTL da configuração ou usar valor padrão de 30 dias
    this.cacheTtlDays = this.configService.get<number>('ZIP_CODE_CACHE_TTL_DAYS') || 30;
  }

  /**
   * Calcula a data de expiração do cache
   */
  private calculateExpirationDate(): Date {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + this.cacheTtlDays);
    return expirationDate;
  }

  /**
   * Verifica se um cache está expirado
   */
  private isCacheExpired(expiresAt: Date | null): boolean {
    if (!expiresAt) return false; // Registros manuais não expiram

    const now = new Date();
    return expiresAt < now;
  }

  /**
   * Formata um CEP para o padrão XXXXX-XXX
   */
  private formatZipCode(zipCode: string): string {
    // Remove caracteres não numéricos
    const cleanZipCode = zipCode.replace(/\D/g, '');

    // Formata para o padrão XXXXX-XXX se tiver 8 dígitos
    if (cleanZipCode.length === 8) {
      return `${cleanZipCode.substring(0, 5)}-${cleanZipCode.substring(5)}`;
    }

    // Retorna o CEP original se não for possível formatar
    return zipCode;
  }

  /**
   * Busca um CEP no banco de dados local
   */
  private async findInLocalDatabase(zipCode: string): Promise<ZipCodeDto | null> {
    const formattedZipCode = this.formatZipCode(zipCode);

    const zipCodeData = await this.prisma.zipCode.findUnique({
      where: { zipCode: formattedZipCode },
    });

    if (!zipCodeData) {
      return null;
    }

    // Verifica se o cache está expirado (apenas para registros automáticos)
    if (zipCodeData.registrationOrigin === 'auto' && this.isCacheExpired(zipCodeData.expiresAt)) {
      // Se estiver expirado, remove o registro e retorna null para forçar uma nova busca
      await this.prisma.zipCode.delete({
        where: { zipCode: formattedZipCode },
      });
      return null;
    }

    return {
      zipCode: zipCodeData.zipCode,
      street: zipCodeData.street,
      neighborhood: zipCodeData.neighborhood,
      city: zipCodeData.city,
      state: zipCodeData.state,
      registrationOrigin: zipCodeData.registrationOrigin,
      expiresAt: zipCodeData.expiresAt || undefined,
      createdAt: zipCodeData.createdAt,
      updatedAt: zipCodeData.updatedAt,
    };
  }

  /**
   * Busca um CEP na API externa (ViaCEP)
   */
  private async findInExternalApi(zipCode: string): Promise<ViaCepResponse | null> {
    const cleanZipCode = zipCode.replace(/\D/g, '');

    if (cleanZipCode.length !== 8) {
      return null;
    }

    try {
      const { data } = await firstValueFrom(
        this.httpService.get<ViaCepResponse>(`https://viacep.com.br/ws/${cleanZipCode}/json/`).pipe(
          catchError((error: AxiosError) => {
            console.error('Erro ao buscar CEP na API externa:', error.message);
            throw new Error('Falha ao buscar CEP na API externa');
          }),
        ),
      );

      // Verifica se a API retornou erro
      if (data.erro) {
        return null;
      }

      return data;
    } catch (error) {
      console.error('Erro ao buscar CEP na API externa:', error);
      return null;
    }
  }

  /**
   * Salva um CEP no banco de dados local
   */
  private async saveZipCode(zipCodeData: CreateZipCodeDto): Promise<ZipCodeDto> {
    const formattedZipCode = this.formatZipCode(zipCodeData.zipCode);

    try {
      // Definir data de expiração apenas para registros automáticos
      const expiresAt = zipCodeData.registrationOrigin === 'auto'
        ? this.calculateExpirationDate()
        : null;

      const savedZipCode = await this.prisma.zipCode.upsert({
        where: { zipCode: formattedZipCode },
        update: {
          street: zipCodeData.street,
          neighborhood: zipCodeData.neighborhood,
          city: zipCodeData.city,
          state: zipCodeData.state,
          registrationOrigin: zipCodeData.registrationOrigin,
          expiresAt: expiresAt,
          updatedAt: new Date(),
        },
        create: {
          zipCode: formattedZipCode,
          street: zipCodeData.street,
          neighborhood: zipCodeData.neighborhood,
          city: zipCodeData.city,
          state: zipCodeData.state,
          registrationOrigin: zipCodeData.registrationOrigin,
          expiresAt: expiresAt,
        },
      });

      return {
        zipCode: savedZipCode.zipCode,
        street: savedZipCode.street,
        neighborhood: savedZipCode.neighborhood,
        city: savedZipCode.city,
        state: savedZipCode.state,
        registrationOrigin: savedZipCode.registrationOrigin,
        expiresAt: savedZipCode.expiresAt || undefined,
        createdAt: savedZipCode.createdAt,
        updatedAt: savedZipCode.updatedAt,
      };
    } catch (error) {
      console.error('Erro ao salvar CEP no banco de dados:', error);
      throw new Error('Falha ao salvar CEP no banco de dados');
    }
  }

  /**
   * Busca um CEP, primeiro no banco local e depois na API externa
   */
  async findByCep(cep: string): Promise<ZipCodeDto> {
    // Limpa e formata o CEP
    const cleanZipCode = cep.replace(/\D/g, '');

    if (cleanZipCode.length !== 8) {
      throw new NotFoundException(`CEP inválido: ${cep}. O CEP deve conter 8 dígitos.`);
    }

    // Busca no banco de dados local
    const localZipCode = await this.findInLocalDatabase(cleanZipCode);
    if (localZipCode) {
      return localZipCode;
    }

    // Se não encontrou localmente, busca na API externa
    const externalZipCode = await this.findInExternalApi(cleanZipCode);
    if (!externalZipCode) {
      throw new NotFoundException(`CEP não encontrado: ${cep}`);
    }

    // Salva o CEP encontrado na API externa no banco de dados local
    const zipCodeToSave: CreateZipCodeDto = {
      zipCode: this.formatZipCode(cleanZipCode),
      street: externalZipCode.logradouro,
      neighborhood: externalZipCode.bairro,
      city: externalZipCode.localidade,
      state: externalZipCode.uf,
      registrationOrigin: 'auto',
    };

    return this.saveZipCode(zipCodeToSave);
  }

  /**
   * Cria ou atualiza um CEP manualmente
   */
  async createManual(createZipCodeDto: CreateZipCodeDto): Promise<ZipCodeDto> {
    // Força a origem como 'manual' para CEPs inseridos manualmente
    createZipCodeDto.registrationOrigin = 'manual';

    return this.saveZipCode(createZipCodeDto);
  }

  /**
   * Busca CEPs por logradouro, cidade e estado
   */
  async findByAddress(searchDto: SearchAddressDto): Promise<ZipCodeDto[]> {
    try {
      // Primeiro, tenta buscar no banco de dados local
      const localResults = await this.findAddressInLocalDatabase(searchDto);

      // Se encontrou resultados localmente, retorna
      if (localResults.length > 0) {
        return localResults;
      }

      // Se não encontrou localmente, busca na API externa
      const externalResults = await this.findAddressInExternalApi(searchDto);

      // Se não encontrou na API externa, lança exceção
      if (externalResults.length === 0) {
        throw new NotFoundException(`Nenhum CEP encontrado para o endereço informado`);
      }

      // Salva os resultados no banco de dados local e retorna
      const savedResults: ZipCodeDto[] = [];

      for (const result of externalResults) {
        const zipCodeToSave: CreateZipCodeDto = {
          zipCode: result.cep,
          street: result.logradouro,
          neighborhood: result.bairro,
          city: result.localidade,
          state: result.uf,
          registrationOrigin: 'auto',
        };

        const savedZipCode = await this.saveZipCode(zipCodeToSave);
        savedResults.push(savedZipCode);
      }

      return savedResults;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      console.error('Erro ao buscar CEPs por endereço:', error);
      throw new BadRequestException('Falha ao buscar CEPs por endereço');
    }
  }

  /**
   * Busca endereços no banco de dados local
   */
  private async findAddressInLocalDatabase(searchDto: SearchAddressDto): Promise<ZipCodeDto[]> {
    const { street, city, state } = searchDto;

    try {
      const zipCodes = await this.prisma.zipCode.findMany({
        where: {
          street: {
            contains: street,
            mode: 'insensitive'
          },
          city: {
            equals: city,
            mode: 'insensitive'
          },
          state: {
            equals: state.toUpperCase()
          }
        },
      });

      return zipCodes.map(zipCode => ({
        zipCode: zipCode.zipCode,
        street: zipCode.street,
        neighborhood: zipCode.neighborhood,
        city: zipCode.city,
        state: zipCode.state,
        registrationOrigin: zipCode.registrationOrigin,
        createdAt: zipCode.createdAt,
        updatedAt: zipCode.updatedAt,
      }));
    } catch (error) {
      console.error('Erro ao buscar endereços no banco de dados local:', error);
      return [];
    }
  }

  /**
   * Busca endereços na API externa (ViaCEP)
   */
  private async findAddressInExternalApi(searchDto: SearchAddressDto): Promise<ViaCepResponse[]> {
    const { street, city, state } = searchDto;

    try {
      // A API do ViaCEP espera o logradouro sem acentos e com espaços substituídos por +
      const formattedStreet = this.formatStringForViaCep(street);
      const formattedCity = this.formatStringForViaCep(city);

      const { data } = await firstValueFrom(
        this.httpService.get<ViaCepResponse[]>(
          `https://viacep.com.br/ws/${state}/${formattedCity}/${formattedStreet}/json/`
        ).pipe(
          catchError((error: AxiosError) => {
            console.error('Erro ao buscar endereços na API externa:', error.message);
            throw new Error('Falha ao buscar endereços na API externa');
          }),
        ),
      );

      // Filtra resultados com erro
      return Array.isArray(data) ? data.filter(item => !item.erro) : [];
    } catch (error) {
      console.error('Erro ao buscar endereços na API externa:', error);
      return [];
    }
  }

  /**
   * Formata uma string para o padrão esperado pela API do ViaCEP
   */
  private formatStringForViaCep(str: string): string {
    // Remove acentos
    const withoutAccents = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

    // Substitui espaços por +
    return withoutAccents.replace(/\s+/g, '+');
  }
}
