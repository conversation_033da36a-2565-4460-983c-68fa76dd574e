/**
 * Formata um valor numérico como moeda (BRL)
 * @param value Valor a ser formatado
 * @returns String formatada como moeda
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

/**
 * Formata uma data no padrão brasileiro (DD/MM/YYYY)
 * @param date Data a ser formatada
 * @returns String formatada como data
 */
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

/**
 * Formata uma data com hora no padrão brasileiro (DD/MM/YYYY HH:MM)
 * @param date Data a ser formatada
 * @returns String formatada como data e hora
 */
export const formatDateTime = (date: Date): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Formata um número com separadores de milhar
 * @param value Número a ser formatado
 * @param decimalPlaces Número de casas decimais
 * @returns String formatada como número
 */
export const formatNumber = (value: number, decimalPlaces: number = 2): string => {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  }).format(value);
};

/**
 * Formata um número como percentual
 * @param value Valor a ser formatado (0.1 = 10%)
 * @param decimalPlaces Número de casas decimais
 * @returns String formatada como percentual
 */
export const formatPercent = (value: number, decimalPlaces: number = 2): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'percent',
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  }).format(value);
};

/**
 * Formata um CPF (XXX.XXX.XXX-XX)
 * @param cpf String com o CPF (apenas números)
 * @returns CPF formatado
 */
export const formatCPF = (cpf: string): string => {
  if (!cpf) return '';
  
  // Remove caracteres não numéricos
  const cleanCPF = cpf.replace(/\D/g, '');
  
  // Verifica se tem 11 dígitos
  if (cleanCPF.length !== 11) return cpf;
  
  // Formata como XXX.XXX.XXX-XX
  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

/**
 * Formata um CNPJ (XX.XXX.XXX/XXXX-XX)
 * @param cnpj String com o CNPJ (apenas números)
 * @returns CNPJ formatado
 */
export const formatCNPJ = (cnpj: string): string => {
  if (!cnpj) return '';
  
  // Remove caracteres não numéricos
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  // Verifica se tem 14 dígitos
  if (cleanCNPJ.length !== 14) return cnpj;
  
  // Formata como XX.XXX.XXX/XXXX-XX
  return cleanCNPJ.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

/**
 * Formata um CEP (XXXXX-XXX)
 * @param cep String com o CEP (apenas números)
 * @returns CEP formatado
 */
export const formatCEP = (cep: string): string => {
  if (!cep) return '';
  
  // Remove caracteres não numéricos
  const cleanCEP = cep.replace(/\D/g, '');
  
  // Verifica se tem 8 dígitos
  if (cleanCEP.length !== 8) return cep;
  
  // Formata como XXXXX-XXX
  return cleanCEP.replace(/(\d{5})(\d{3})/, '$1-$2');
};

/**
 * Formata um telefone ((XX) XXXXX-XXXX ou (XX) XXXX-XXXX)
 * @param phone String com o telefone (apenas números)
 * @returns Telefone formatado
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return '';
  
  // Remove caracteres não numéricos
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Verifica o tamanho
  if (cleanPhone.length < 10) return phone;
  
  // Celular (com 9 dígitos)
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  
  // Telefone fixo
  return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
};
