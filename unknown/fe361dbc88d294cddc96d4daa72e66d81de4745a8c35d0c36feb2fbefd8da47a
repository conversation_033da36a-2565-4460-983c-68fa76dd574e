// backend/src/routes/currencies/dto/create-currency.dto.ts
import {
  IsString,
  IsBoolean,
  IsInt,
  IsOptional,
  MaxLength,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCurrencyDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> da moeda (ISO 4217)',
    example: 'BRL',
    maxLength: 3
  })
  @IsString()
  @MaxLength(3)
  code: string;

  @ApiProperty({
    description: 'Nome da moeda',
    example: 'Real Brasileiro',
    maxLength: 50
  })
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiProperty({
    description: 'Símbolo da moeda',
    example: 'R$',
    maxLength: 5
  })
  @IsString()
  @MaxLength(5)
  symbol: string;

  @ApiPropertyOptional({
    description: 'Número de casas decimais',
    example: 2,
    default: 2,
    minimum: 0,
    maximum: 10
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  decimalPlaces?: number;

  @ApiPropertyOptional({
    description: 'Indica se é a moeda padrão',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}
