import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import {
  PermissionDto,
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionListDto,
} from './dto/permission.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PermissionsService {
  private readonly logger = new Logger(PermissionsService.name);

  constructor(private prisma: PrismaService) {}

  async create(createPermissionDto: CreatePermissionDto): Promise<PermissionDto> {
    try {
      // Verificar se a empresa existe
      const company = await this.prisma.company.findUnique({
        where: { id: createPermissionDto.companyId },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Verificar se já existe uma permissão com a mesma ação na empresa
      const existingPermission = await this.prisma.permission.findFirst({
        where: {
          companyId: createPermissionDto.companyId,
          action: createPermissionDto.action,
        },
      });

      if (existingPermission) {
        throw new ConflictException(
          `Já existe uma permissão com a ação ${createPermissionDto.action} nesta empresa`,
        );
      }

      // Verificar se a permissão do sistema existe, se fornecida
      if (createPermissionDto.systemPermissionId) {
        const systemPermission = await this.prisma.systemPermission.findUnique({
          where: { id: createPermissionDto.systemPermissionId },
        });

        if (!systemPermission) {
          throw new NotFoundException('Permissão do sistema não encontrada');
        }
      }

      // Criar a permissão
      const permission = await this.prisma.permission.create({
        data: {
          id: uuidv4(),
          companyId: createPermissionDto.companyId,
          action: createPermissionDto.action,
          description: createPermissionDto.description,
          systemPermissionId: createPermissionDto.systemPermissionId,
        },
      });

      return {
        id: permission.id,
        companyId: permission.companyId,
        action: permission.action,
        description: permission.description || undefined,
        systemPermissionId: permission.systemPermissionId || undefined,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(
        `Erro ao criar permissão: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(
    companyId: string,
    page = 1,
    limit = 10,
  ): Promise<PermissionListDto> {
    try {
      const skip = (page - 1) * limit;

      // Contar total de permissões
      const total = await this.prisma.permission.count({
        where: { companyId },
      });

      // Buscar permissões paginadas
      const permissions = await this.prisma.permission.findMany({
        where: { companyId },
        skip,
        take: limit,
        orderBy: { action: 'asc' },
      });

      return {
        items: permissions.map((permission) => ({
          id: permission.id,
          companyId: permission.companyId,
          action: permission.action,
          description: permission.description || undefined,
          systemPermissionId: permission.systemPermissionId || undefined,
          createdAt: permission.createdAt,
          updatedAt: permission.updatedAt,
        })),
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Erro ao listar permissões: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: string): Promise<PermissionDto> {
    try {
      const permission = await this.prisma.permission.findUnique({
        where: { id },
      });

      if (!permission) {
        throw new NotFoundException('Permissão não encontrada');
      }

      return {
        id: permission.id,
        companyId: permission.companyId,
        action: permission.action,
        description: permission.description || undefined,
        systemPermissionId: permission.systemPermissionId || undefined,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao buscar permissão: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async update(
    id: string,
    updatePermissionDto: UpdatePermissionDto,
  ): Promise<PermissionDto> {
    try {
      // Verificar se a permissão existe
      const existingPermission = await this.prisma.permission.findUnique({
        where: { id },
      });

      if (!existingPermission) {
        throw new NotFoundException('Permissão não encontrada');
      }

      // Verificar se a permissão do sistema existe, se fornecida
      if (updatePermissionDto.systemPermissionId) {
        const systemPermission = await this.prisma.systemPermission.findUnique({
          where: { id: updatePermissionDto.systemPermissionId },
        });

        if (!systemPermission) {
          throw new NotFoundException('Permissão do sistema não encontrada');
        }
      }

      // Verificar se já existe outra permissão com a mesma ação na empresa
      if (updatePermissionDto.action) {
        const duplicatePermission = await this.prisma.permission.findFirst({
          where: {
            companyId: existingPermission.companyId,
            action: updatePermissionDto.action,
            id: { not: id },
          },
        });

        if (duplicatePermission) {
          throw new ConflictException(
            `Já existe uma permissão com a ação ${updatePermissionDto.action} nesta empresa`,
          );
        }
      }

      // Atualizar a permissão
      const updatedPermission = await this.prisma.permission.update({
        where: { id },
        data: {
          action: updatePermissionDto.action,
          description: updatePermissionDto.description,
          systemPermissionId: updatePermissionDto.systemPermissionId,
        },
      });

      return {
        id: updatedPermission.id,
        companyId: updatedPermission.companyId,
        action: updatedPermission.action,
        description: updatedPermission.description || undefined,
        systemPermissionId: updatedPermission.systemPermissionId || undefined,
        createdAt: updatedPermission.createdAt,
        updatedAt: updatedPermission.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(
        `Erro ao atualizar permissão: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Verificar se a permissão existe
      const existingPermission = await this.prisma.permission.findUnique({
        where: { id },
        include: { rolePermissions: true },
      });

      if (!existingPermission) {
        throw new NotFoundException('Permissão não encontrada');
      }

      // Verificar se a permissão está sendo usada
      if (existingPermission.rolePermissions.length > 0) {
        throw new ConflictException(
          'Esta permissão está em uso e não pode ser removida',
        );
      }

      // Remover a permissão
      await this.prisma.permission.delete({ where: { id } });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      this.logger.error(
        `Erro ao remover permissão: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // Método para criar permissões para uma empresa com base nas permissões do sistema
  async createCompanyPermissions(companyId: string): Promise<void> {
    try {
      // Verificar se a empresa existe
      const company = await this.prisma.company.findUnique({
        where: { id: companyId },
      });

      if (!company) {
        throw new NotFoundException('Empresa não encontrada');
      }

      // Buscar todas as permissões do sistema
      const systemPermissions = await this.prisma.systemPermission.findMany();

      // Criar permissões da empresa para cada permissão do sistema
      for (const systemPermission of systemPermissions) {
        // Verificar se a permissão já existe
        const existingPermission = await this.prisma.permission.findFirst({
          where: {
            companyId,
            action: systemPermission.code,
          },
        });

        if (!existingPermission) {
          await this.prisma.permission.create({
            data: {
              id: uuidv4(),
              companyId,
              action: systemPermission.code,
              description: systemPermission.description,
              systemPermissionId: systemPermission.id,
            },
          });
        }
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Erro ao criar permissões da empresa: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
