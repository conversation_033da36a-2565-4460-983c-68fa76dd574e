import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { addressService } from '@/services/api';
import { CreateAddressRequest, UpdateAddressRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar endereços com paginação
export const useAddresses = (page = 1, limit = 10, search?: string, entityId?: string) => {
  return useQuery({
    queryKey: ['addresses', { page, limit, search, entityId }],
    queryFn: () => addressService.getAddresses(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um endereço específico por ID
export const useAddress = (id: string) => {
  return useQuery({
    queryKey: ['addresses', id],
    queryFn: () => addressService.getAddressById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo endereço
export const useCreateAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateAddressRequest) => addressService.createAddress(data),
    onSuccess: (newAddress) => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      
      // Invalidar também endereços relacionados a uma entidade específica, se necessário
      // Assumimos que a entidade ID está nos dados enviados para criar o endereço
      const entityId = (newAddress as any).entityId || (newAddress as any).entity_id;
      if (entityId) {
        queryClient.invalidateQueries({ 
          queryKey: ['addresses', { entityId }] 
        });
      }
      
      toast.success('Endereço criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar endereço. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um endereço existente
export const useUpdateAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateAddressRequest }) => 
      addressService.updateAddress(id, data),
    onSuccess: (updatedAddress) => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      queryClient.setQueryData(['addresses', updatedAddress.id], updatedAddress);
      
      // Invalidar também endereços relacionados a uma entidade específica, se necessário
      // Assumimos que a entidade ID está nos dados enviados para atualizar o endereço
      const entityId = (updatedAddress as any).entityId || (updatedAddress as any).entity_id;
      if (entityId) {
        queryClient.invalidateQueries({ 
          queryKey: ['addresses', { entityId }] 
        });
      }
      
      toast.success('Endereço atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar endereço. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um endereço
export const useDeleteAddress = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => addressService.deleteAddress(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      queryClient.removeQueries({ queryKey: ['addresses', id] });
      toast.success('Endereço excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir endereço. Por favor, tente novamente.'
      );
    }
  });
};
