
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { RefreshCcw } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { TransactionFormValues, RefreshableField } from "../types/TransactionModalTypes";
import { bankAccounts } from "../data/transactionModalData";

interface TransactionAccountsSectionProps {
  form: UseFormReturn<TransactionFormValues>;
  transactionType: "accounts_receivable" | "accounts_payable" | "transfer";
  refreshingFields: Record<RefreshableField, boolean>;
  onRefresh: (field: RefreshableField) => void;
}

export default function TransactionAccountsSection({ 
  form, 
  transactionType,
  refreshingFields,
  onRefresh
}: TransactionAccountsSectionProps) {
  if (transactionType === "transfer") {
    return (
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="bankAccountId"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center justify-between">
                <FormLabel>Source Account</FormLabel>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7" 
                  onClick={() => onRefresh("bankAccountId")}
                  disabled={refreshingFields.bankAccountId}
                >
                  <RefreshCcw className={`h-4 w-4 ${refreshingFields.bankAccountId ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source account" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bankAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="destinationAccountId"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center justify-between">
                <FormLabel>Destination Account</FormLabel>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7" 
                  onClick={() => onRefresh("destinationAccountId")}
                  disabled={refreshingFields.destinationAccountId}
                >
                  <RefreshCcw className={`h-4 w-4 ${refreshingFields.destinationAccountId ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select destination account" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bankAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    );
  }

  return (
    <FormField
      control={form.control}
      name="bankAccountId"
      render={({ field }) => (
        <FormItem>
          <div className="flex items-center justify-between">
            <FormLabel>Bank Account</FormLabel>
            <Button 
              type="button"
              variant="ghost" 
              size="icon" 
              className="h-7 w-7" 
              onClick={() => onRefresh("bankAccountId")}
              disabled={refreshingFields.bankAccountId}
            >
              <RefreshCcw className={`h-4 w-4 ${refreshingFields.bankAccountId ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          <Select 
            onValueChange={field.onChange} 
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a bank account" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {bankAccounts.map((account) => (
                <SelectItem key={account.id} value={account.id}>
                  {account.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
