# frontend -> backend -> database
# direction: right
# grid-columns: 1
# grid-rows: 1

frontend -> backend: {
  style: {
    animated: true
  }
}

spacer1: "" {
  style.opacity: 0
}

backend -> database: {
  style: {
    animated: true
  }
}
# frontend.style.animated: true
spacer2: "" {
  style.opacity: 0
}

database: {
  shape: cylinder
  icon: https://icons.terrastruct.com/dev%2Fpostgresql-words.svg
  #   style: {
  #     font-size: 12
  #     bold: true
  #     underline: false
  #   }
}

backend: {
  icon: https://icons.terrastruct.com/dev%2Fnodejs.svg
}

frontend: {
  icon: https://icons.terrastruct.com/dev%2Freact.svg
}
