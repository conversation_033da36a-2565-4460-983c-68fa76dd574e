// backend/src/routes/recurrence-types/dto/update-recurrence-type.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateRecurrenceTypeDto {
  @ApiPropertyOptional({
    description: 'Nome do tipo de recorrência',
    example: 'mensal',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  name?: string;

  @ApiPropertyOptional({
    description: 'Descrição do tipo de recorrência',
    example: 'Recorrência mensal',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
}
