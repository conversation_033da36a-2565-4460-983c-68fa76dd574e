# Node.js dependencies
node_modules/

# Build outputs
dist/
build/
out/

# Environment and secrets
.env*
*.pem
*.key
*.crt

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo

# Version control
.git/
.gitignore

# Documentation and temporary files
docs/
*.md
README*
tmp/
temp/
*.tmp

# Docker files
# We need to keep Dockerfiles for our builds
# Dockerfile*
# docker-compose*

# Logs and debug files
*.log
debug/

# Project-specific patterns
local/
.local/
config.local.*
*.local.yml

# Exclude specific files
!.dockerignore
!Dockerfile

# Frontend specific
frontend/public/
frontend/src/**/*.test.tsx
frontend/src/**/*.test.ts

# Backend specific
backend/prisma/migrations/
backend/test/
backend/src/**/*.test.ts