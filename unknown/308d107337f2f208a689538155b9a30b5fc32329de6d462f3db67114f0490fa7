
import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { FileUp, Download, AlertCircle } from "lucide-react";

interface ImportCSVDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (csvContent: string) => Promise<{ success: number; failed: number }>;
}

export function ImportCSVDialog({
  open,
  onOpenChange,
  onImport,
}: ImportCSVDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  const downloadTemplate = () => {
    // Create CSV template content - removed Origem column as it's always 'manual' for CSV imports
    const csvContent = 
      "CEP,Logradouro,Bairro,Cidade,Estado\n" +
      "12345-678,Avenida Exemplo,Bairro Exemplo,São Paulo,SP\n" +
      "87654-321,Rua Teste,Centro,Rio de Janeiro,RJ";
    
    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'template_ceps.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setError('O arquivo selecionado não é um CSV válido.');
        setSelectedFile(null);
        return;
      }
      
      setSelectedFile(file);
      setError(null);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      setError('Selecione um arquivo CSV para importar.');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Read the file content
      const reader = new FileReader();
      
      reader.onload = async (event) => {
        const csvContent = event.target?.result as string;
        
        // Process the CSV content
        const result = await onImport(csvContent);
        
        toast({
          title: "Importação concluída",
          description: `${result.success} CEPs importados com sucesso. ${result.failed} falhas.`,
          variant: result.success > 0 ? "default" : "destructive"
        });
        
        // Close the dialog if successful
        if (result.success > 0) {
          onOpenChange(false);
        }
      };
      
      reader.onerror = () => {
        setError('Erro ao ler o arquivo CSV.');
        setIsLoading(false);
      };
      
      reader.readAsText(selectedFile);
    } catch (error) {
      console.error('Error importing CSV:', error);
      setError('Erro ao processar o arquivo CSV.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <FileUp className="h-5 w-5" />
            Importar CEPs
          </DialogTitle>
          <DialogDescription>
            Importe CEPs a partir de um arquivo CSV. O arquivo deve conter as colunas: CEP, Logradouro, Bairro, Cidade e Estado.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex flex-col gap-1.5">
            <Input
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="cursor-pointer"
            />
            <p className="text-xs text-muted-foreground">
              Selecione um arquivo CSV para importar.
            </p>
          </div>

          {error && (
            <div className="bg-destructive/10 p-3 rounded-md flex gap-2 items-start">
              <AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
              <p className="text-sm text-destructive">{error}</p>
            </div>
          )}

          <div className="border rounded-md p-3">
            <h3 className="text-sm font-medium mb-2">Não tem um arquivo CSV?</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Baixe nosso modelo de arquivo CSV para importação de CEPs.
            </p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={downloadTemplate}
              className="w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              Baixar modelo CSV
            </Button>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleImport}
            disabled={!selectedFile || isLoading}
          >
            {isLoading ? (
              <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
            ) : (
              <FileUp className="h-4 w-4 mr-2" />
            )}
            Importar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
