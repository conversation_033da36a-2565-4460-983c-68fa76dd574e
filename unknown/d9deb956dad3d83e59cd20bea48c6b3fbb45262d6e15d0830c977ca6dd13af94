
import { useState, useEffect } from "react";
import { Check, CalendarIcon, ChevronDown } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useIsMobile } from "@/hooks/use-mobile";

interface EnhancedPeriodFilterProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  startDate?: Date;
  endDate?: Date;
  onDateRangeChange?: (startDate: Date | undefined, endDate: Date | undefined) => void;
}

const EnhancedPeriodFilter = ({
  selectedPeriod,
  onPeriodChange,
  startDate,
  endDate,
  onDateRangeChange,
}: EnhancedPeriodFilterProps) => {
  const [open, setOpen] = useState(false);
  const [mode, setMode] = useState<"predefined" | "custom">("predefined");
  const [selectedDates, setSelectedDates] = useState<{
    start?: Date;
    end?: Date;
  }>({
    start: startDate,
    end: endDate,
  });
  const isMobile = useIsMobile();

  // Reset selectedDates when selectedPeriod changes (for example when "Limpar" is clicked)
  useEffect(() => {
    if (selectedPeriod !== "custom") {
      setMode("predefined");
      setSelectedDates({ start: undefined, end: undefined });
    } else if (startDate && endDate) {
      setSelectedDates({ start: startDate, end: endDate });
      setMode("custom");
    }
  }, [selectedPeriod, startDate, endDate]);

  const periods = [
    { id: "current-month", label: "Este Mês" },
    { id: "next-month", label: "Próximo Mês" },
    { id: "previous-month", label: "Mês Anterior" },
    { id: "current-week", label: "Esta Semana" },
    { id: "next-week", label: "Próxima Semana" },
    { id: "last-3-months", label: "Últimos 3 Meses" },
    { id: "current-year", label: "Este Ano" },
    { id: "today", label: "Hoje" },
  ];

  const handlePeriodSelect = (period: string) => {
    onPeriodChange(period);
    setOpen(false);
  };

  const handleDateSelect = (date?: Date) => {
    if (!date) return;

    if (!selectedDates.start || (selectedDates.start && selectedDates.end)) {
      setSelectedDates({ start: date, end: undefined });
    } else {
      // Make sure start date is before end date
      if (date < selectedDates.start) {
        setSelectedDates({ start: date, end: selectedDates.start });
      } else {
        setSelectedDates({ start: selectedDates.start, end: date });
      }
    }
  };

  const handleApplyCustomRange = () => {
    if (selectedDates.start && selectedDates.end && onDateRangeChange) {
      onDateRangeChange(selectedDates.start, selectedDates.end);
      setOpen(false);
    }
  };

  const formatDateWithShortYear = (date: Date) => {
    return format(date, "dd/MM/yy", { locale: ptBR });
  };

  const getModeLabel = () => {
    if (selectedPeriod === "custom" && startDate && endDate) {
      return `${formatDateWithShortYear(startDate)} - ${formatDateWithShortYear(endDate)}`;
    }
    
    return periods.find((p) => p.id === selectedPeriod)?.label || "Este Mês";
  };

  const getFilterWidth = () => {
    if (selectedPeriod === "custom" && startDate && endDate) {
      return isMobile ? "w-full" : "w-[240px]";
    }
    return isMobile ? "w-full" : "w-[180px]";
  };

  const handleModeChange = (value: string) => {
    setMode(value as "predefined" | "custom");
  };

  // Handle calendar change with automatic reset
  const handleCalendarSelect = (range: { from?: Date; to?: Date }) => {
    // If both dates are already selected and user clicks on a new date
    // reset the selection to start fresh with the new date
    if (selectedDates.start && selectedDates.end && range?.from && !range?.to) {
      setSelectedDates({
        start: range.from, 
        end: undefined
      });
    } else {
      setSelectedDates({
        start: range?.from, 
        end: range?.to
      });
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "flex items-center gap-2 bg-white dark:bg-dark-card shadow-sm border border-gray-100 dark:border-dark-card-light rounded-md justify-between h-10 px-4 py-2",
            getFilterWidth()
          )}
        >
          <div className="flex items-center gap-1.5 truncate">
            <CalendarIcon className="h-4 w-4 min-w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
              {getModeLabel()}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 min-w-4 text-gray-500 dark:text-gray-400" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4 shadow-lg rounded-md border border-gray-100 dark:border-dark-card-light bg-white dark:bg-dark-card">
        <div className="mb-4">
          <Select value={mode} onValueChange={handleModeChange}>
            <SelectTrigger className="w-full bg-white dark:bg-dark-card-light dark:text-gray-100 dark:border-dark-card-light">
              <SelectValue placeholder="Escolha o tipo de período" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-dark-panel dark:border-dark-card-light">
              <SelectItem value="predefined" className="dark:text-gray-100 dark:focus:bg-dark-card-light dark:focus:text-white">Pré-definido</SelectItem>
              <SelectItem value="custom" className="dark:text-gray-100 dark:focus:bg-dark-card-light dark:focus:text-white">Personalizado</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {mode === "predefined" ? (
          <div className="py-1 max-h-72 overflow-y-auto scrollbar-thin">
            {periods.map((period) => (
              <div
                key={period.id}
                className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-dark-card-light cursor-pointer"
                onClick={() => handlePeriodSelect(period.id)}
              >
                <div className="flex items-center gap-2">
                  {selectedPeriod === period.id ? (
                    <Check className="h-4 w-4 text-gray-800 dark:text-white min-w-4" />
                  ) : (
                    <div className="w-4 min-w-4" />
                  )}
                  <span className="text-sm text-gray-800 dark:text-gray-100 font-medium">
                    {period.label}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-2 dark:text-gray-100">Selecione o intervalo:</p>
              <Calendar
                mode="range"
                selected={{
                  from: selectedDates.start,
                  to: selectedDates.end,
                }}
                onSelect={handleCalendarSelect}
                locale={ptBR}
                className={cn("rounded-md border min-w-[300px] pointer-events-auto dark:bg-dark-card-light dark:text-gray-100 dark:border-dark-card-light")}
              />
            </div>
            <Button
              className="w-full"
              onClick={handleApplyCustomRange}
              disabled={!selectedDates.start || !selectedDates.end}
            >
              Aplicar
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default EnhancedPeriodFilter;
