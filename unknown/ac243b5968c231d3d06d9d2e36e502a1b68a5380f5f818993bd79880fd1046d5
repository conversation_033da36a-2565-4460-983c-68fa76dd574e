// backend/src/routes/address-types/address-types.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { AddressTypesService } from './address-types.service';
import { CreateAddressTypeDto } from './dto/create-address-type.dto';
import { UpdateAddressTypeDto } from './dto/update-address-type.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('address-types')
@Controller('address-types')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
export class AddressTypesController {
  constructor(private readonly addressTypesService: AddressTypesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo tipo de endereço' })
  @ApiBody({ type: CreateAddressTypeDto })
  @ApiResponse({
    status: 201,
    description: 'Tipo de endereço criado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Tipo de endereço já existe' })
  create(@Body() createAddressTypeDto: CreateAddressTypeDto) {
    return this.addressTypesService.create(createAddressTypeDto);
  }

  @Get()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os tipos de endereço' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number })
  @ApiResponse({ status: 200, description: 'Lista de tipos de endereço' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite de itens por página inválido');
    }

    return this.addressTypesService.findAll({
      page: pageNumber,
      limit: limitNumber,
    });
  }

  @Get(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter um tipo de endereço pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do tipo de endereço', type: String })
  @ApiResponse({ status: 200, description: 'Tipo de endereço encontrado' })
  @ApiResponse({ status: 404, description: 'Tipo de endereço não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.addressTypesService.findOne(id);
  }

  @Put(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um tipo de endereço' })
  @ApiParam({ name: 'id', description: 'ID do tipo de endereço', type: String })
  @ApiBody({ type: UpdateAddressTypeDto })
  @ApiResponse({ status: 200, description: 'Tipo de endereço atualizado com sucesso' })
  @ApiResponse({ status: 400, description: 'Requisição inválida' })
  @ApiResponse({ status: 404, description: 'Tipo de endereço não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 409, description: 'Conflito - Nome já existe' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAddressTypeDto: UpdateAddressTypeDto,
  ) {
    return this.addressTypesService.update(id, updateAddressTypeDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover um tipo de endereço' })
  @ApiParam({ name: 'id', description: 'ID do tipo de endereço', type: String })
  @ApiResponse({ status: 204, description: 'Tipo de endereço removido com sucesso' })
  @ApiResponse({ status: 400, description: 'Não é possível remover o tipo de endereço' })
  @ApiResponse({ status: 404, description: 'Tipo de endereço não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.addressTypesService.remove(id);
  }
}
