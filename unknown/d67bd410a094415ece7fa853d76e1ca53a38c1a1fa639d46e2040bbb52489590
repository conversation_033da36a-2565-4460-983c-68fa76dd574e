
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useTransactionModalForm } from "./hooks/useTransactionModalForm";
import { TransactionModalProps } from "./types/TransactionModalTypes";
import TransactionTypeSection from "./FormSections/TransactionTypeSection";
import TransactionBasicInfoSection from "./FormSections/TransactionBasicInfoSection";
import TransactionPaymentSection from "./FormSections/TransactionPaymentSection";
import TransactionAccountsSection from "./FormSections/TransactionAccountsSection";
import TransactionNotesSection from "./FormSections/TransactionNotesSection";

export default function TransactionModal({
  isOpen,
  onClose,
  transaction,
}: TransactionModalProps) {
  const {
    form,
    transactionType,
    isEditing,
    refreshingFields,
    refreshFieldOptions,
    onSubmit,
    getFormTitle
  } = useTransactionModalForm(isOpen, transaction, onClose);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>{getFormTitle()}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Type selection */}
            <TransactionTypeSection form={form} />
            
            {/* Basic information */}
            <TransactionBasicInfoSection form={form} />
            
            {/* Payment method, recurrence, invoice for non-transfers */}
            <TransactionPaymentSection 
              form={form} 
              transactionType={transactionType}
              refreshingFields={refreshingFields}
              onRefresh={refreshFieldOptions}
            />
            
            {/* Account selection */}
            <TransactionAccountsSection 
              form={form} 
              transactionType={transactionType}
              refreshingFields={refreshingFields}
              onRefresh={refreshFieldOptions}
            />
            
            {/* Notes section */}
            <TransactionNotesSection form={form} />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                {isEditing ? "Update" : "Create"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
