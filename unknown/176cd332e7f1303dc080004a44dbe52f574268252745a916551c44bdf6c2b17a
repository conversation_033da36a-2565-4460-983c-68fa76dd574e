import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreatePermissionDto,
  PermissionDto,
  UpdatePermissionDto,
  PermissionListDto,
} from './dto/permission.dto';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { RolesGuard } from '../../guards/roles.guard';

@ApiTags('permissions')
@Controller('permissions')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova permissão' })
  @ApiBody({ type: CreatePermissionDto })
  @ApiResponse({
    status: 201,
    description: 'Permissão criada com sucesso',
    type: PermissionDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Empresa ou permissão do sistema não encontrada' })
  @ApiResponse({ status: 409, description: 'Permissão já existe' })
  async create(
    @Body() createPermissionDto: CreatePermissionDto,
  ): Promise<PermissionDto> {
    return this.permissionsService.create(createPermissionDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar permissões de uma empresa' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de permissões',
    type: PermissionListDto,
  })
  async findAll(
    @Query('companyId') companyId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<PermissionListDto> {
    return this.permissionsService.findAll(companyId, page, limit);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma permissão pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da permissão' })
  @ApiResponse({
    status: 200,
    description: 'Permissão encontrada',
    type: PermissionDto,
  })
  @ApiResponse({ status: 404, description: 'Permissão não encontrada' })
  async findOne(@Param('id') id: string): Promise<PermissionDto> {
    return this.permissionsService.findOne(id);
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma permissão' })
  @ApiParam({ name: 'id', description: 'ID da permissão' })
  @ApiBody({ type: UpdatePermissionDto })
  @ApiResponse({
    status: 200,
    description: 'Permissão atualizada com sucesso',
    type: PermissionDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Permissão não encontrada' })
  @ApiResponse({ status: 409, description: 'Ação já existe' })
  async update(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ): Promise<PermissionDto> {
    return this.permissionsService.update(id, updatePermissionDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma permissão' })
  @ApiParam({ name: 'id', description: 'ID da permissão' })
  @ApiResponse({ status: 204, description: 'Permissão removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Permissão não encontrada' })
  @ApiResponse({ status: 409, description: 'Permissão está em uso' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.permissionsService.remove(id);
  }

  @Post('setup')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configurar permissões para uma empresa' })
  @ApiQuery({ name: 'companyId', required: true, description: 'ID da empresa' })
  @ApiResponse({
    status: 201,
    description: 'Permissões da empresa configuradas com sucesso',
  })
  @HttpCode(HttpStatus.CREATED)
  async setup(@Query('companyId') companyId: string): Promise<{ message: string }> {
    await this.permissionsService.createCompanyPermissions(companyId);
    return { message: 'Permissões da empresa configuradas com sucesso' };
  }
}
