#!/bin/bash
set -e

echo "Resetando o banco de dados..."
npx prisma migrate reset --force

echo "Aplicando migrações existentes..."
npx prisma migrate dev

echo "Inserindo dados iniciais..."
# Conectar ao banco de dados e executar o script SQL diretamente
psql $DATABASE_URL -f /app/prisma/seed_data.sql

echo "Gerando o cliente Prisma..."
npx prisma generate

echo "Dados iniciais aplicados com sucesso!"
