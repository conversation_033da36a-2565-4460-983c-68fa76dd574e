import { Modu<PERSON> } from '@nestjs/common';
import { SystemPermissionsModule } from '../system-permissions/system-permissions.module';
import { PermissionsModule } from '../permissions/permissions.module';
import { RolesModule } from '../roles/roles.module';
import { UserCompanyRolesModule } from '../user-company-roles/user-company-roles.module';
import { RbacService } from './rbac.service';
import { RbacController } from './rbac.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    SystemPermissionsModule,
    PermissionsModule,
    RolesModule,
    UserCompanyRolesModule,
  ],
  controllers: [RbacController],
  providers: [RbacService],
  exports: [RbacService],
})
export class RbacModule {}
