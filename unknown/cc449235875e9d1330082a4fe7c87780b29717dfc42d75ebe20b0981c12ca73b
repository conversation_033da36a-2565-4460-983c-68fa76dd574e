import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID, IsBoolean } from 'class-validator';

export class RoleDto {
  @ApiProperty({
    description: 'ID único do papel',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  companyId: string;

  @ApiProperty({ description: 'Nome do papel', example: 'Administrador' })
  name: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de administrador',
    example: true,
    default: false,
  })
  isAdmin: boolean;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CreateRoleDto {
  @ApiProperty({
    description: 'ID da empresa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({ description: 'Nome do papel', example: 'Administrador' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de administrador',
    example: true,
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isAdmin?: boolean;
}

export class UpdateRoleDto {
  @ApiProperty({
    description: 'Nome do papel',
    example: 'Administrador',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Descrição do papel',
    example: 'Acesso completo ao sistema',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Indica se é um papel de administrador',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isAdmin?: boolean;
}

export class RoleListDto {
  @ApiProperty({
    description: 'Lista de papéis',
    type: [RoleDto],
  })
  items: RoleDto[];

  @ApiProperty({ description: 'Total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'Página atual', example: 1 })
  page: number;

  @ApiProperty({ description: 'Limite de itens por página', example: 10 })
  limit: number;
}
