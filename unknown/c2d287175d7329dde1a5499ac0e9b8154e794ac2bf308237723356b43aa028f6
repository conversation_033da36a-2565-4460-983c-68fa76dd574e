import { ArrowUp, ArrowDown, ArrowLeftRight } from "lucide-react";
import { Transaction } from "@/types/transaction";
import { MoneyDisplay } from "@/components/ui-custom/MoneyInput";

interface TransactionAmountDisplayProps {
  transaction: Transaction;
}

export const TransactionAmountDisplay = ({ transaction }: TransactionAmountDisplayProps) => {
  // Garantir que o valor seja um número
  const amount = typeof transaction.amount === 'string' 
    ? parseFloat(String(transaction.amount).replace(/[^\d.,]/g, '').replace(',', '.')) 
    : (Number(transaction.amount) || 0);
  
  // Normalizar o tipo para lidar com diferentes formatos da API
  const normalizedType = transaction.type?.toLowerCase?.() || '';
  
  if (normalizedType.includes('transfer')) {
    return (
      <div className="flex items-center justify-end gap-1 text-blue-600 dark:text-blue-400">
        <ArrowLeftRight className="h-4 w-4" />
        <MoneyDisplay value={amount} />
      </div>
    );
  }
  
  // Determinar se é receita ou despesa com base no tipo ou no valor
  const isIncome = normalizedType.includes('receivable') || normalizedType.includes('income');
  const isExpense = normalizedType.includes('payable') || normalizedType.includes('expense');
  
  // Se o tipo não for claro, usar o valor para determinar
  const isPositive = isIncome || (!isExpense && amount > 0);
  
  const Icon = isPositive ? ArrowUp : ArrowDown;
  const textColorClass = isPositive 
    ? 'text-green-600 dark:text-green-400' 
    : 'text-red-600 dark:text-red-400';
  
  return (
    <div className={`flex items-center justify-end gap-1 ${textColorClass}`}>
      <Icon className="h-4 w-4" />
      <MoneyDisplay value={Math.abs(amount)} className={textColorClass} />
    </div>
  );
};
