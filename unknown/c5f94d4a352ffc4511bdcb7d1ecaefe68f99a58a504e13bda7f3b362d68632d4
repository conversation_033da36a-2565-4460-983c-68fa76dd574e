
import { z } from "zod";

// Schema for transaction validation
export const transactionSchema = z.object({
  type: z.enum(["accounts_receivable", "accounts_payable", "transfer"]),
  description: z.string().min(1, "Description is required"),
  amount: z.string().min(1, "Amount is required"),
  transactionDate: z.date(),
  bankAccountId: z.string().min(1, "Source account is required"),
  destinationAccountId: z.string().optional(),
  relatedId: z.string().optional(),
  notes: z.string().optional(),
  // Add new fields to the schema
  recurrence: z.string().optional(),
  paymentMethod: z.string().optional(),
  invoiceNumber: z.string().optional(),
});

export type TransactionFormValues = z.infer<typeof transactionSchema>;

// Sample data types
export interface Account {
  id: string;
  name: string;
}

export interface RecurrenceOption {
  id: string;
  name: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
}

export type RefreshableField = 
  | "bankAccountId" 
  | "destinationAccountId" 
  | "paymentMethod" 
  | "recurrence";

export interface TransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction?: any;
}
