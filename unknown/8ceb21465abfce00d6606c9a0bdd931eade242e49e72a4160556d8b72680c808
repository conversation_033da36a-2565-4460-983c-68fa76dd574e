import { useQuery } from '@tanstack/react-query';
import { currencyService } from '@/services/api';

// Hook para obter a moeda padrão
export const useDefaultCurrency = () => {
  return useQuery({
    queryKey: ['currencies', 'default'],
    queryFn: () => currencyService.getDefaultCurrency(),
    staleTime: 60 * 60 * 1000, // 1 hora (moedas raramente mudam)
    retry: false, // Desabilita novas tentativas automáticas em caso de erro
  });
};

// Hook para listar todas as moedas
export const useCurrencies = (page = 1, limit = 100) => {
  return useQuery({
    queryKey: ['currencies', { page, limit }],
    queryFn: () => currencyService.getCurrencies(page, limit),
    staleTime: 60 * 60 * 1000, // 1 hora
    retry: false, // Desabilita novas tentativas automáticas em caso de erro
  });
};
