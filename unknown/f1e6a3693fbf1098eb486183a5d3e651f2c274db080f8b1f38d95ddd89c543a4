import { useCallback } from 'react';
import monitoringService from '@/services/api/monitoringService';

interface EventData {
  [key: string]: any;
}

interface UseMonitoringReturn {
  logEvent: (eventName: string, data?: EventData) => void;
  logError: (error: Error | string, context?: EventData) => void;
  logNavigation: (path: string, data?: EventData) => void;
  logApiRequest: (endpoint: string, method: string, data?: EventData) => void;
  logApiResponse: (endpoint: string, method: string, status: number, data?: EventData) => void;
  logPerformance: (operation: string, durationMs: number, data?: EventData) => void;
  logCacheHit: (cacheKey: string, data?: EventData) => void;
  logCacheMiss: (cacheKey: string, data?: EventData) => void;
  logUserAction: (action: string, data?: EventData) => void;
}

/**
 * Hook para monitoramento de eventos na aplicação
 * 
 * Permite registrar diferentes tipos de eventos como:
 * - Eventos gerais
 * - Erros
 * - Navegação
 * - Requisições e respostas de API
 * - Métricas de performance
 * - Eventos de cache
 * - Ações do usuário
 */
export function useMonitoring(): UseMonitoringReturn {
  // Registrar evento genérico
  const logEvent = useCallback((eventName: string, data?: EventData) => {
    monitoringService.recordEvent(eventName, data);
  }, []);

  // Registrar erro
  const logError = useCallback((error: Error | string, context?: EventData) => {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    monitoringService.recordEvent('error', {
      message: errorMessage,
      stack: errorStack,
      ...context
    });
  }, []);

  // Registrar navegação
  const logNavigation = useCallback((path: string, data?: EventData) => {
    monitoringService.recordEvent('navigation', {
      path,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar requisição de API
  const logApiRequest = useCallback((endpoint: string, method: string, data?: EventData) => {
    monitoringService.recordEvent('api_request', {
      endpoint,
      method,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar resposta de API
  const logApiResponse = useCallback((endpoint: string, method: string, status: number, data?: EventData) => {
    monitoringService.recordEvent('api_response', {
      endpoint,
      method,
      status,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar métricas de performance
  const logPerformance = useCallback((operation: string, durationMs: number, data?: EventData) => {
    monitoringService.recordEvent('performance', {
      operation,
      durationMs,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar hit de cache
  const logCacheHit = useCallback((cacheKey: string, data?: EventData) => {
    monitoringService.recordEvent('cache_hit', {
      cacheKey,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar miss de cache
  const logCacheMiss = useCallback((cacheKey: string, data?: EventData) => {
    monitoringService.recordEvent('cache_miss', {
      cacheKey,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  // Registrar ação do usuário
  const logUserAction = useCallback((action: string, data?: EventData) => {
    monitoringService.recordEvent('user_action', {
      action,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }, []);

  return {
    logEvent,
    logError,
    logNavigation,
    logApiRequest,
    logApiResponse,
    logPerformance,
    logCacheHit,
    logCacheMiss,
    logUserAction,
  };
}
