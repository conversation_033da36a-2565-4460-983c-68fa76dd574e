// backend/src/routes/categories/dto/update-category.dto.ts
import {
  IsString,
  IsEnum,
  IsUUID,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { TransactionType } from './create-category.dto';

export class UpdateCategoryDto {
  @ApiPropertyOptional({
    description: 'Nome da categoria',
    example: 'Alimentação',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Tipo de transação (payable ou receivable)',
    example: 'payable',
    enum: TransactionType
  })
  @IsOptional()
  @IsEnum(TransactionType, { message: 'Tipo de transação deve ser payable ou receivable' })
  transactionType?: TransactionType;

  @ApiPropertyOptional({
    description: 'ID da categoria pai (para subcategorias)',
    example: '123e4567-e89b-12d3-a456-************',
    nullable: true
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID da categoria pai deve ser um UUID válido' })
  parentCategoryId?: string;
}
