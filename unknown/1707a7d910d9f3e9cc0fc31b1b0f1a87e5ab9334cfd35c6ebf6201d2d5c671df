import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notificationService } from '@/services/api';
import { CreateNotificationRequest, UpdateNotificationRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar notificações com paginação e filtros
export const useNotifications = (
  page = 1, 
  limit = 10, 
  read?: boolean,
  type?: string
) => {
  return useQuery({
    queryKey: ['notifications', { page, limit, read, type }],
    queryFn: () => notificationService.getNotifications(page, limit, read, type),
    placeholderData: (previousData) => previousData,
    staleTime: 60 * 1000, // 1 minuto (notificações são mais dinâmicas)
  });
};

// Hook para buscar uma notificação específica por ID
export const useNotification = (id: string) => {
  return useQuery({
    queryKey: ['notifications', id],
    queryFn: () => notificationService.getNotificationById(id),
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minuto
  });
};

// Hook para criar uma nova notificação
export const useCreateNotification = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateNotificationRequest) => notificationService.createNotification(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar notificação. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma notificação existente
export const useUpdateNotification = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateNotificationRequest }) => 
      notificationService.updateNotification(id, data),
    onSuccess: (updatedNotification) => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.setQueryData(['notifications', updatedNotification.id], updatedNotification);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar notificação. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma notificação
export const useDeleteNotification = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => notificationService.deleteNotification(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.removeQueries({ queryKey: ['notifications', id] });
      toast.success('Notificação excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir notificação. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para marcar notificação como lida
export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => notificationService.markAsRead(id),
    onSuccess: (updatedNotification) => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.setQueryData(['notifications', updatedNotification.id], updatedNotification);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao marcar notificação como lida. Por favor, tente novamente.'
      );
    }
  });
};
