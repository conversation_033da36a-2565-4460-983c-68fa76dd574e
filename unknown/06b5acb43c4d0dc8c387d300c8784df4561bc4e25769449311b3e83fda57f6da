import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Checkbox } from "@/components/ui/checkbox";
import { recurrenceOptions } from './constants';
import TransactionAmountField from '@/components/transactions/TransactionAmountField';

interface FormData {
  installments: boolean;
  numberOfInstallments: string;
  installmentFrequency: string;
  firstInstallmentDate: Date;
  interestAmount: string;
  discountAmount: string;
  notes: string;
}

interface InstallmentsSectionProps {
  isReadOnly: boolean;
  formData: FormData;
  openInstallmentCalendar: boolean;
  setOpenInstallmentCalendar: (open: boolean) => void;
  handleChange: (field: string, value: any) => void;
  finalAmount: () => string;
}

export const InstallmentsSection: React.FC<InstallmentsSectionProps> = ({
  isReadOnly,
  formData,
  openInstallmentCalendar,
  setOpenInstallmentCalendar,
  handleChange,
  finalAmount,
}) => {
  return (
    <>
      {!isReadOnly && (
        <div className="col-span-2 flex items-center space-x-2 mt-2">
          <Checkbox 
            id="installments" 
            checked={formData.installments}
            onCheckedChange={(checked) => 
              handleChange("installments", !!checked)
            }
            disabled={isReadOnly}
          />
          <Label htmlFor="installments" className="cursor-pointer">
            Dividir em parcelas
          </Label>
        </div>
      )}
      
      {formData.installments && !isReadOnly && (
        <div className="col-span-2 border rounded-md p-4 mt-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="numberOfInstallments">Número de parcelas</Label>
                <div className="w-5 h-5"></div>
              </div>
              <Input
                id="numberOfInstallments"
                type="number"
                min="2"
                value={formData.numberOfInstallments}
                onChange={(e) => handleChange("numberOfInstallments", e.target.value)}
                placeholder="1"
                className="mt-1"
                readOnly={isReadOnly}
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Frequência</Label>
                <div className="w-5 h-5"></div>
              </div>
              <Select 
                value={formData.installmentFrequency}
                onValueChange={(value) => handleChange("installmentFrequency", value)}
                disabled={isReadOnly}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecione a frequência..." />
                </SelectTrigger>
                <SelectContent>
                  {recurrenceOptions.filter(option => option.value !== "none").map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-2">
              <div className="flex items-center justify-between h-5">
                <Label>Data da primeira parcela</Label>
                <div className="w-5 h-5"></div>
              </div>
              <Popover open={openInstallmentCalendar} onOpenChange={setOpenInstallmentCalendar}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal mt-1"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.firstInstallmentDate ? format(formData.firstInstallmentDate, 'PPP') : <span>Selecione uma data</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.firstInstallmentDate}
                    onSelect={(date) => {
                      handleChange("firstInstallmentDate", date || new Date());
                      setOpenInstallmentCalendar(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      )}
      
      <div className="grid md:grid-cols-2 gap-4 mt-6 col-span-2">
        <div>
          <TransactionAmountField
            id="interestAmount"
            label="Juros (R$)"
            value={formData.interestAmount}
            onChange={(value) => handleChange("interestAmount", value)}
            disabled={isReadOnly}
          />
        </div>
        <div>
          <TransactionAmountField
            id="discountAmount"
            label="Desconto (R$)"
            value={formData.discountAmount}
            onChange={(value) => handleChange("discountAmount", value)}
            disabled={isReadOnly}
          />
        </div>
      </div>
      
      <div className="mt-6 col-span-2">
        <TransactionAmountField
          id="finalAmount"
          label="Valor Final (R$)"
          value={finalAmount()}
          onChange={() => {}} // Somente leitura
          disabled={true}
        />
      </div>
      
      <div className="col-span-2">
        <div className="flex items-center justify-between h-5">
          <Label htmlFor="notes">Observações</Label>
          <div className="w-5 h-5"></div>
        </div>
        <Input
          id="notes"
          value={formData.notes}
          onChange={(e) => handleChange("notes", e.target.value)}
          className="mt-1"
          readOnly={isReadOnly}
        />
      </div>
    </>
  );
}; 