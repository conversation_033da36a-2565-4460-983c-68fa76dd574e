import { Module } from '@nestjs/common';
import { CompaniesController } from '../controllers/companies.controller';
import { CompaniesService } from '../services/companies.service';
import { PrismaService } from '../services/prisma.service';
import { CompanyManagerService } from '../services/company-manager.service';
import { CompanyQueryService } from '../services/company-query.service';
import { UtilsModule } from '../utils/utils.module';

@Module({
  imports: [UtilsModule],
  controllers: [CompaniesController],
  providers: [
    CompaniesService,
    CompanyManagerService,
    CompanyQueryService,
    PrismaService
  ],
  exports: [
    CompaniesService,
    CompanyManagerService,
    CompanyQueryService
  ],
})
export class CompaniesModule {}
