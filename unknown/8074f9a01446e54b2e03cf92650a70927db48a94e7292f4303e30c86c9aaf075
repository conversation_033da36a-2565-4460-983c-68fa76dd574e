# Dependências
node_modules
/.pnp
.pnp.js

# Arquivos de build/produção
/dist
/build
/out
/dist-ssr
/frontend/dist
/backend/dist
/src-tauri/target
*.tsbuildinfo

# Arquivos de ambiente
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Arquivos de teste e cobertura
/coverage
/.nyc_output
.temp
.tmp

# Sistema operacional
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# IDEs e editores
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Cache
.eslintcache
.stylelintcache
.npm
.nx

# Arquivos específicos do projeto
bun.lockb
.next
.nuxt
.nitro
.output
.vercel
.turbo

# Arquivos específicos do banco de dados
*.sqlite
*.db
*.dump

# Arquivos de backup
*.bak
*~ frontend/
