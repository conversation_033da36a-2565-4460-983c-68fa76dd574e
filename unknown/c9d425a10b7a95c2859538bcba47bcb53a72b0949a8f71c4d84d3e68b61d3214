import React, { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import TransactionsTable from "./TransactionsTable";
import { Transaction } from "@/types/transaction";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { format, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";
import { ptBR } from "date-fns/locale";

interface TransactionTabContentProps {
  accountId?: string;
  period: "all" | "month" | "year" | "custom";
  dateRange?: { startDate?: Date; endDate?: Date };
  type: "all" | "income" | "expense" | "transfer";
  onEdit: (transaction: Transaction, mode?: "view" | "edit") => void;
}

const TransactionTabContent: React.FC<TransactionTabContentProps> = ({ 
  accountId, 
  period, 
  dateRange, 
  type,
  onEdit
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [periodMapping, setPeriodMapping] = useState<string>("current-month");
  
  useEffect(() => {
    // Converter o período para o formato esperado pelo TransactionsTable
    if (period === "month") {
      setPeriodMapping("current-month");
    } else if (period === "year") {
      setPeriodMapping("current-year");
    } else if (period === "custom") {
      setPeriodMapping("custom");
    } else {
      setPeriodMapping("all");
    }
    
    // Definir carregamento como falso após configurar o período
    setLoading(false);
  }, [accountId, period, dateRange, type]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  // Mapear o tipo para o formato esperado
  const transactionTypeMapping = {
    "income": "accounts_receivable",
    "expense": "accounts_payable",
    "transfer": "transfer",
    "all": undefined
  };

  return (
    <div className="w-full">
      <TransactionsTable 
        onEdit={onEdit}
        selectedPeriod={periodMapping}
        selectedAccountId={accountId || "all"}
        customDateRange={dateRange || {}}
        transactionType={transactionTypeMapping[type]}
      />
    </div>
  );
};

interface TransactionTabsProps {
  onEdit: (transaction: Transaction, mode?: "view" | "edit") => void;
  period: "all" | "month" | "year" | "custom";
  accountId?: string;
  dateRange?: { startDate?: Date; endDate?: Date };
}

const TransactionTabs: React.FC<TransactionTabsProps> = ({ onEdit, period, accountId, dateRange }) => {
  return (
    <Tabs defaultValue="all" className="w-full">
      <TabsList className="grid grid-cols-4 mb-4">
        <TabsTrigger value="all" className="flex items-center justify-center">
          <span className="mr-2">•</span>
          Todas
        </TabsTrigger>
        <TabsTrigger value="income" className="flex items-center justify-center">
          <span className="text-green-500 mr-2">↑</span>
          Receitas
        </TabsTrigger>
        <TabsTrigger value="expense" className="flex items-center justify-center">
          <span className="text-red-500 mr-2">↓</span>
          Despesas
        </TabsTrigger>
        <TabsTrigger value="transfer" className="flex items-center justify-center">
          <span className="text-blue-500 mr-2">⇄</span>
          Transferências
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="all">
        <TransactionTabContent 
          accountId={accountId} 
          period={period} 
          dateRange={dateRange} 
          type="all"
          onEdit={onEdit}
        />
      </TabsContent>
      <TabsContent value="income">
        <TransactionTabContent 
          accountId={accountId} 
          period={period} 
          dateRange={dateRange} 
          type="income"
          onEdit={onEdit}
        />
      </TabsContent>
      <TabsContent value="expense">
        <TransactionTabContent 
          accountId={accountId} 
          period={period} 
          dateRange={dateRange} 
          type="expense"
          onEdit={onEdit}
        />
      </TabsContent>
      <TabsContent value="transfer">
        <TransactionTabContent 
          accountId={accountId} 
          period={period} 
          dateRange={dateRange} 
          type="transfer"
          onEdit={onEdit}
        />
      </TabsContent>
    </Tabs>
  );
};

export default TransactionTabs;
