import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RecurringSchedulesService } from '../routes/recurring-schedules/recurring-schedules.service';

@Injectable()
export class RecurringTasksService {
  private readonly logger = new Logger(RecurringTasksService.name);

  constructor(private readonly recurringSchedulesService: RecurringSchedulesService) {}

  /**
   * Executa diariamente à meia-noite para gerar contas recorrentes agendadas
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generateRecurringAccounts() {
    this.logger.log('Iniciando geração de contas recorrentes agendadas...');
    
    try {
      const accountsCreated = await this.recurringSchedulesService.generateScheduledAccounts();
      
      this.logger.log(`Geração de contas recorrentes concluída. ${accountsCreated} contas foram criadas.`);
    } catch (error) {
      this.logger.error('Erro ao gerar contas recorrentes:', error);
    }
  }
}
