/**
 * Sistema de logging seguro para a aplicação FluxoMax
 * 
 * Este utilitário fornece uma interface de logging que:
 * - Em desenvolvimento: permite logs controlados para debugging
 * - Em produção: suprime logs que possam expor informações sensíveis
 * - Integra com o sistema de monitoramento para logs críticos
 */

// Tipos de log disponíveis
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// Interface para dados de contexto do log
interface LogContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

// Configuração do logger
interface LoggerConfig {
  enableConsoleInDev: boolean;
  enableMonitoringIntegration: boolean;
  maxLogLevel: LogLevel;
  sensitiveFields: string[];
}

class SecureLogger {
  private config: LoggerConfig;
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = import.meta.env.DEV || process.env.NODE_ENV === 'development';
    this.config = {
      enableConsoleInDev: this.isDevelopment,
      enableMonitoringIntegration: true,
      maxLogLevel: this.isDevelopment ? LogLevel.DEBUG : LogLevel.ERROR,
      sensitiveFields: [
        'password', 'token', 'accessToken', 'refreshToken', 'secret',
        'key', 'hash', 'email', 'cpf', 'cnpj', 'phone', 'address'
      ]
    };
  }

  /**
   * Remove campos sensíveis dos dados antes do log
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };
    
    for (const field of this.config.sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Recursivamente sanitizar objetos aninhados
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Verifica se o nível de log deve ser processado
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.CRITICAL];
    const currentLevelIndex = levels.indexOf(this.config.maxLogLevel);
    const requestedLevelIndex = levels.indexOf(level);
    
    return requestedLevelIndex >= currentLevelIndex;
  }

  /**
   * Envia log para o sistema de monitoramento
   */
  private sendToMonitoring(level: LogLevel, message: string, context?: LogContext): void {
    if (!this.config.enableMonitoringIntegration) return;

    try {
      if (typeof window !== 'undefined' && window.monitoringService) {
        const sanitizedContext = this.sanitizeData(context);
        
        if (level === LogLevel.ERROR || level === LogLevel.CRITICAL) {
          window.monitoringService.recordError({
            message,
            level,
            context: sanitizedContext,
            timestamp: new Date().toISOString()
          });
        } else {
          window.monitoringService.recordEvent('application_log', {
            level,
            message,
            context: sanitizedContext,
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      // Falha silenciosa ao enviar para monitoramento
    }
  }

  /**
   * Log de debug - apenas em desenvolvimento
   */
  debug(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const sanitizedContext = this.sanitizeData(context);
    
    if (this.config.enableConsoleInDev && this.isDevelopment) {
      console.debug(`[DEBUG] ${message}`, sanitizedContext);
    }
  }

  /**
   * Log de informação
   */
  info(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const sanitizedContext = this.sanitizeData(context);
    
    if (this.config.enableConsoleInDev && this.isDevelopment) {
      console.info(`[INFO] ${message}`, sanitizedContext);
    }
  }

  /**
   * Log de aviso
   */
  warn(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const sanitizedContext = this.sanitizeData(context);
    
    if (this.config.enableConsoleInDev && this.isDevelopment) {
      console.warn(`[WARN] ${message}`, sanitizedContext);
    }

    this.sendToMonitoring(LogLevel.WARN, message, sanitizedContext);
  }

  /**
   * Log de erro
   */
  error(message: string, error?: Error, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const sanitizedContext = this.sanitizeData(context);
    const errorContext = {
      ...sanitizedContext,
      stack: error?.stack,
      errorMessage: error?.message
    };
    
    if (this.config.enableConsoleInDev && this.isDevelopment) {
      console.error(`[ERROR] ${message}`, error, sanitizedContext);
    }

    this.sendToMonitoring(LogLevel.ERROR, message, errorContext);
  }

  /**
   * Log crítico - sempre enviado para monitoramento
   */
  critical(message: string, error?: Error, context?: LogContext): void {
    const sanitizedContext = this.sanitizeData(context);
    const errorContext = {
      ...sanitizedContext,
      stack: error?.stack,
      errorMessage: error?.message
    };

    // Logs críticos sempre vão para o monitoramento
    this.sendToMonitoring(LogLevel.CRITICAL, message, errorContext);
    
    if (this.config.enableConsoleInDev && this.isDevelopment) {
      console.error(`[CRITICAL] ${message}`, error, sanitizedContext);
    }
  }

  /**
   * Atualizar configuração do logger
   */
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Obter configuração atual
   */
  getConfig(): LoggerConfig {
    return { ...this.config };
  }
}

// Instância singleton do logger
export const secureLogger = new SecureLogger();

// Exportar métodos de conveniência
export const logger = {
  debug: (message: string, context?: LogContext) => secureLogger.debug(message, context),
  info: (message: string, context?: LogContext) => secureLogger.info(message, context),
  warn: (message: string, context?: LogContext) => secureLogger.warn(message, context),
  error: (message: string, error?: Error, context?: LogContext) => secureLogger.error(message, error, context),
  critical: (message: string, error?: Error, context?: LogContext) => secureLogger.critical(message, error, context),
};

export default secureLogger;
