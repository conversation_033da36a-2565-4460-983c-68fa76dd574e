// Utilitários para gerenciamento de cache e queries

import { QueryClient } from '@tanstack/react-query';

// Instância do QueryClient para uso em funções utilitárias
let queryClientInstance: QueryClient | null = null;

// Função para definir a instância do QueryClient
export const setQueryClient = (client: QueryClient) => {
  queryClientInstance = client;
};

// Função para invalidar o cache de contas bancárias
export const invalidateBankAccountsCache = () => {
  if (!queryClientInstance) {
    // QueryClient não está disponível - operação silenciosa
    return;
  }

  queryClientInstance.invalidateQueries({ queryKey: ['bankAccounts'] });
};
