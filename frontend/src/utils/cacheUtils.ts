// Utilitários para gerenciamento de cache

import { queryClientInstance } from '@/contexts/QueryClientContext';

// Função para invalidar o cache de contas bancárias
export const invalidateBankAccountsCache = () => {
  if (!queryClientInstance) {
    // QueryClient não está disponível - operação silenciosa
    return;
  }

  queryClientInstance.invalidateQueries({ queryKey: ['bankAccounts'] });
};

// Função para forçar uma revalidação das contas bancárias
export const refetchBankAccounts = () => {
  if (!queryClientInstance) {
    // QueryClient não está disponível - operação silenciosa
    return;
  }

  queryClientInstance.refetchQueries({ queryKey: ['bankAccounts'] });
};
