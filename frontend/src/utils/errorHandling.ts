import { AxiosError } from 'axios';
import { toast } from 'react-toastify';

/**
 * Função para tratamento padronizado de erros de API
 * @param error Erro capturado (normalmente do Axios)
 * @param defaultMessage Mensagem padrão para exibir caso não seja possível extrair uma mensagem do erro
 * @returns Rejeita a Promise com o erro tratado
 */
export const handleApiError = (error: unknown, defaultMessage = 'Ocorreu um erro na operação'): never => {
  // Erro de API - processar sem log no console
  
  // Extrair mensagem de erro
  let errorMessage = defaultMessage;
  
  if (error instanceof AxiosError) {
    // Tentar extrair mensagem de erro da resposta da API
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    // Tratamento específico para códigos de erro comuns
    if (error.response?.status === 401) {
      errorMessage = 'Sessão expirada. Por favor, faça login novamente.';
      // Redirecionar para a página de login se necessário
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    } else if (error.response?.status === 403) {
      errorMessage = 'Você não tem permissão para realizar esta operação.';
    } else if (error.response?.status === 404) {
      errorMessage = 'O recurso solicitado não foi encontrado.';
    } else if (error.response?.status === 422) {
      errorMessage = 'Dados inválidos. Verifique as informações e tente novamente.';
    } else if (error.response?.status >= 500) {
      errorMessage = 'Erro no servidor. Por favor, tente novamente mais tarde.';
    }
  } else if (error instanceof Error) {
    errorMessage = error.message;
  }
  
  // Exibir mensagem de erro usando toast
  toast.error(errorMessage);
  
  // Rejeitar a Promise com o erro tratado
  throw new Error(errorMessage);
};
