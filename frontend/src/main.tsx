// Importar o inicializador do monitoringService primeiro
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { QueryClientContext } from './contexts/QueryClientContext';

import './index.css';

// Configuração do stagewise toolbar
const stagewiseConfig = {
  plugins: []
};

// Importação condicional do stagewise toolbar
type StagewiseComponent = React.ComponentType<{ config?: unknown; enabled?: boolean }>;
let StagewiseToolbar: StagewiseComponent | null = null;

// Função para inicializar o toolbar em um React root separado
function initializeStagewiseToolbar() {
  if (!StagewiseToolbar || process.env.NODE_ENV !== 'development') return;
  
  // Criar elemento para o toolbar
  let toolbarElement = document.getElementById('stagewise-toolbar');
  if (!toolbarElement) {
    toolbarElement = document.createElement('div');
    toolbarElement.id = 'stagewise-toolbar';
    document.body.appendChild(toolbarElement);
  }
  
  // Criar React root separado para o toolbar
  const toolbarRoot = ReactDOM.createRoot(toolbarElement);
  toolbarRoot.render(
    React.createElement(StagewiseToolbar, { config: stagewiseConfig })
  );
}

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Elemento root não encontrado');
}

const root = ReactDOM.createRoot(rootElement);

// Carregamento condicional do stagewise toolbar apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  import('@stagewise/toolbar-react').then(module => {
    StagewiseToolbar = module.StagewiseToolbar;
    initializeStagewiseToolbar();
  }).catch(() => {
    // Stagewise toolbar não pôde ser carregado - falha silenciosa em produção
  });
}

// Aguardar a inicialização do monitoringService
import('./services/initializers/monitoringInitializer')
  .then(({ initializeMonitoringService }) => {

    return initializeMonitoringService();
  })
  .then(() => {

    root.render(
      <React.StrictMode>
        <QueryClientContext>
          <App />
        </QueryClientContext>
      </React.StrictMode>
    );

  })
  .catch(() => {
    // Erro na inicialização - renderizar versão básica da aplicação
    root.render(
      <div className="p-4">
        <h1>Erro na inicialização</h1>
        <p>Por favor, recarregue a página.</p>
      </div>
    );
  });
