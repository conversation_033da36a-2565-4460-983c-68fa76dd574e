import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useMonitoring } from '@/hooks/useMonitoring';

// Definição do tipo para o tema
type Theme = 'light' | 'dark' | 'system';

// Interface para o estado da aplicação
interface AppState {
  theme: Theme;
  sidebarOpen: boolean;
  notifications: Notification[];
  userPreferences: UserPreferences;
}

// Interface para notificações
interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  title?: string;
  read: boolean;
  createdAt: Date;
}

// Interface para preferências do usuário
interface UserPreferences {
  language: string;
  dateFormat: string;
  numberFormat: string;
  defaultCurrency: string;
  defaultView: string;
}

// Interface para o contexto da aplicação
interface AppContextType {
  // Estado
  state: AppState;
  
  // Métodos para gerenciar o tema
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  
  // Métodos para gerenciar a barra lateral
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  
  // Métodos para gerenciar notificações
  addNotification: (notification: Omit<Notification, 'id' | 'read' | 'createdAt'>) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
  markAllNotificationsAsRead: () => void;
  clearNotifications: () => void;
  
  // Métodos para gerenciar preferências do usuário
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
}

// Valores padrão para o contexto
const defaultUserPreferences: UserPreferences = {
  language: 'pt-BR',
  dateFormat: 'dd/MM/yyyy',
  numberFormat: 'pt-BR',
  defaultCurrency: 'BRL',
  defaultView: 'list',
};

// Estado inicial da aplicação
const initialState: AppState = {
  theme: 'system',
  sidebarOpen: true,
  notifications: [],
  userPreferences: defaultUserPreferences,
};

// Criar o contexto
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider do contexto
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<AppState>(() => {
    // Tentar carregar o estado do localStorage
    const savedState = localStorage.getItem('appState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        
        // Converter strings de data para objetos Date
        if (parsedState.notifications) {
          parsedState.notifications = parsedState.notifications.map((notification: any) => ({
            ...notification,
            createdAt: new Date(notification.createdAt),
          }));
        }
        
        return {
          ...initialState,
          ...parsedState,
        };
      } catch (error) {
        // Erro ao carregar o estado da aplicação - usar estado inicial
        return initialState;
      }
    }
    return initialState;
  });
  
  const monitoring = useMonitoring();
  
  // Salvar o estado no localStorage quando ele mudar
  useEffect(() => {
    localStorage.setItem('appState', JSON.stringify(state));
  }, [state]);
  
  // Aplicar o tema ao documento HTML
  useEffect(() => {
    const root = window.document.documentElement;
    
    // Remover classes de tema anteriores
    root.classList.remove('light', 'dark');
    
    // Aplicar o tema atual
    if (state.theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(state.theme);
    }
  }, [state.theme]);
  
  // Métodos para gerenciar o tema
  const setTheme = (theme: Theme) => {
    setState((prev) => ({ ...prev, theme }));
    monitoring.logUserAction('change_theme', { theme });
  };
  
  const toggleTheme = () => {
    setState((prev) => ({
      ...prev,
      theme: prev.theme === 'light' ? 'dark' : 'light',
    }));
    monitoring.logUserAction('toggle_theme');
  };
  
  // Métodos para gerenciar a barra lateral
  const toggleSidebar = () => {
    setState((prev) => ({
      ...prev,
      sidebarOpen: !prev.sidebarOpen,
    }));
    monitoring.logUserAction('toggle_sidebar');
  };
  
  const setSidebarOpen = (open: boolean) => {
    setState((prev) => ({ ...prev, sidebarOpen: open }));
    monitoring.logUserAction('set_sidebar', { open });
  };
  
  // Métodos para gerenciar notificações
  const addNotification = (notification: Omit<Notification, 'id' | 'read' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      read: false,
      createdAt: new Date(),
    };
    
    setState((prev) => ({
      ...prev,
      notifications: [newNotification, ...prev.notifications],
    }));
    
    monitoring.logEvent('notification_added', { type: notification.type });
  };
  
  const removeNotification = (id: string) => {
    setState((prev) => ({
      ...prev,
      notifications: prev.notifications.filter((notification) => notification.id !== id),
    }));
    monitoring.logEvent('notification_removed', { id });
  };
  
  const markNotificationAsRead = (id: string) => {
    setState((prev) => ({
      ...prev,
      notifications: prev.notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      ),
    }));
    monitoring.logEvent('notification_read', { id });
  };
  
  const markAllNotificationsAsRead = () => {
    setState((prev) => ({
      ...prev,
      notifications: prev.notifications.map((notification) => ({
        ...notification,
        read: true,
      })),
    }));
    monitoring.logEvent('all_notifications_read');
  };
  
  const clearNotifications = () => {
    setState((prev) => ({
      ...prev,
      notifications: [],
    }));
    monitoring.logEvent('notifications_cleared');
  };
  
  // Métodos para gerenciar preferências do usuário
  const updateUserPreferences = (preferences: Partial<UserPreferences>) => {
    setState((prev) => ({
      ...prev,
      userPreferences: {
        ...prev.userPreferences,
        ...preferences,
      },
    }));
    monitoring.logUserAction('update_preferences', preferences);
  };
  
  // Valor do contexto
  const contextValue: AppContextType = {
    state,
    setTheme,
    toggleTheme,
    toggleSidebar,
    setSidebarOpen,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearNotifications,
    updateUserPreferences,
  };
  
  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
}

// Hook para usar o contexto da aplicação
export function useAppContext() {
  const context = useContext(AppContext);
  
  if (context === undefined) {
    throw new Error('useAppContext deve ser usado dentro de um AppProvider');
  }
  
  return context;
}
