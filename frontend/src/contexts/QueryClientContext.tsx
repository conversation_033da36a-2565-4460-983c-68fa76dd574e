import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode, useState } from 'react';
import { monitoringService, EventType } from '@/services/monitoring/monitoringService';

// Exportar uma instância do QueryClient para uso em outros lugares
export let queryClientInstance: QueryClient;

interface QueryClientProviderProps {
  children: ReactNode;
}

export function QueryClientContext({ children }: QueryClientProviderProps) {
  const [queryClient] = useState(() => {
    const client = new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false,
          retry: 2,
          staleTime: 1000 * 60 * 5, // 5 minutos
        },
      },
    });

    // Adicionar listener global para erros de query
    client.getQueryCache().subscribe(() => {
      // Listener vazio, apenas para inicializar o cache
    });

    // Adicionar listener manual para erros de query usando o evento global
    window.addEventListener('unhandledrejection', (event) => {
      // Verificar se o erro é de uma query do React Query
      if (event.reason && typeof event.reason === 'object' && 'queryKey' in event.reason) {
        monitoringService.recordError({
          source: 'react-query',
          message: event.reason.message || 'Erro na consulta',
          queryKey: String(event.reason.queryKey),
          stack: event.reason.stack
        });
      }
    });

    // Definir a instância global para uso em outros lugares
    queryClientInstance = client;
    // console.log('QueryClient inicializado e disponível globalmente');

    return client;
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
