/**
 * Inicializador do serviço de monitoramento
 * Este arquivo deve ser importado antes de qualquer componente que utilize o serviço
 * 
 * IMPORTANTE: Este arquivo não deve importar outros serviços que possam causar dependências circulares
 */

// Definir os tipos para o objeto window
declare global {
  interface Window {
    monitoringService: any;
    monitoringServiceInitialized: boolean;
  }
}

let initializationPromise: Promise<void> | null = null;

export const initializeMonitoringService = () => {
  if (!initializationPromise) {
    initializationPromise = new Promise((resolve, reject) => {
      try {
        if (typeof window === 'undefined') {
          resolve();
          return;
        }

        if (window.monitoringServiceInitialized) {
          // Serviço já inicializado
          resolve();
          return;
        }

        import('../monitoring/monitoringService')
          .then(module => {
            window.monitoringService = module.monitoringService;
            window.monitoringServiceInitialized = true;
            // Serviço inicializado com sucesso
            resolve();
          })
          .catch(error => {
            // Erro na inicialização do monitoramento - falha silenciosa
            reject(error);
          });
      } catch (error) {
        // Erro crítico na inicialização do monitoramento - falha silenciosa
        reject(error);
      }
    });
  }
  return initializationPromise;
};

// Exportar um objeto vazio como fallback para casos em que o serviço ainda não esteja disponível
const dummyMonitoringService = {
  recordEvent: () => {}, // Operação silenciosa
  recordApiRequest: () => {}, // Operação silenciosa
  recordApiResponseSuccess: () => {}, // Operação silenciosa
  recordApiResponseError: () => {}, // Operação silenciosa
  recordAuthEvent: () => {}, // Operação silenciosa
  recordError: () => {}, // Operação silenciosa
  subscribe: () => {
    return { unsubscribe: () => {} };
  },
  getEventTypes: () => ([])
};

// Inicializar o serviço dummy como fallback
if (typeof window !== 'undefined' && !window.monitoringService) {
  window.monitoringService = dummyMonitoringService;
}

// Inicializar imediatamente
initializeMonitoringService();

export default dummyMonitoringService;
