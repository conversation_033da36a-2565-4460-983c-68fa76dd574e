/**
 * Serviço para gerenciar tokens de autenticação
 * Fornece métodos para armazenar, recuperar e limpar tokens
 */

import { monitoringService, EventType } from '@/services/monitoring/monitoringService';

interface Tokens {
  accessToken: string;
  refreshToken: string;
}

// Chaves para armazenamento no localStorage
const ACCESS_TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';
const TOKEN_EXPIRY_KEY = 'tokenExpiry';

/**
 * Armazena os tokens de acesso e refresh no localStorage com tempo de expiração
 */
const setTokens = (tokens: Tokens, expiresIn: number = 3600): void => {
  try {
    const expiryTime = Date.now() + expiresIn * 1000;
    localStorage.setItem(ACCESS_TOKEN_KEY, tokens.accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refreshToken);
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());

    // Registrar evento de armazenamento de tokens bem-sucedido
    monitoringService.recordEvent('token_storage', {
      action: 'store_tokens',
      success: true
    });

    // console.log('Tokens armazenados com sucesso. Expira em:', new Date(expiryTime).toLocaleString());
  } catch (error) {
    // Registrar erro ao armazenar tokens
    monitoringService.recordError({
      message: error instanceof Error ? error.message : 'Erro ao armazenar tokens',
      source: 'tokenStorage',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

/**
 * Recupera o token de acesso do localStorage
 */
const getAccessToken = (): string | null => {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

/**
 * Recupera o token de refresh do localStorage
 */
const getRefreshToken = (): string | null => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Verifica se o token está expirado
 */
const isTokenExpired = (): boolean => {
  const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
  if (!expiryTime) return true;

  const isExpired = Date.now() > parseInt(expiryTime, 10);
  // console.log('Verificando expiração do token:', isExpired ? 'Expirado' : 'Válido',
              // 'Expira em:', new Date(parseInt(expiryTime, 10)).toLocaleString());
  return isExpired;
};

/**
 * Remove todos os tokens do localStorage
 */
const clearTokens = (): void => {
  try {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);

    // Registrar evento de remoção de tokens bem-sucedido
    monitoringService.recordEvent('token_storage', {
      action: 'clear_tokens',
      success: true
    });

    // console.log('Tokens removidos com sucesso');
  } catch (error) {
    // Registrar erro ao remover tokens
    monitoringService.recordError({
      message: error instanceof Error ? error.message : 'Erro ao remover tokens',
      source: 'tokenStorage',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

/**
 * Verifica se existem tokens armazenados
 */
const hasTokens = (): boolean => {
  const hasAccess = !!getAccessToken();
  const hasRefresh = !!getRefreshToken();
  // console.log('Verificando tokens:', hasAccess && hasRefresh ? 'Tokens existem' : 'Tokens não existem');
  return hasAccess && hasRefresh;
};

/**
 * Verifica se o usuário está autenticado (tem tokens válidos)
 */
const isAuthenticated = (): boolean => {
  return hasTokens() && !isTokenExpired();
};

const tokenStorage = {
  setTokens,
  getAccessToken,
  getRefreshToken,
  clearTokens,
  hasTokens,
  isTokenExpired,
  isAuthenticated
};

export default tokenStorage;
