import api from './axios';
import { 
  Notification, 
  CreateNotificationRequest, 
  UpdateNotificationRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const notificationService = {
  // Listar todas as notificações com paginação
  getNotifications: async (
    page = 1, 
    limit = 10, 
    read?: boolean,
    type?: string
  ): Promise<PaginatedResponse<Notification>> => {
    const params = { page, limit, read, type };
    const response = await api.get<PaginatedResponse<Notification>>('/notifications', { params });
    return response.data;
  },
  
  // Obter notificação por ID
  getNotificationById: async (id: string): Promise<Notification> => {
    const response = await api.get<ApiResponse<Notification>>(`/notifications/${id}`);
    return response.data.data;
  },
  
  // Criar notificação
  createNotification: async (data: CreateNotificationRequest): Promise<Notification> => {
    const response = await api.post<ApiResponse<Notification>>('/notifications', data);
    return response.data.data;
  },
  
  // Atualizar notificação
  updateNotification: async (id: string, data: UpdateNotificationRequest): Promise<Notification> => {
    const response = await api.put<ApiResponse<Notification>>(`/notifications/${id}`, data);
    return response.data.data;
  },
  
  // Excluir notificação
  deleteNotification: async (id: string): Promise<void> => {
    await api.delete(`/notifications/${id}`);
  },
  
  // Marcar notificação como lida
  markAsRead: async (id: string): Promise<Notification> => {
    const response = await api.put<ApiResponse<Notification>>(`/notifications/${id}/read`, {});
    return response.data.data;
  }
};
