import axios from 'axios';
import { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Estendendo o tipo InternalAxiosRequestConfig para incluir a propriedade meta
declare module 'axios' {
  export interface InternalAxiosRequestConfig {
    meta?: {
      startTime: number;
      [key: string]: any;
    };
  }
}

// Interface para eventos de monitoramento
interface MonitoringEvent {
  eventType: string;
  timestamp: number;
  details?: any;
  userId?: string;
  sessionId?: string;
}

// Interface para métricas de requisição
interface RequestMetrics {
  url: string;
  method: string;
  startTime: number;
  endTime: number;
  duration: number;
  status?: number;
  error?: string;
}

// Gerar um ID de sessão único para o usuário atual
const generateSessionId = (): string => {
  const sessionId = localStorage.getItem('sessionId');
  if (sessionId) return sessionId;
  
  const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  localStorage.setItem('sessionId', newSessionId);
  return newSessionId;
};

// Classe para gerenciar o monitoramento
class MonitoringService {
  private sessionId: string;
  private events: MonitoringEvent[] = [];
  private metrics: RequestMetrics[] = [];
  private maxEventsInMemory: number = 100;
  private isEnabled: boolean = true;
  
  constructor() {
    this.sessionId = generateSessionId();
    this.setupInterceptors();
    
    // Enviar eventos pendentes ao inicializar
    this.sendPendingEvents();
    
    // Configurar envio periódico de eventos
    setInterval(() => this.sendPendingEvents(), 60000); // A cada minuto
    
    // Configurar envio de eventos antes do usuário sair da página
    window.addEventListener('beforeunload', () => this.sendPendingEvents());
  }
  
  // Configurar interceptores para monitorar requisições
  private setupInterceptors(): void {
    // Interceptor de requisição
    axios.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Adicionar timestamp de início à requisição
        config.meta = { ...config.meta, startTime: Date.now() };
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );
    
    // Interceptor de resposta
    axios.interceptors.response.use(
      (response: AxiosResponse) => {
        // Calcular duração da requisição
        const startTime = response.config.meta?.startTime || Date.now();
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Registrar métrica de requisição bem-sucedida
        this.recordMetric({
          url: response.config.url || '',
          method: response.config.method?.toUpperCase() || 'UNKNOWN',
          startTime,
          endTime,
          duration,
          status: response.status
        });
        
        return response;
      },
      (error: AxiosError) => {
        // Calcular duração da requisição com erro
        const startTime = error.config?.meta?.startTime || Date.now();
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Registrar métrica de requisição com erro
        this.recordMetric({
          url: error.config?.url || '',
          method: error.config?.method?.toUpperCase() || 'UNKNOWN',
          startTime,
          endTime,
          duration,
          status: error.response?.status,
          error: error.message
        });
        
        // Registrar evento de erro
        this.recordEvent('api_error', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          message: error.message,
          data: error.response?.data
        });
        
        return Promise.reject(error);
      }
    );
  }
  
  // Registrar um evento de monitoramento
  public recordEvent(eventType: string, details?: any, userId?: string): void {
    if (!this.isEnabled) return;
    
    const event: MonitoringEvent = {
      eventType,
      timestamp: Date.now(),
      details,
      userId,
      sessionId: this.sessionId
    };
    
    this.events.push(event);
    // Log de eventos desativado para reduzir ruído no console
    
    // Limitar o número de eventos em memória
    if (this.events.length > this.maxEventsInMemory) {
      this.sendPendingEvents();
    }
  }
  
  // Registrar uma métrica de requisição
  private recordMetric(metric: RequestMetrics): void {
    if (!this.isEnabled) return;
    
    this.metrics.push(metric);
    
    // Limitar o número de métricas em memória
    if (this.metrics.length > this.maxEventsInMemory) {
      this.metrics = this.metrics.slice(-this.maxEventsInMemory);
    }
    
    // Log para depuração
    // Log de métricas desativado para reduzir ruído no console
  }
  
  // Enviar eventos pendentes para o backend
  private async sendPendingEvents(): Promise<void> {
    if (!this.isEnabled || this.events.length === 0) return;
    
    try {
      // Clonar e limpar eventos antes de enviar
      const eventsToSend = [...this.events];
      this.events = [];
      
      // Em produção, enviar para o backend
      if (process.env.NODE_ENV === 'production') {
        await axios.post('/monitoring/events', { events: eventsToSend });
      }
    } catch (error) {
      // Erro ao enviar eventos de monitoramento - falha silenciosa
      // Readicionar eventos que falharam ao enviar
      // this.events = [...this.events, ...eventsToSend];
    }
  }
  
  // Obter métricas de performance
  public getPerformanceMetrics(): any {
    // Calcular métricas agregadas
    const totalRequests = this.metrics.length;
    const successfulRequests = this.metrics.filter(m => m.status && m.status >= 200 && m.status < 300).length;
    const failedRequests = this.metrics.filter(m => m.status && (m.status < 200 || m.status >= 300)).length;
    const avgDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0) / (totalRequests || 1);
    
    // Agrupar por endpoint
    const endpointMetrics: Record<string, any> = {};
    this.metrics.forEach(m => {
      const key = `${m.method} ${m.url}`;
      if (!endpointMetrics[key]) {
        endpointMetrics[key] = {
          count: 0,
          totalDuration: 0,
          failures: 0,
          avgDuration: 0
        };
      }
      
      endpointMetrics[key].count++;
      endpointMetrics[key].totalDuration += m.duration;
      if (m.status && (m.status < 200 || m.status >= 300)) {
        endpointMetrics[key].failures++;
      }
      endpointMetrics[key].avgDuration = endpointMetrics[key].totalDuration / endpointMetrics[key].count;
    });
    
    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate: totalRequests ? (successfulRequests / totalRequests) * 100 : 100,
      avgDuration,
      endpointMetrics
    };
  }
  
  // Ativar/desativar monitoramento
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Instância singleton do serviço de monitoramento
const monitoringService = new MonitoringService();

export default monitoringService;
