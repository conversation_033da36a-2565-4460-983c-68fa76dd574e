import api from './axios';
import {
  Transaction,
  CreateTransactionRequest,
  UpdateTransactionRequest,
  PaginatedResponse,
  ApiResponse
} from '@/types/api';
import { handleApiError } from '@/utils/errorHandling';

export const transactionService = {
  // Listar todas as transações com paginação
  getTransactions: async (
    page = 1,
    limit = 10,
    search?: string,
    type?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string,
    bankAccountId?: string
  ): Promise<PaginatedResponse<Transaction>> => {
    try {
      const params = { page, limit, search, type, status, startDate, endDate, categoryId, entityId, bankAccountId };
      const response = await api.get<PaginatedResponse<Transaction>>('/transactions', { params });
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar transações');
    }
  },

  // Obter transação por ID
  getTransactionById: async (id: string): Promise<Transaction> => {
    try {
      const response = await api.get<ApiResponse<Transaction>>(`/transactions/${id}`);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar detalhes da transação');
    }
  },

  // Criar transação
  createTransaction: async (data: CreateTransactionRequest): Promise<Transaction> => {
    try {
      // Enviar dados diretamente sem transformações - a API espera minúsculo
      const response = await api.post<ApiResponse<Transaction>>('/transactions', data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao criar transação');
    }
  },

  // Atualizar transação
  updateTransaction: async (id: string, data: UpdateTransactionRequest): Promise<Transaction> => {
    try {
      // Enviar dados diretamente sem transformações
      const response = await api.put<ApiResponse<Transaction>>(`/transactions/${id}`, data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao atualizar transação');
    }
  },

  // Excluir transação
  deleteTransaction: async (id: string): Promise<void> => {
    try {
      await api.delete(`/transactions/${id}`);
    } catch (error) {
      return handleApiError(error, 'Erro ao excluir transação');
    }
  },

  // Obter transações por conta bancária
  getTransactionsByBankAccount: async (
    accountId: string,
    page = 1,
    limit = 10,
    startDate?: string,
    endDate?: string,
    type?: string
  ): Promise<PaginatedResponse<Transaction>> => {
    try {
      const params = { page, limit, startDate, endDate, type };
      const response = await api.get<PaginatedResponse<Transaction>>(`/transactions/by-account/${accountId}`, { params });
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar transações da conta');
    }
  },

  // Obter resumo de transações para dashboard
  getTransactionsSummary: async (
    startDate?: string,
    endDate?: string,
    bankAccountId?: string,
    companyId?: string
  ) => {
    try {
      // Verificar se temos um UUID válido para bankAccountId
      const validatedBankAccountId = bankAccountId &&
        bankAccountId !== 'all' &&
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(bankAccountId)
          ? bankAccountId
          : undefined;

      // Obter o companyId válido
      // Primeiro, tentar usar o companyId fornecido
      let validCompanyId = undefined;

      // Verificar se o companyId fornecido é válido
      if (companyId &&
          companyId !== 'null' &&
          companyId !== 'undefined' &&
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(companyId)) {
        validCompanyId = companyId;
      } else {
        // Tentar obter do localStorage
        const storedCompanyId = localStorage.getItem('activeCompanyId');
        if (storedCompanyId &&
            storedCompanyId !== 'null' &&
            storedCompanyId !== 'undefined' &&
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(storedCompanyId)) {
          validCompanyId = storedCompanyId;
        }
      }

      // Se não temos um companyId válido, retornar dados padrão sem fazer a chamada API
      if (!validCompanyId) {
        console.warn('Nenhum ID de empresa válido disponível. Retornando dados padrão.');
        return {
          totalIncome: 0,
          totalExpense: 0,
          netChange: 0,
          pendingCount: 0,
          previousPeriodIncome: 0
        };
      }

      const params = {
        startDate,
        endDate,
        bankAccountId: validatedBankAccountId,
        companyId: validCompanyId
      };

      console.log('Chamando API de resumo com parâmetros:', params);

      const response = await api.get('/transactions/summary', { params });
      console.log('Resposta da API de resumo:', response.data);

      // Verificar se temos dados válidos na resposta
      if (!response.data || !response.data.data) {
        console.warn('Resposta da API não contém dados válidos:', response.data);
        return {
          totalIncome: 0,
          totalExpense: 0,
          netChange: 0,
          pendingCount: 0,
          previousPeriodIncome: 0
        };
      }

      return response.data.data;
    } catch (error: any) {
      console.error('Erro ao buscar resumo de transações:', error);

      // Se for um erro da API, podemos tentar extrair a mensagem de erro
      if (error.response && error.response.data) {
        console.error('Detalhes do erro da API:', error.response.data);
      }

      // Retornar valores padrão em caso de erro em vez de propagar o erro
      return {
        totalIncome: 0,
        totalExpense: 0,
        netChange: 0,
        pendingCount: 0,
        previousPeriodIncome: 0
      };
    }
  }
};
