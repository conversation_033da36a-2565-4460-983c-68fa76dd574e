import api from './axios';
import { Company, CreateCompanyRequest, UpdateCompanyRequest, CompanySettings, PaginatedResponse, ApiResponse } from '@/types/api';

export const companyService = {
  // Listar todas as empresas com paginação
  getCompanies: async (page = 1, limit = 10, search?: string): Promise<PaginatedResponse<Company>> => {
    const params = { page, limit, search };
    console.log('[companyService.getCompanies] Parâmetros:', params);
    
    const response = await api.get('/companies', { params });
    console.log('[companyService.getCompanies] Resposta completa:', response);
    console.log('[companyService.getCompanies] Status:', response.status);
    console.log('[companyService.getCompanies] Headers:', response.headers);
    console.log('[companyService.getCompanies] Data:', response.data);

    // Adaptar a resposta do backend para o formato esperado pelo frontend
    const backendResponse = response.data;

    // Verificar se a resposta tem a estrutura esperada
    if (!backendResponse || !backendResponse.data) {
      console.error('Resposta de empresas inválida:', backendResponse);
      return {
        data: [],
        total: 0,
        page: page,
        limit: limit,
        totalPages: 0
      };
    }

    // Mapear a resposta do backend para o formato esperado pelo frontend
    const result = {
      data: backendResponse.data,
      total: backendResponse.total || 0,
      page: backendResponse.page || page,
      limit: backendResponse.limit || limit,
      totalPages: Math.ceil((backendResponse.total || 0) / (backendResponse.limit || limit))
    };
    
    console.log('[companyService.getCompanies] Resultado formatado:', result);
    return result;
  },

  // Obter empresa por ID
  getCompanyById: async (id: string): Promise<Company> => {
    const response = await api.get<ApiResponse<Company>>(`/companies/${id}`);
    return response.data.data;
  },

  // Criar empresa
  createCompany: async (data: CreateCompanyRequest): Promise<Company> => {
    const response = await api.post<ApiResponse<Company>>('/companies', data);
    return response.data.data;
  },

  // Atualizar empresa
  updateCompany: async (id: string, data: UpdateCompanyRequest): Promise<Company> => {
    const response = await api.put<ApiResponse<Company>>(`/companies/${id}`, data);
    return response.data.data;
  },

  // Excluir empresa
  deleteCompany: async (id: string): Promise<void> => {
    await api.delete(`/companies/${id}`);
  },

  // Obter configurações da empresa
  getCompanySettings: async (id: string): Promise<CompanySettings> => {
    const response = await api.get<ApiResponse<CompanySettings>>(`/companies/${id}/settings`);
    return response.data.data;
  }
};
