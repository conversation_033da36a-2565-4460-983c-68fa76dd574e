import api from './axios';
import { LoginRequest, LoginResponse, RegisterRequest, User, RefreshTokenRequest } from '@/types/api';
import { monitoringService, EventType } from '@/services/monitoring/monitoringService';

// Importar o serviço de armazenamento de tokens unificado
import tokenStorage from './tokenStorage';

export const authService = {
  // Exportar funções de gerenciamento de tokens para uso externo
  tokenStorage,

  // Login
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    try {
      // Registrar tentativa de login
      monitoringService.recordAuthEvent(EventType.AUTH_LOGIN, {
        action: 'login_attempt',
        email: data.email
      });

      // Fazer login e obter tokens
      const response = await api.post<{ accessToken: string; refreshToken: string }>('/auth/login', data);
      console.log('Login response:', response.data);

      // Verificar se a resposta tem a estrutura esperada
      if (!response.data || !response.data.accessToken || !response.data.refreshToken) {
        throw new Error('Resposta de login inválida');
      }

      const { accessToken, refreshToken } = response.data;

      // Salvar tokens no localStorage com tempo de expiração (1 hora por padrão)
      tokenStorage.setTokens({ accessToken, refreshToken }, 3600);

      // Buscar perfil do usuário
      const profileResponse = await api.get<User>('/users/profile/me');
      const user = profileResponse.data;

      if (!user) {
        throw new Error('Erro ao buscar perfil do usuário');
      }

      // Registrar login bem-sucedido
      monitoringService.recordAuthEvent(EventType.AUTH_LOGIN, {
        action: 'login_success',
        userId: user.id,
        email: user.email || data.email,
        success: true
      });

      // Retornar resposta completa
      return {
        accessToken,
        refreshToken,
        user
      };
    } catch (error) {
      // Registrar falha no login
      monitoringService.recordAuthEvent(EventType.AUTH_LOGIN_ERROR, {
        action: 'login_failure',
        email: data.email,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Erro durante o login:', error);
      throw error;
    }
  },

  // Registro
  register: async (data: RegisterRequest): Promise<void> => {
    try {
      // Registrar tentativa de registro
      monitoringService.recordAuthEvent(EventType.AUTH_REGISTER, {
        action: 'register_attempt',
        email: data.email
      });

      await api.post('/auth/register', data);

      // Registrar registro bem-sucedido
      monitoringService.recordAuthEvent(EventType.AUTH_REGISTER, {
        action: 'register_success',
        email: data.email,
        success: true
      });
    } catch (error) {
      // Registrar falha no registro
      monitoringService.recordAuthEvent(EventType.AUTH_REGISTER_ERROR, {
        action: 'register_failure',
        email: data.email,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Erro durante o registro - propagar erro sem log
      throw error;
    }
  },

  // Obter perfil do usuário atual
  getProfile: async (): Promise<User> => {
    try {
      const response = await api.get<User>('/users/profile');
      return response.data;
    } catch (error) {
      console.error('Erro ao obter perfil do usuário:', error);
      throw error;
    }
  },

  // Renovar token
  refreshToken: async (): Promise<LoginResponse | null> => {
    try {
      const refreshToken = tokenStorage.getRefreshToken();

      if (!refreshToken) {
        throw new Error('Refresh token não encontrado');
      }

      const response = await api.post<LoginResponse>('/auth/refresh-token', { refreshToken });
      const { accessToken, refreshToken: newRefreshToken } = response.data;

      // Atualizar tokens no localStorage com tempo de expiração (1 hora por padrão)
      tokenStorage.setTokens({
        accessToken,
        refreshToken: newRefreshToken || refreshToken
      }, 3600);

      console.log('Token renovado com sucesso');

      return response.data;
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      // Em caso de erro no refresh, limpar tokens
      tokenStorage.clearTokens();
      return null;
    }
  },

  // Verificar se o usuário está autenticado
  isAuthenticated: (): boolean => {
    const isAuth = tokenStorage.isAuthenticated();
    console.log('Verificando autenticação:', isAuth ? 'Autenticado' : 'Não autenticado');
    return isAuth;
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      // Registrar tentativa de logout
      monitoringService.recordAuthEvent(EventType.AUTH_LOGOUT, {
        action: 'logout_attempt'
      });

      const refreshToken = tokenStorage.getRefreshToken();

      if (refreshToken) {
        // Notificar o backend sobre o logout
        await api.post('/auth/logout', { refreshToken });
      }

      // Registrar logout bem-sucedido
      monitoringService.recordAuthEvent(EventType.AUTH_LOGOUT, {
        action: 'logout_success',
        success: true
      });
    } catch (error) {
      // Registrar erro no logout
      monitoringService.recordAuthEvent(EventType.AUTH_LOGOUT_ERROR, {
        action: 'logout_error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Erro durante o logout:', error);
      // Continuar com o logout mesmo em caso de erro
    } finally {
      // Limpar tokens independentemente do resultado da requisição
      tokenStorage.clearTokens();
    }
  },

  // Solicitar redefinição de senha
  forgotPassword: async (email: string): Promise<void> => {
    try {
      // Registrar tentativa de recuperação de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET_REQUEST, {
        action: 'password_reset_request',
        email
      });

      await api.post('/auth/forgot-password', { email });

      // Registrar sucesso na solicitação de recuperação
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET_REQUEST, {
        action: 'password_reset_request_success',
        email,
        success: true
      });
    } catch (error) {
      // Registrar erro na solicitação de recuperação
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET_ERROR, {
        action: 'password_reset_request_error',
        email,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Erro ao solicitar redefinição de senha:', error);
      throw error;
    }
  },

  // Redefinir senha com token
  resetPassword: async (token: string, password: string, passwordConfirmation: string): Promise<void> => {
    try {
      // Registrar tentativa de redefinição de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET, {
        action: 'password_reset_attempt'
      });

      await api.post('/auth/reset-password', { token, password, passwordConfirmation });

      // Registrar sucesso na redefinição de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET, {
        action: 'password_reset_success',
        success: true
      });
    } catch (error) {
      // Registrar erro na redefinição de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_RESET_ERROR, {
        action: 'password_reset_error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Erro ao redefinir senha:', error);
      throw error;
    }
  },

  // Alterar senha do usuário logado
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      // Registrar tentativa de alteração de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_CHANGE, {
        action: 'password_change_attempt'
      });

      await api.post('/auth/change-password', { currentPassword, newPassword });

      // Registrar sucesso na alteração de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_CHANGE, {
        action: 'password_change_success',
        success: true
      });
    } catch (error) {
      // Registrar erro na alteração de senha
      monitoringService.recordAuthEvent(EventType.AUTH_PASSWORD_CHANGE_ERROR, {
        action: 'password_change_error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error('Erro ao alterar senha:', error);
      throw error;
    }
  }
};
