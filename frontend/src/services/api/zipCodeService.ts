import api from './axios';
import { 
  ZipCodeResponse, 
  CacheZipCodeRequest, 
  ApiResponse 
} from '@/types/api';

export const zipCodeService = {
  // Buscar CEP
  getZipCode: async (cep: string): Promise<ZipCodeResponse> => {
    const response = await api.get<ApiResponse<ZipCodeResponse>>(`/zip-codes/${cep}`);
    return response.data.data;
  },
  
  // Armazenar CEP em cache
  cacheZipCode: async (data: CacheZipCodeRequest): Promise<void> => {
    await api.post('/zip-codes/cache', data);
  }
};
