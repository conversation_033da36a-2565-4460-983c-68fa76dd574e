import api from './axios';
import { handleApiError } from '@/utils/errorHandling';
import { ApiResponse, PaginatedResponse } from '@/types/api';

export interface RecurrenceType {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateRecurrenceTypeRequest {
  name: string;
  description?: string;
}

export interface UpdateRecurrenceTypeRequest {
  name?: string;
  description?: string;
}

export const recurrenceTypeService = {
  // Listar todos os tipos de recorrência
  getRecurrenceTypes: async (
    page = 1,
    limit = 50,
    search?: string
  ): Promise<PaginatedResponse<RecurrenceType>> => {
    try {
      const params = { page, limit, search };
      const response = await api.get<PaginatedResponse<RecurrenceType>>('/recurrence-types', { params });
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar tipos de recorrência');
    }
  },

  // Obter tipo de recorrência por ID
  getRecurrenceTypeById: async (id: string): Promise<RecurrenceType> => {
    try {
      const response = await api.get<ApiResponse<RecurrenceType>>(`/recurrence-types/${id}`);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar tipo de recorrência');
    }
  },

  // Criar tipo de recorrência
  createRecurrenceType: async (data: CreateRecurrenceTypeRequest): Promise<RecurrenceType> => {
    try {
      const response = await api.post<ApiResponse<RecurrenceType>>('/recurrence-types', data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao criar tipo de recorrência');
    }
  },

  // Atualizar tipo de recorrência
  updateRecurrenceType: async (id: string, data: UpdateRecurrenceTypeRequest): Promise<RecurrenceType> => {
    try {
      const response = await api.put<ApiResponse<RecurrenceType>>(`/recurrence-types/${id}`, data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao atualizar tipo de recorrência');
    }
  },

  // Excluir tipo de recorrência
  deleteRecurrenceType: async (id: string): Promise<void> => {
    try {
      await api.delete(`/recurrence-types/${id}`);
    } catch (error) {
      return handleApiError(error, 'Erro ao excluir tipo de recorrência');
    }
  },
  
  // Obter todos os tipos de recorrência ativos (sem paginação)
  getAllActiveRecurrenceTypes: async (): Promise<RecurrenceType[]> => {
    try {
      const response = await api.get<RecurrenceType[]>('/recurrence-types/active');
      return response.data;
    } catch (error) {
      handleApiError(error, 'Erro ao buscar tipos de recorrência ativos');
      return []; // Fallback para array vazio
    }
  }
};
