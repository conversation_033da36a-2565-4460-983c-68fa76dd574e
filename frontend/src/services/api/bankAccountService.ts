import api from './axios';
import {
  BankAccount,
  CreateBankAccountRequest,
  UpdateBankAccountRequest,
  BankAccountBalance,
  PaginatedResponse,
  ApiResponse
} from '@/types/api';

export const bankAccountService = {
  // Listar todas as contas bancárias com paginação
  getBankAccounts: async (
    page = 1,
    limit = 10,
    search?: string,
    type?: string,
    bankId?: string,
    companyId?: string
  ): Promise<PaginatedResponse<BankAccount>> => {
    try {
      // Usar diretamente o companyId passado como parâmetro, que vem do AuthContext via hook.
      const effectiveCompanyId = companyId;

      if (!effectiveCompanyId) {
        console.warn('bankAccountService.getBankAccounts - Nenhum companyId disponível do parâmetro.');
        // Retornar vazio se não houver companyId
        return { data: [], total: 0, page, limit, totalPages: 0 };
      }

      console.log('bankAccountService.getBankAccounts - effectiveCompanyId:', effectiveCompanyId);

      // Verificar se estamos em modo de desenvolvimento
      const isDev = process.env.NODE_ENV === 'development';
      const useMock = false; // Definido como false para usar o backend real

      if (isDev && useMock) {
        console.log('Usando mock para listar contas bancárias');

        // Verificar se há contas bancárias no localStorage
        const mockAccountsStr = localStorage.getItem('mockBankAccounts');
        let mockAccounts: BankAccount[] = [];

        if (mockAccountsStr) {
          try {
            mockAccounts = JSON.parse(mockAccountsStr);
          } catch (e) {
            console.error('Erro ao parsear contas bancárias do localStorage:', e);
          }
        }

        // Se não houver contas bancárias no localStorage, criar algumas contas de exemplo
        if (!mockAccounts || mockAccounts.length === 0) {
          mockAccounts = [
            {
              id: '1',
              name: 'Conta Corrente',
              accountNumber: '12345-6',
              agency: '0001',
              bankId: '9b706d56-b96c-4c13-8420-fe3f3639d688',
              bank: undefined,
              initialBalance: 1000,
              currentBalance: 1500,
              type: 'checking',
              companyId: '9b706d56-b96c-4c13-8420-fe3f3639d688',
              company: undefined,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: '2',
              name: 'Conta Poupança',
              accountNumber: '98765-4',
              agency: '0001',
              bankId: '9b706d56-b96c-4c13-8420-fe3f3639d688',
              bank: undefined,
              initialBalance: 5000,
              currentBalance: 5500,
              type: 'savings',
              companyId: '9b706d56-b96c-4c13-8420-fe3f3639d688',
              company: undefined,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ];

          // Salvar contas bancárias no localStorage
          localStorage.setItem('mockBankAccounts', JSON.stringify(mockAccounts));
        }

        // Filtrar contas bancárias de acordo com os parâmetros
        let filteredAccounts = [...mockAccounts];

        if (search) {
          const searchLower = search.toLowerCase();
          filteredAccounts = filteredAccounts.filter(account =>
            account.name.toLowerCase().includes(searchLower) ||
            account.accountNumber.toLowerCase().includes(searchLower)
          );
        }

        if (type) {
          filteredAccounts = filteredAccounts.filter(account => account.type === type);
        }

        if (bankId) {
          filteredAccounts = filteredAccounts.filter(account => account.bankId === bankId);
        }

        if (effectiveCompanyId) {
          filteredAccounts = filteredAccounts.filter(account => account.companyId === effectiveCompanyId);
        }

        // Paginar resultados
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex);

        const mockResponse: PaginatedResponse<BankAccount> = {
          data: paginatedAccounts,
          total: filteredAccounts.length,
          page,
          limit,
          totalPages: Math.ceil(filteredAccounts.length / limit)
        };

        console.log('bankAccountService.getBankAccounts - mockResponse:', mockResponse);
        return mockResponse;
      }

      // Usar o backend real
      const params = { page, limit, search, type, bankId, companyId: effectiveCompanyId }; // Garantir que usa o effectiveCompanyId
      console.log('bankAccountService.getBankAccounts - params:', params);
      console.log('bankAccountService.getBankAccounts - effectiveCompanyId:', effectiveCompanyId);
      console.log('bankAccountService.getBankAccounts - localStorage activeCompanyId:', localStorage.getItem('activeCompanyId'));

      const response = await api.get('/bank-accounts', { params });
      console.log('bankAccountService.getBankAccounts - response:', response.data);
      console.log('bankAccountService.getBankAccounts - response.status:', response.status);
      console.log('bankAccountService.getBankAccounts - response.headers:', response.headers);

      // Adaptar a resposta do backend para o formato esperado pelo frontend
      const backendResponse = response.data;

      // Verificar se a resposta tem a estrutura esperada
      if (!backendResponse || !backendResponse.data) {
        console.error('Resposta de contas bancárias inválida:', backendResponse);
        return {
          data: [],
          total: 0,
          page: page,
          limit: limit,
          totalPages: 0
        };
      }

      // A filtragem por companyId e status de exclusão deve ser feita no backend.
      // O backend já retorna os dados paginados corretamente.

      console.log('Dados recebidos do backend:', backendResponse);

      // Mapear a resposta do backend para o formato esperado pelo frontend
      // Mapear os tipos de conta do backend para o frontend
      const mappedData = backendResponse.data.map((account: any) => ({
        ...account,
        type: account.accountType === 'corrente' ? 'checking' :
              account.accountType === 'poupanca' ? 'savings' :
              account.accountType === 'investimento' ? 'investment' :
              account.accountType === 'dinheiro' ? 'cash' : 'other'
      }));

      return {
        data: mappedData,
        total: backendResponse.total,
        page: backendResponse.page || page,
        limit: backendResponse.limit || limit,
        totalPages: Math.ceil(backendResponse.total / (backendResponse.limit || limit))
      };
    } catch (error) {
      console.error('Erro ao buscar contas bancárias:', error);
      // Retornar um objeto vazio em caso de erro
      return {
        data: [],
        total: 0,
        page: page,
        limit: limit,
        totalPages: 1
      };
    }
  },

  // Obter conta bancária por ID
  getBankAccountById: async (id: string): Promise<BankAccount> => {
    try {
      // Verificar se estamos em modo de desenvolvimento
      const isDev = process.env.NODE_ENV === 'development';
      const useMock = false; // Definido como false para usar o backend real

      if (isDev && useMock) {
        console.log('Usando mock para buscar conta bancária por ID:', id);

        // Buscar a conta bancária no mock
        const mockAccountsStr = localStorage.getItem('mockBankAccounts');
        let mockAccounts: BankAccount[] = [];

        if (mockAccountsStr) {
          try {
            mockAccounts = JSON.parse(mockAccountsStr);
            const account = mockAccounts.find(account => account.id === id);

            if (account) {
              console.log('Conta bancária encontrada no mock:', account);
              return account;
            }
          } catch (e) {
            console.error('Erro ao parsear contas bancárias do localStorage:', e);
          }
        }

        throw new Error('Conta bancária não encontrada');
      }

      // Usar o backend real
      const response = await api.get(`/bank-accounts/${id}`);
      
      // Mapear a resposta do backend para o formato esperado pelo frontend
      const backendAccount = response.data;
      const frontendAccount: BankAccount = {
        ...backendAccount,
        type: backendAccount.accountType === 'corrente' ? 'checking' :
              backendAccount.accountType === 'poupanca' ? 'savings' :
              backendAccount.accountType === 'investimento' ? 'investment' :
              backendAccount.accountType === 'dinheiro' ? 'cash' : 'other'
      };

      return frontendAccount;
    } catch (error) {
      console.error('Erro ao buscar conta bancária por ID:', error);
      throw error;
    }
  },

  // Criar conta bancária
  createBankAccount: async (data: any): Promise<BankAccount> => {
    try {
      console.log('Enviando dados para criar conta bancária:', data);

      // Verificar se há campos não esperados pelo backend e removê-los
      const cleanedData = { ...data };

      // Remover campos que não são esperados pelo backend
      if ('agency' in cleanedData) {
        console.warn('Removendo campo "agency" que não é esperado pelo backend');
        delete cleanedData.agency;
      }

      // Verificar se estamos em modo de desenvolvimento
      const isDev = process.env.NODE_ENV === 'development';
      const useMock = false; // Definido como false para usar o backend real

      if (isDev && useMock) {
        // Mock da resposta para testes
        console.log('Usando mock para criar conta bancária');

        // Simular um atraso de 1 segundo
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Criar um ID aleatório
        const id = Math.random().toString(36).substring(2, 15) +
                  Math.random().toString(36).substring(2, 15);

        // Criar uma resposta mock
        const mockResponse: BankAccount = {
          id,
          name: cleanedData.name,
          accountNumber: cleanedData.accountNumber,
          // Campo agency removido
          bankId: cleanedData.bankId,
          bank: undefined,
          initialBalance: cleanedData.initialBalance || cleanedData.balance || 0,
          currentBalance: cleanedData.currentBalance || cleanedData.balance || 0,
          type: cleanedData.accountType === 'corrente' ? 'checking' :
                cleanedData.accountType === 'poupanca' ? 'savings' :
                cleanedData.accountType === 'investimento' ? 'investment' :
                cleanedData.accountType === 'dinheiro' ? 'cash' : 'other',
          companyId: '9b706d56-b96c-4c13-8420-fe3f3639d688',
          company: undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Adicionar a nova conta bancária ao mock
        const mockAccountsStr = localStorage.getItem('mockBankAccounts');
        let mockAccounts: BankAccount[] = [];

        if (mockAccountsStr) {
          try {
            mockAccounts = JSON.parse(mockAccountsStr);
          } catch (e) {
            console.error('Erro ao parsear contas bancárias do localStorage:', e);
          }
        }

        mockAccounts.push(mockResponse);
        localStorage.setItem('mockBankAccounts', JSON.stringify(mockAccounts));

        console.log('Resposta mock:', mockResponse);
        return mockResponse;
      }

      // Usar o backend real com dados limpos
      console.log('Enviando dados limpos para o backend:', cleanedData);
      const response = await api.post('/bank-accounts', cleanedData);
      console.log('Resposta da API ao criar conta bancária:', response.data);
      
      // Mapear a resposta do backend para o formato esperado pelo frontend
      const backendAccount = response.data;
      const frontendAccount: BankAccount = {
        ...backendAccount,
        type: backendAccount.accountType === 'corrente' ? 'checking' :
              backendAccount.accountType === 'poupanca' ? 'savings' :
              backendAccount.accountType === 'investimento' ? 'investment' :
              backendAccount.accountType === 'dinheiro' ? 'cash' : 'other'
      };

      console.log('Dados mapeados para o frontend:', frontendAccount);
      return frontendAccount;
    } catch (error: any) {
      console.error('Erro ao criar conta bancária no serviço:', error);

      let errorMessage = 'Erro ao criar conta bancária. Por favor, tente novamente.';

      if (error.response && error.response.data) {
        console.error('Detalhes do erro da API:', error.response.data);

        // Exibir mensagens de erro mais detalhadas
        if (error.response.data.message) {
          console.error('Mensagens de erro detalhadas:');
          if (Array.isArray(error.response.data.message)) {
            error.response.data.message.forEach((msg: string, index: number) => {
              console.error(`${index + 1}. ${msg}`);
            });
            errorMessage = error.response.data.message.join(', ');
          } else {
            errorMessage = error.response.data.message;
          }
        }
      }

      // Criar um erro personalizado com a mensagem de erro
      const enhancedError = new Error(errorMessage);
      Object.assign(enhancedError, error);
      throw enhancedError;
    }
  },

  // Atualizar conta bancária
  updateBankAccount: async (id: string, data: UpdateBankAccountRequest): Promise<BankAccount> => {
    try {
      console.log('Atualizando conta bancária com ID:', id, 'Dados:', data);

      // Mapear os campos do frontend para o formato esperado pelo backend
      const backendData: any = {};

      // Mapear apenas os campos que o backend espera
      if (data.name !== undefined) backendData.name = data.name;
      if (data.accountNumber !== undefined) backendData.accountNumber = data.accountNumber;

      // Converter 'type' para 'accountType' - manter formato do frontend para compatibilidade
      if (data.type !== undefined) {
        backendData.accountType = data.type; // Enviar diretamente o valor do frontend
      }

      // Incluir bankId sempre que estiver presente (mesmo se vazio para desconectar)
      if (data.bankId !== undefined) {
        backendData.bankId = data.bankId || null;
      }

      // Incluir initialBalance se fornecido
      if (data.initialBalance !== undefined) {
        backendData.initialBalance = data.initialBalance;
      }

      console.log('Dados mapeados para o backend:', backendData);

      // Usar o backend real com os dados mapeados
      const response = await api.put(`/bank-accounts/${id}`, backendData);
      console.log('Resposta da atualização:', response.status, response.data);

      // Mapear a resposta do backend para o formato esperado pelo frontend
      const backendAccount = response.data;
      console.log('UPDATE - Dados do backend ANTES do mapeamento:', backendAccount);
      console.log('UPDATE - accountType do backend:', backendAccount.accountType);

      const frontendAccount: BankAccount = {
        ...backendAccount,
        // Mapear accountType para type, mantendo compatibilidade com ambos os formatos
        type: backendAccount.accountType === 'corrente' ? 'checking' :
              backendAccount.accountType === 'poupanca' ? 'savings' :
              backendAccount.accountType === 'investimento' ? 'investment' :
              backendAccount.accountType === 'dinheiro' ? 'cash' :
              backendAccount.accountType === 'checking' ? 'checking' :
              backendAccount.accountType === 'savings' ? 'savings' :
              backendAccount.accountType === 'investment' ? 'investment' :
              backendAccount.accountType === 'cash' ? 'cash' : 'other'
      };

      console.log('UPDATE - Dados mapeados para o frontend DEPOIS:', frontendAccount);
      console.log('UPDATE - type mapeado para o frontend:', frontendAccount.type);
      console.log('UPDATE - frontendAccount tem accountType?', 'accountType' in frontendAccount);
      return frontendAccount;
    } catch (error) {
      console.error('Erro ao atualizar conta bancária:', error);
      throw error;
    }
  },

  // Excluir conta bancária
  deleteBankAccount: async (id: string): Promise<void> => {
    try {
      console.log('Excluindo conta bancária com ID:', id);

      // Verificar se temos um token válido antes de fazer a requisição
      const hasToken = api.defaults.headers.common['Authorization'];
      if (!hasToken) {
        console.warn('Tentando excluir conta sem token de autenticação');
      }

      // Usar o backend real com tratamento de erro mais detalhado
      const response = await api.delete(`/bank-accounts/${id}`);
      console.log('Resposta da exclusão:', response.status);

      // Verificar se a exclusão foi bem-sucedida
      if (response.status === 204 || response.status === 200) {
        console.log('Exclusão bem-sucedida com status:', response.status);
        return;
      } else {
        console.warn('Exclusão retornou status inesperado:', response.status);
        throw new Error(`Exclusão falhou com status ${response.status}`);
      }
    } catch (error: any) {
      console.error('Erro ao excluir conta bancária:', error);

      // Adicionar mais detalhes sobre o erro
      if (error.response) {
        console.error('Detalhes do erro:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      }

      throw error;
    }
  },

  // Obter saldo da conta bancária
  getBankAccountBalance: async (id: string): Promise<BankAccountBalance> => {
    const response = await api.get(`/bank-accounts/${id}/balance`);
    return response.data;
  }
};
