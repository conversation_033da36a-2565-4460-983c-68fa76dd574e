import api from './axios';
import { User, CreateUserRequest, UpdateUserRequest, PaginatedResponse, ApiResponse, UserProfile } from '@/types/api';

export const userService = {
  // Listar todos os usuários com paginação
  getUsers: async (page = 1, limit = 10, search?: string): Promise<PaginatedResponse<User>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<User>>('/users', { params });
    return response.data;
  },

  // Obter usuário por ID
  getUserById: async (id: string): Promise<User> => {
    const response = await api.get<ApiResponse<User>>(`/users/${id}`);
    return response.data.data;
  },

  // Criar usuário
  createUser: async (data: CreateUserRequest): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/users', data);
    return response.data.data;
  },

  // Atualizar usuário
  updateUser: async (id: string, data: UpdateUserRequest): Promise<User> => {
    const response = await api.put<ApiResponse<User>>(`/users/${id}`, data);
    return response.data.data;
  },

  // Excluir usuário
  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/users/${id}`);
  },

  // Obter perfil do usuário autenticado
  getProfile: async (): Promise<User> => {
    try {
      const response = await api.get('/users/profile/me');

      // Verificar a estrutura da resposta e fazer log para debug
      // console.log('Resposta completa da API de perfil:', response);

      // Verificar se a resposta contém dados válidos
      // Primeiro, tentar acessar response.data.data (estrutura esperada)
      if (response.data && response.data.data) {
        return response.data.data;
      }

      // Se não encontrar na estrutura esperada, verificar se os dados estão diretamente em response.data
      if (response.data && typeof response.data === 'object' && 'id' in response.data) {
        return response.data as User;
      }

      // Se ainda não encontrou dados válidos, criar um objeto de usuário mínimo para evitar erros
      console.warn('Estrutura de resposta não reconhecida, criando usuário padrão');
      return {
        id: 'temp-user-id',
        email: '<EMAIL>',
        status: 'active',
        profile: {
          username: 'Usuário',
          firstName: 'Usuário',
          lastName: 'Temporário',
          isActive: false
        },
        role: {
          id: 'temp-role-id',
          name: 'Usuário',
          permissions: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Erro ao buscar perfil do usuário:', error);
      // Retornar um usuário padrão em vez de lançar erro
      return {
        id: 'temp-user-id',
        email: '<EMAIL>',
        status: 'active',
        profile: {
          username: 'Usuário',
          firstName: 'Usuário',
          lastName: 'Temporário',
          isActive: false
        },
        role: {
          id: 'temp-role-id',
          name: 'Usuário',
          permissions: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    }
  },

  // Atualizar perfil do usuário autenticado
  updateProfile: async (data: Partial<UserProfile>): Promise<User> => {
    const response = await api.put<ApiResponse<User>>('/users/profile/me', data);
    return response.data.data;
  },

  // Obter todos os usuários (para admin)
  getAllUsers: async (page = 1, limit = 10, search?: string): Promise<PaginatedResponse<User>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<User>>('/admin/users', { params });
    return response.data;
  },

  // Obter usuário por ID (para admin)
  getUserAdmin: async (id: string): Promise<User> => {
    const response = await api.get<ApiResponse<User>>(`/admin/users/${id}`);
    return response.data.data;
  },

  // Criar usuário (para admin)
  createUserAdmin: async (data: CreateUserRequest): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/admin/users', data);
    return response.data.data;
  },

  // Atualizar usuário (para admin)
  updateUserAdmin: async (id: string, data: UpdateUserRequest): Promise<User> => {
    const response = await api.put<ApiResponse<User>>(`/admin/users/${id}`, data);
    return response.data.data;
  },

  // Excluir usuário (para admin)
  deleteUserAdmin: async (id: string): Promise<void> => {
    await api.delete(`/admin/users/${id}`);
  }
};
