import api from './axios';
import { handleApiError } from '@/utils/errorHandling';
import { ApiResponse, PaginatedResponse } from '@/types/api';

export interface PaymentMethod {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePaymentMethodRequest {
  name: string;
  description?: string;
}

export interface UpdatePaymentMethodRequest {
  name?: string;
  description?: string;
}

export const paymentMethodService = {
  // Listar todos os métodos de pagamento
  getPaymentMethods: async (
    page = 1,
    limit = 50,
    search?: string
  ): Promise<PaginatedResponse<PaymentMethod>> => {
    try {
      const params = { page, limit, search };
      const response = await api.get<PaginatedResponse<PaymentMethod>>('/payment-methods', { params });
      return response.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar métodos de pagamento');
    }
  },

  // Obter método de pagamento por ID
  getPaymentMethodById: async (id: string): Promise<PaymentMethod> => {
    try {
      const response = await api.get<ApiResponse<PaymentMethod>>(`/payment-methods/${id}`);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao buscar método de pagamento');
    }
  },

  // Criar método de pagamento
  createPaymentMethod: async (data: CreatePaymentMethodRequest): Promise<PaymentMethod> => {
    try {
      const response = await api.post<ApiResponse<PaymentMethod>>('/payment-methods', data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao criar método de pagamento');
    }
  },

  // Atualizar método de pagamento
  updatePaymentMethod: async (id: string, data: UpdatePaymentMethodRequest): Promise<PaymentMethod> => {
    try {
      const response = await api.put<ApiResponse<PaymentMethod>>(`/payment-methods/${id}`, data);
      return response.data.data;
    } catch (error) {
      return handleApiError(error, 'Erro ao atualizar método de pagamento');
    }
  },

  // Excluir método de pagamento
  deletePaymentMethod: async (id: string): Promise<void> => {
    try {
      await api.delete(`/payment-methods/${id}`);
    } catch (error) {
      return handleApiError(error, 'Erro ao excluir método de pagamento');
    }
  },
  
  // Obter todos os métodos de pagamento ativos (sem paginação)
  getAllActivePaymentMethods: async (): Promise<PaymentMethod[]> => {
    try {
      const response = await api.get<PaymentMethod[]>('/payment-methods/active');
      return response.data;
    } catch (error) {
      handleApiError(error, 'Erro ao buscar métodos de pagamento ativos');
      return []; // Fallback para array vazio
    }
  }
};
