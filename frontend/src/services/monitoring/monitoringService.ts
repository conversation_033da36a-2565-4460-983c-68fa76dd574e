/**
 * Serviço de monitoramento para registrar eventos da aplicação
 * Utiliza o padrão Observer através do EventBus para evitar dependências circulares
 *
 * IMPORTANTE: Este arquivo foi modificado para auto-registrar-se no objeto global window
 * e evitar dependências circulares com outros módulos.
 */

// Importar apenas o eventBus para evitar dependências circulares
import { eventBus } from '../events/eventBus';

// Definir os tipos para o objeto window
declare global {
  interface Window {
    monitoringService: any;
    monitoringServiceInitialized: boolean;
  }
}

// Tipos de eventos que podem ser monitorados
export enum EventType {
  // Eventos de API
  API_REQUEST = 'api_request',
  API_RESPONSE_SUCCESS = 'api_response_success',
  API_RESPONSE_ERROR = 'api_response_error',

  // Eventos de autenticação
  AUTH_LOGIN = 'auth_login',
  AUTH_LOGIN_ERROR = 'auth_login_error',
  AUTH_LOGOUT = 'auth_logout',
  AUTH_LOGOUT_ERROR = 'auth_logout_error',
  AUTH_REGISTER = 'auth_register',
  AUTH_REGISTER_ERROR = 'auth_register_error',
  AUTH_REFRESH_TOKEN = 'auth_refresh_token',
  AUTH_REFRESH_TOKEN_ERROR = 'auth_refresh_token_error',
  AUTH_PASSWORD_RESET = 'auth_password_reset',
  AUTH_PASSWORD_RESET_ERROR = 'auth_password_reset_error',
  AUTH_PASSWORD_RESET_REQUEST = 'auth_password_reset_request',
  AUTH_PASSWORD_CHANGE = 'auth_password_change',
  AUTH_PASSWORD_CHANGE_ERROR = 'auth_password_change_error',

  // Eventos de navegação
  NAVIGATION = 'navigation',

  // Eventos de erro
  ERROR = 'error',

  // Eventos de performance
  PERFORMANCE = 'performance'
}

// Interface para dados de eventos de API
export interface ApiEventData {
  url?: string;
  method?: string;
  status?: number;
  duration?: number;
  requestId?: string;
  dataSize?: number;
  error?: string;
}

// Interface para dados de eventos de autenticação
export interface AuthEventData {
  userId?: string;
  email?: string;
  action?: string;
  success?: boolean;
  error?: string;
}

// Interface para dados de eventos de navegação
export interface NavigationEventData {
  from?: string;
  to?: string;
  duration?: number;
}

// Interface para dados de eventos de erro
export interface ErrorEventData {
  message?: string;
  stack?: string;
  componentName?: string;
  source?: string;
  queryKey?: string;
  context?: any;
}

// Interface para dados de eventos de performance
export interface PerformanceEventData {
  metric?: string;
  value?: number;
  context?: any;
}

// Tipo união para todos os tipos de dados de eventos
export type EventData =
  | ApiEventData
  | AuthEventData
  | NavigationEventData
  | ErrorEventData
  | PerformanceEventData
  | Record<string, any>;

class MonitoringService {
  private isEnabled: boolean = true;
  private isDevelopment: boolean = import.meta.env.DEV;

  constructor() {
    // Inicializar listeners globais
    this.setupGlobalListeners();

    if (this.isDevelopment) {
      // console.log('[MonitoringService] Inicializado em modo de desenvolvimento');
    }
  }

  /**
   * Retorna todos os tipos de eventos disponíveis para monitoramento
   * Usado para registrar listeners para todos os eventos
   */
  getEventTypes(): string[] {
    return Object.values(EventType);
  }

  /**
   * Configura listeners globais para eventos não capturados
   */
  private setupGlobalListeners(): void {
    if (typeof window !== 'undefined') {
      // Capturar erros não tratados
      window.addEventListener('error', (event) => {
        this.recordEvent(EventType.ERROR, {
          message: event.error?.message || 'Erro desconhecido',
          stack: event.error?.stack,
          context: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        });
      });

      // Capturar rejeições de promessas não tratadas
      window.addEventListener('unhandledrejection', (event) => {
        this.recordEvent(EventType.ERROR, {
          message: event.reason?.message || 'Promessa rejeitada não tratada',
          stack: event.reason?.stack,
          context: { reason: event.reason }
        });
      });
    }
  }

  /**
   * Registra um evento no sistema de monitoramento
   * @param eventType Tipo do evento
   * @param data Dados associados ao evento
   */
  recordEvent(eventType: EventType | string, data?: EventData): void {
    if (!this.isEnabled) return;

    // Adicionar timestamp ao evento
    const eventData = {
      ...data,
      timestamp: new Date().toISOString()
    };

    // Emitir evento no EventBus
    eventBus.emit(`monitoring:${eventType}`, eventData);

    // Log em desenvolvimento
    if (this.isDevelopment) {
      // console.log(`[Monitoring] ${eventType}:`, eventData);
    }

    // Aqui poderia enviar para um serviço de analytics em produção
    // como Google Analytics, Sentry, etc.
    if (!this.isDevelopment) {
      // Implementação futura para envio a serviços externos
      this.sendToAnalyticsService(eventType, eventData);
    }
  }

  // Função recordError já está implementada mais abaixo

  /**
   * Método para enviar dados para serviços externos de analytics
   * @param eventType Tipo do evento
   * @param data Dados do evento
   */
  private sendToAnalyticsService(eventType: string, data: any): void {
    // Implementação futura
    // Por exemplo, enviar para Google Analytics, Sentry, etc.
  }

  /**
   * Ativa ou desativa o serviço de monitoramento
   * @param enabled Estado de ativação
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (this.isDevelopment) {
      console.log(`[MonitoringService] ${enabled ? 'Ativado' : 'Desativado'}`);
    }
  }

  /**
   * Registra um evento de requisição de API
   * @param data Dados da requisição
   */
  recordApiRequest(data: ApiEventData): void {
    this.recordEvent(EventType.API_REQUEST, data);
  }

  /**
   * Registra um evento de resposta de API bem-sucedida
   * @param data Dados da resposta
   */
  recordApiResponseSuccess(data: ApiEventData): void {
    this.recordEvent(EventType.API_RESPONSE_SUCCESS, data);
  }

  /**
   * Registra um evento de erro na resposta de API
   * @param data Dados do erro
   */
  recordApiResponseError(data: ApiEventData): void {
    this.recordEvent(EventType.API_RESPONSE_ERROR, data);
  }

  /**
   * Registra um evento de autenticação
   * @param eventType Tipo específico do evento de autenticação
   * @param data Dados do evento
   */
  recordAuthEvent(eventType: EventType | string, data: AuthEventData): void {
    this.recordEvent(eventType, data);
  }

  /**
   * Registra um evento de erro
   * @param data Dados do erro
   */
  recordError(data: ErrorEventData): void {
    this.recordEvent(EventType.ERROR, data);
  }

  /**
   * Registra uma métrica de performance
   * @param metric Nome da métrica
   * @param value Valor da métrica
   * @param context Contexto adicional
   */
  recordPerformance(metric: string, value: number, context?: any): void {
    this.recordEvent(EventType.PERFORMANCE, {
      metric,
      value,
      context
    });
  }

  /**
   * Inscreve-se para receber notificações de um tipo específico de evento
   * @param eventType Tipo do evento
   * @param callback Função a ser chamada quando o evento ocorrer
   * @returns Objeto com método para cancelar a inscrição
   */
  subscribe(eventType: EventType | string, callback: (data: any) => void) {
    return eventBus.subscribe(`monitoring:${eventType}`, callback);
  }
}

// Definir os tipos para o objeto window
// Primeiro remover a declaração existente se houver
declare global {
  interface Window {
    monitoringService: any; // Usa any para evitar problemas de tipo em diferentes declarações
    monitoringServiceInitialized: boolean;
  }
}

// Exporta uma instância singleton do MonitoringService
export const monitoringService = new MonitoringService();

// Removido o auto-registro para evitar dupla inicialização
// O registro será feito exclusivamente pelo monitoringInitializer.ts
