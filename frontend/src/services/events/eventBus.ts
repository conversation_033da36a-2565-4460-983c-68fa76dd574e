/**
 * Sistema de eventos para comunicação entre componentes
 * Implementa o padrão Observer para evitar dependências circulares
 * Fornece recursos de depuração e tratamento de erros
 */

type EventCallback = (data?: any) => void;

interface EventSubscription {
  unsubscribe: () => void;
}

interface EventOptions {
  /** Se verdadeiro, registra informações de depuração no console em modo de desenvolvimento */
  debug?: boolean;
}

class EventBus {
  private events: Map<string, EventCallback[]> = new Map();
  private isDevelopment: boolean = import.meta.env.DEV;
  private debugEnabled: boolean = false;
  
  constructor() {
    if (this.isDevelopment) {
      // Desabilitar depuração em modo de desenvolvimento
      this.debugEnabled = false;
    }
  }

  /**
   * Inscreve uma função para ser chamada quando um evento for emitido
   * @param eventName Nome do evento
   * @param callback Função a ser chamada quando o evento for emitido
   * @returns Objeto com método para cancelar a inscrição
   */
  subscribe(eventName: string, callback: EventCallback): EventSubscription {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, []);
    }

    const callbacks = this.events.get(eventName)!;
    callbacks.push(callback);

    return {
      unsubscribe: () => {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Emite um evento para todos os inscritos
   * @param eventName Nome do evento
   * @param data Dados opcionais a serem passados para os callbacks
   */
  emit(eventName: string, data?: any, options?: EventOptions): void {
    const debug = options?.debug ?? this.debugEnabled;
    
    if (debug && this.isDevelopment) {
      console.log(`[EventBus] Emitindo evento: ${eventName}`, data);
    }
    
    if (!this.events.has(eventName)) {
      if (debug && this.isDevelopment) {
        console.warn(`[EventBus] Nenhum assinante para o evento: ${eventName}`);
      }
      return;
    }

    const callbacks = this.events.get(eventName)!;
    const callbackCount = callbacks.length;
    
    if (debug && this.isDevelopment) {
      console.log(`[EventBus] Notificando ${callbackCount} assinante(s) para o evento: ${eventName}`);
    }
    
    callbacks.forEach((callback, index) => {
      try {
        callback(data);
        
        if (debug && this.isDevelopment) {
          console.log(`[EventBus] Callback #${index + 1}/${callbackCount} executado com sucesso`);
        }
      } catch (error) {
        console.error(`[EventBus] Erro ao processar evento ${eventName} no callback #${index + 1}:`, error);
        
        // Registrar erro no console com mais detalhes
        if (error instanceof Error) {
          console.error(`Detalhes do erro: ${error.message}\nStack: ${error.stack}`);
        }
      }
    });
  }

  /**
   * Remove todas as inscrições para um evento específico
   * @param eventName Nome do evento
   */
  clearEvent(eventName: string): void {
    this.events.delete(eventName);
  }

  /**
   * Remove todas as inscrições
   */
  clearAll(): void {
    // Operação silenciosa - logs removidos por segurança
    this.events.clear();
  }

  /**
   * Habilita ou desabilita o modo de depuração
   * @param enabled Se verdadeiro, habilita o modo de depuração
   */
  setDebug(enabled: boolean): void {
    this.debugEnabled = enabled;
    // Log removido por segurança
  }
}

// Exporta uma instância singleton do EventBus
export const eventBus = new EventBus();
