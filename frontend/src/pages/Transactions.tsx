import { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { useLocation, useNavigate } from "react-router-dom";
import TransactionActions from "@/components/transactions/TransactionActions";
import TransactionFiltersRow from "@/components/transactions/TransactionFiltersRow";
import TransactionsSummary from "@/components/transactions/TransactionsSummary";
import TransactionTabs from "@/components/transactions/TransactionTabs";
import { Transaction } from "@/types/transaction";
import { bankAccountService } from "@/services/api/bankAccountService";
import { toast } from "react-toastify";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

export default function Transactions() {
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [transactionType, setTransactionType] = useState<"accounts_receivable" | "accounts_payable" | "transfer">("accounts_receivable");
  const [selectedPeriod, setSelectedPeriod] = useState("current-month");
  const [selectedAccountId, setSelectedAccountId] = useState("all");
  const [customDateRange, setCustomDateRange] = useState<{
    startDate?: Date;
    endDate?: Date;
  }>({});
  const [accounts, setAccounts] = useState<Array<{ id: string; name: string }>>([]);
  const [loadingAccounts, setLoadingAccounts] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  // Carregar contas bancárias da API
  useEffect(() => {
    const fetchBankAccounts = async () => {
      try {
        setLoadingAccounts(true);

        // Obter o companyId do localStorage
        const companyId = localStorage.getItem('activeCompanyId');

        const response = await bankAccountService.getBankAccounts(1, 100, undefined, undefined, undefined, companyId || undefined);

        // Verificar se response.items existe e é um array antes de chamar map
        const formattedAccounts = Array.isArray(response.items)
          ? response.items.map(account => ({
              id: account.id,
              name: account.name
            }))
          : [];

        setAccounts(formattedAccounts);
      } catch (error) {
        console.error('Erro ao carregar contas bancárias:', error);
        toast.error('Não foi possível carregar as contas bancárias');
        // Usar dados padrão em caso de erro
        setAccounts([
          { id: "default1", name: "Conta Principal" },
          { id: "default2", name: "Conta Secundária" }
        ]);
      } finally {
        setLoadingAccounts(false);
      }
    };

    fetchBankAccounts();
  }, []);

  // Converter o período selecionado para o formato esperado pelo TransactionTabs
  const getPeriodMapping = (): "all" | "month" | "year" | "custom" => {
    switch (selectedPeriod) {
      case "current-month":
        return "month";
      case "current-year":
        return "year";
      case "custom":
        return "custom";
      default:
        return "all";
    }
  };

  const handleOpenTransaction = (transaction?: Transaction, mode: "view" | "edit" = "edit") => {
    if (transaction) {
      navigate(`/transactions/${transaction.id}/${mode}`);
    }
  };

  const handleNewTransaction = () => {
    navigate(`/transactions/accounts_receivable/new`);
  };

  return (
    <Layout location={location}>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Transações</h1>
          <TransactionActions onNewTransaction={handleNewTransaction} />
        </div>

        {loadingAccounts ? (
          <div className="flex justify-center items-center h-20">
            <LoadingSpinner />
          </div>
        ) : (
          <TransactionFiltersRow
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            selectedAccountId={selectedAccountId}
            setSelectedAccountId={setSelectedAccountId}
            customDateRange={customDateRange}
            setCustomDateRange={setCustomDateRange}
            accounts={accounts}
          />
        )}

        <TransactionsSummary
          selectedPeriod={selectedPeriod}
          selectedAccountId={selectedAccountId}
          customDateRange={customDateRange}
        />

        <TransactionTabs
          onEdit={handleOpenTransaction}
          period={getPeriodMapping()}
          accountId={selectedAccountId !== "all" ? selectedAccountId : undefined}
          dateRange={customDateRange}
        />
      </div>
    </Layout>
  );
}
