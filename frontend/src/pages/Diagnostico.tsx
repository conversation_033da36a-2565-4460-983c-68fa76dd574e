import { ApiConnectionTest } from '@/components/ApiConnectionTest';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import MonitoringDebugger from '@/components/MonitoringDebugger';

export default function DiagnosticoPage() {
  const [lastError, setLastError] = useState<string | null>(null);

  useEffect(() => {
    document.title = 'FluxoMax - Diagnóstico do Sistema';
  }, []);

  // Função para testar um erro tratado
  const testCaughtError = () => {
    try {
      // Gerar um erro propositalmente
      throw new Error('Erro de teste tratado');
    } catch (error) {
      if (error instanceof Error) {
        // Registrar o erro no sistema de monitoramento
        // @ts-ignore - Acessando monitoringService global
        if (window.monitoringService) {
          window.monitoringService.recordError({
            message: error.message,
            source: 'DiagnosticoPage',
            stack: error.stack
          });
        }
        setLastError(error.message);
      }
    }
  };

  // Função para testar um erro de promessa não tratada
  const testUnhandledPromise = () => {
    // Esta promessa será rejeitada e não tratada
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Erro de promessa não tratada'));
      }, 100);
    });
    setLastError('Promessa rejeitada enviada. Verifique o console.');
  };

  // Função para limpar os logs do console
  const clearConsoleLogs = () => {
    // Limpar apenas o estado local - não manipular console em produção
    setLastError(null);
  };
  
  return (
    <Layout>
      <div className="container py-8 max-w-5xl">
        <h1 className="text-3xl font-bold mb-2">Diagnóstico do Sistema</h1>
        <p className="text-muted-foreground mb-6">
          Ferramentas para testar e verificar o funcionamento do sistema
        </p>

        <Tabs defaultValue="connection">
          <TabsList className="mb-4">
            <TabsTrigger value="connection">Conexão com API</TabsTrigger>
            <TabsTrigger value="monitoring">Monitoramento</TabsTrigger>
            <TabsTrigger value="environment">Variáveis de Ambiente</TabsTrigger>
          </TabsList>

          <TabsContent value="connection" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Teste de Conexão com o Backend</CardTitle>
                <CardDescription>
                  Verifica se o frontend consegue se comunicar corretamente com o backend
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ApiConnectionTest />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitoring" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sistema de Monitoramento</CardTitle>
                <CardDescription>
                  Visualize e teste o sistema de monitoramento baseado em eventos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MonitoringDebugger />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Teste de Tratamento de Erros</CardTitle>
                <CardDescription>
                  Teste como o sistema captura e registra diferentes tipos de erros
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" onClick={testCaughtError}>
                      Testar Erro Tratado
                    </Button>
                    <Button variant="outline" onClick={testUnhandledPromise}>
                      Testar Promessa Não Tratada
                    </Button>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Último Erro:</h3>
                    {lastError ? (
                      <div className="p-4 bg-destructive/10 text-destructive rounded-md">
                        {lastError}
                      </div>
                    ) : (
                      <div className="p-4 bg-muted rounded-md text-muted-foreground">
                        Nenhum erro registrado. Use os botões acima para testar.
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-end">
                    <Button variant="ghost" onClick={clearConsoleLogs}>
                      Limpar Console
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="environment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Variáveis de Ambiente</CardTitle>
                <CardDescription>
                  Configurações atuais e variáveis de ambiente disponíveis para o frontend
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">API URL</h3>
                    <p className="text-sm font-mono bg-muted p-2 rounded-md">
                      {import.meta.env.VITE_API_URL || 'Não definida'}
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Ambiente</h3>
                    <p className="text-sm font-mono bg-muted p-2 rounded-md">
                      {import.meta.env.MODE || 'development'}
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Versão</h3>
                    <p className="text-sm font-mono bg-muted p-2 rounded-md">
                      {import.meta.env.VITE_APP_VERSION || '1.0.0'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
