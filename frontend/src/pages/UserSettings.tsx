
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Building2, Lock, Settings as SettingsIcon, Users, Pencil, Trash, UserPlus, Key, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { UserModal } from "@/components/settings/UserModal";
import { PasswordChangeModal } from "@/components/settings/PasswordChangeModal";
import { toast } from "sonner";

interface User {
  id: string;
  name: string;
  email: string;
  isAdmin: boolean;
  active: boolean;
}

const UserSettings = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | undefined>(undefined);
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: 'Administrador',
      email: '<EMAIL>',
      isAdmin: true,
      active: true,
    }
  ]);
  
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleChangePassword = (user: User) => {
    setSelectedUser(user);
    setIsPasswordModalOpen(true);
  };

  const handleSaveUser = (userData: any) => {
    if (selectedUser) {
      // Edit existing user
      setUsers(users.map(user => 
        user.id === selectedUser.id ? { ...user, ...userData, id: user.id } : user
      ));
      toast.success("Usuário atualizado com sucesso");
    } else {
      // Add new user
      setUsers([...users, {
        ...userData,
        id: Math.random().toString(36).substring(7),
      }]);
      toast.success("Usuário criado com sucesso");
    }
    setIsModalOpen(false);
    setSelectedUser(undefined);
  };

  const handlePasswordChange = (password: string) => {
    if (selectedUser) {
      // In a real application, you would call an API to update the password
      console.log(`Password changed for user ${selectedUser.name} to: ${password}`);
      toast.success("Senha alterada com sucesso");
      setIsPasswordModalOpen(false);
      setSelectedUser(undefined);
    }
  };
  
  return (
    <Layout>
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-8">
          <SettingsIcon className="h-8 w-8 text-primary mr-3" />
          <h1 className="text-3xl font-bold">Configurações</h1>
        </div>
        
        <div className="flex border-b mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/companies")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300"
          >
            <Building2 className="h-5 w-5" />
            <span>Empresas</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/users")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-primary text-primary font-medium"
          >
            <Users className="h-5 w-5" />
            <span>Usuários</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/security")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300"
          >
            <Lock className="h-5 w-5" />
            <span>Segurança</span>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => navigate("/settings/general")}
            className="flex items-center gap-2 pb-2 pt-2 px-4 border-b-2 border-transparent hover:border-gray-300"
          >
            <SettingsIcon className="h-5 w-5" />
            <span>Geral</span>
          </Button>
        </div>
        
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-semibold flex items-center gap-2">
              <Users className="h-6 w-6" />
              Gerenciamento de Usuários
            </h2>
          </div>
          <Button
            onClick={() => {
              setSelectedUser(undefined);
              setIsModalOpen(true);
            }}
            className="flex items-center gap-2"
          >
            <UserPlus className="h-4 w-4" />
            Novo Usuário
          </Button>
        </div>
        
        <div className="bg-white rounded-lg border shadow dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6">
            <h3 className="text-xl font-semibold mb-2">Lista de Usuários</h3>
            <p className="text-gray-500 mb-6 dark:text-gray-400">Gerencie os usuários do sistema. Você pode editar, ativar/desativar ou redefinir senhas.</p>
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Usuário</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Administrador</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      {user.isAdmin ? (
                        <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-100 dark:bg-blue-900 dark:text-blue-300">
                          <Shield className="mr-1 h-3 w-3" />
                          Admin
                        </Badge>
                      ) : (
                        <span className="text-gray-500 dark:text-gray-400">Usuário</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={user.active 
                        ? "bg-green-100 text-green-700 hover:bg-green-100 dark:bg-green-900 dark:text-green-300"
                        : "bg-red-100 text-red-700 hover:bg-red-100 dark:bg-red-900 dark:text-red-300"
                      }>
                        {user.active ? "Ativo" : "Inativo"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          title="Editar usuário"
                          onClick={() => handleEditUser(user)}
                        >
                          <Pencil className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          title="Alterar senha"
                          onClick={() => handleChangePassword(user)}
                        >
                          <Key className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                        </Button>
                        <Button variant="ghost" size="icon" className="text-red-500">
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
      
      <UserModal 
        open={isModalOpen} 
        onOpenChange={setIsModalOpen}
        onSave={handleSaveUser}
        user={selectedUser}
      />

      <PasswordChangeModal
        open={isPasswordModalOpen}
        onOpenChange={setIsPasswordModalOpen}
        onSave={handlePasswordChange}
        user={selectedUser}
      />
    </Layout>
  );
};

export default UserSettings;
