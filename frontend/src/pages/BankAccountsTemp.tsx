import { useState, useEffect } from 'react';
import { Layout } from '@/components/layout/Layout';
import { useAuth } from '@/contexts/AuthContext';
import { useBankAccounts } from '@/hooks/api/useBankAccounts';

const BankAccountsTemp = () => {
  const { activeCompanyId } = useAuth();
  const [page, setPage] = useState(1);
  const limit = 10;

  // Fetch bank accounts from API
  const { data: bankAccountsData, isLoading, isError, refetch } = useBankAccounts(page, limit, {
    companyId: activeCompanyId
  });

  // Logs removidos por segurança

  // Recarregar dados quando o componente for montado
  useEffect(() => {
    console.log('BankAccountsTemp - useEffect executado');
    refetch();
  }, [refetch]);

  return (
    <Layout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Contas Bancárias (Temporário)</h1>

        {isLoading ? (
          <p>Carregando...</p>
        ) : isError ? (
          <p>Erro ao carregar contas bancárias</p>
        ) : bankAccountsData?.items.length === 0 ? (
          <p>Nenhuma conta bancária encontrada</p>
        ) : (
          <div>
            <p>Total de contas: {bankAccountsData?.total}</p>
            <ul>
              {bankAccountsData?.items.map(account => (
                <li key={account.id}>
                  {account.name} - {account.accountNumber}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default BankAccountsTemp;
