import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft, CalendarIcon, FileText, RefreshCcw } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { parseCurrencyTo<PERSON><PERSON>ber, numberToFormattedString } from "@/utils/currencyUtils";
import TransactionAmountField from "@/components/transactions/TransactionAmountField";
import { usePaymentMethodOptions, useRecurrenceTypeOptions } from "@/hooks/api";

// Mock data - this would be replaced by actual data fetching
const MOCK_ACCOUNTS = [
  {
    id: "1",
    description: "Office Supplies",
    entity: "Supplier XYZ",
    dueDate: new Date(),
    amount: 750,
    paidAmount: 0,
    status: "pending",
    category: "Supplies",
    project: "Project Y",
    bankAccount: "main",
    notes: "Monthly office supplies",
    recurrence: "monthly",
    paymentMethod: "bank_transfer",
    invoiceNumber: "INV-001",
    interestAmount: 0,
    discountAmount: 0,
  },
  // More mock data would be here
];

// Mock options for selects
const statusOptions = [
  { label: "Pending", value: "pending" },
  { label: "Partial", value: "partial" },
  { label: "Paid", value: "paid" },
];

const categoryOptions = [
  { label: "Expense Category 0", value: "Expense Category 0" },
  { label: "Expense Category 1", value: "Expense Category 1" },
  { label: "Expense Category 2", value: "Expense Category 2" },
  { label: "Expense Category 3", value: "Expense Category 3" },
  { label: "Expense Category 4", value: "Expense Category 4" },
];

const projectOptions = [
  { label: "Project 0", value: "Project 0" },
  { label: "Project 2", value: "Project 2" },
  { label: "Project 4", value: "Project 4" },
  { label: "Project 6", value: "Project 6" },
  { label: "Project 8", value: "Project 8" },
];

const supplierOptions = [
  { label: "Supplier 1", value: "Supplier 1" },
  { label: "Supplier 2", value: "Supplier 2" },
  { label: "Supplier 3", value: "Supplier 3" },
  { label: "Supplier 4", value: "Supplier 4" },
  { label: "Supplier 5", value: "Supplier 5" },
];

const bankAccountOptions = [
  { label: "Main Account", value: "main" },
  { label: "Secondary Account", value: "secondary" },
];


// Define field names for refreshing
type RefreshableField = 
  | "entity" 
  | "category" 
  | "project" 
  | "bankAccount" 
  | "paymentMethod" 
  | "recurrence";

export default function AccountsPayableDetail() {
  const { id, mode } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Buscar métodos de pagamento e tipos de recorrência da API
  const { data: paymentMethodOptions, isLoading: loadingPaymentMethods } = usePaymentMethodOptions();
  const { data: recurrenceOptions, isLoading: loadingRecurrenceTypes } = useRecurrenceTypeOptions();
  
  const [formData, setFormData] = useState({
    description: "",
    entity: "",
    dueDate: new Date(),
    amount: "",
    paidAmount: "",
    status: "pending",
    category: "",
    project: "",
    bankAccount: "",
    notes: "",
    interestAmount: "",
    discountAmount: "",
    markAsPaid: false,
    installments: false,
    numberOfInstallments: "1",
    installmentFrequency: "monthly",
    firstInstallmentDate: new Date(),
    recurrence: "none",
    paymentMethod: "",
    invoiceNumber: "",
  });

  const isReadOnly = mode === "view";
  const isEditing = mode === "edit";
  const isNew = mode === "new";

  // Track which fields are currently being refreshed
  const [refreshingFields, setRefreshingFields] = useState<Record<RefreshableField, boolean>>({
    entity: false,
    category: false,
    project: false,
    bankAccount: false,
    paymentMethod: false,
    recurrence: false
  });
  
  const [openCalendar, setOpenCalendar] = useState(false);
  const [openInstallmentCalendar, setOpenInstallmentCalendar] = useState(false);

  useEffect(() => {
    // In a real application, you would fetch the account data from your API
    if (id && id !== "new") {
      const foundAccount = MOCK_ACCOUNTS.find(acc => acc.id === id);
      if (foundAccount) {
        setFormData({
          description: foundAccount.description || "",
          entity: foundAccount.entity || "",
          dueDate: foundAccount.dueDate || new Date(),
          amount: foundAccount.amount ? numberToFormattedString(foundAccount.amount) : "0,00",
          paidAmount: foundAccount.paidAmount ? numberToFormattedString(foundAccount.paidAmount) : "0,00",
          status: foundAccount.status || "pending",
          category: foundAccount.category || "",
          project: foundAccount.project || "",
          bankAccount: foundAccount.bankAccount || "",
          notes: foundAccount.notes || "",
          interestAmount: foundAccount.interestAmount ? numberToFormattedString(foundAccount.interestAmount) : "0,00",
          discountAmount: foundAccount.discountAmount ? numberToFormattedString(foundAccount.discountAmount) : "0,00",
          markAsPaid: foundAccount.status === "paid" || false,
          installments: false,
          numberOfInstallments: "1",
          installmentFrequency: "monthly",
          firstInstallmentDate: new Date(),
          recurrence: foundAccount.recurrence || "none",
          paymentMethod: foundAccount.paymentMethod || "",
          invoiceNumber: foundAccount.invoiceNumber || "",
        });
      }
    } else {
      // Inicializar com valores padrão formatados corretamente
      setFormData(prev => ({
        ...prev,
        amount: "0,00",
        paidAmount: "0,00",
        interestAmount: "0,00",
        discountAmount: "0,00"
      }));
    }
  }, [id]);

  const handleBack = () => {
    navigate("/accounts-payable");
  };

  const handleChange = (field: string, value: any) => {
    if (isReadOnly) return;
    
    setFormData({
      ...formData,
      [field]: value,
    });
    
    if (field === "markAsPaid") {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        status: value ? "paid" : "pending",
        paidAmount: value ? prev.amount : "0,00",
      }));
    }
  };

  const handleSubmit = () => {
    console.log("Submitting form data:", formData);
    toast({
      title: isEditing ? "Conta atualizada" : "Conta cadastrada",
      description: `A conta foi ${isEditing ? "atualizada" : "cadastrada"} com sucesso.`,
    });
    navigate("/accounts-payable");
  };

  const finalAmount = () => {
    const amount = parseCurrencyToNumber(formData.amount) || 0;
    const interest = parseCurrencyToNumber(formData.interestAmount) || 0;
    const discount = parseCurrencyToNumber(formData.discountAmount) || 0;
    return numberToFormattedString(amount + interest - discount);
  };

  // Function to refresh specific field options
  const refreshFieldOptions = (field: RefreshableField) => {
    // Set only the specific field to refreshing state
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));
    
    // Simulate API call delay
    setTimeout(() => {
      // Reset only the specific field refreshing state
      setRefreshingFields(prev => ({
        ...prev,
        [field]: false
      }));
      
      // Show success toast with the specific field name
      const fieldDisplayNames: Record<RefreshableField, string> = {
        entity: "fornecedores",
        category: "categorias",
        project: "projetos",
        bankAccount: "contas bancárias",
        paymentMethod: "métodos de pagamento",
        recurrence: "tipos de recorrência"
      };
      
      toast({
        title: "Opções atualizadas",
        description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
      });
    }, 800);
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleBack} 
            className="mr-4"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">
            {mode === "new" 
              ? "Nova Conta a Pagar" 
              : mode === "edit" 
                ? "Editar Conta a Pagar" 
                : "Detalhes da Conta a Pagar"}
          </h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              {isReadOnly 
                ? "Informações da Conta" 
                : isEditing 
                  ? "Editar Informações" 
                  : "Informações da Nova Conta"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="description">Descrição</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleChange("description", e.target.value)}
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Fornecedor</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("entity")}
                        disabled={refreshingFields.entity}
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.entity ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={formData.entity}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.entity}
                      onValueChange={(value) => handleChange("entity", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione o fornecedor..." />
                      </SelectTrigger>
                      <SelectContent>
                        {supplierOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Data de Vencimento</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={formData.dueDate ? format(formData.dueDate, 'PPP') : ''}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Popover open={openCalendar} onOpenChange={setOpenCalendar}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal mt-1"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.dueDate ? format(formData.dueDate, 'PPP') : <span>Selecione uma data</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.dueDate}
                          onSelect={(date) => {
                            handleChange("dueDate", date || new Date());
                            setOpenCalendar(false);
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label htmlFor="amount">Valor</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <TransactionAmountField
                    id="amount"
                    label=""
                    value={formData.amount}
                    onChange={(value) => handleChange("amount", value)}
                    disabled={isReadOnly}
                    isMainAmount={true}
                  />
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label htmlFor="paidAmount">Valor Pago</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <TransactionAmountField
                    id="paidAmount"
                    label=""
                    value={formData.paidAmount}
                    onChange={(value) => handleChange("paidAmount", value)}
                    disabled={isReadOnly || !formData.markAsPaid}
                  />
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Status</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={statusOptions.find(s => s.value === formData.status)?.label || formData.status}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.status}
                      onValueChange={(value) => handleChange("status", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione o status..." />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Categoria</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("category")}
                        disabled={refreshingFields.category}
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.category ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={formData.category}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.category}
                      onValueChange={(value) => handleChange("category", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione a categoria..." />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Método de Pagamento</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("paymentMethod")}
                        disabled={refreshingFields.paymentMethod}
                        title="Atualizar opções"
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.paymentMethod ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={paymentMethodOptions.find(p => p.value === formData.paymentMethod)?.label || formData.paymentMethod}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.paymentMethod}
                      onValueChange={(value) => handleChange("paymentMethod", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione o método..." />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethodOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label htmlFor="invoiceNumber" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Fatura
                    </Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="invoiceNumber"
                    value={formData.invoiceNumber}
                    onChange={(e) => handleChange("invoiceNumber", e.target.value)}
                    placeholder="Número da fatura"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>

                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Projeto (Opcional)</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("project")}
                        disabled={refreshingFields.project}
                        title="Atualizar opções"
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.project ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={formData.project}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.project}
                      onValueChange={(value) => handleChange("project", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione o projeto..." />
                      </SelectTrigger>
                      <SelectContent>
                        {projectOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Conta Bancária</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("bankAccount")}
                        disabled={refreshingFields.bankAccount}
                        title="Atualizar opções"
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.bankAccount ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={bankAccountOptions.find(b => b.value === formData.bankAccount)?.label || formData.bankAccount}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.bankAccount}
                      onValueChange={(value) => handleChange("bankAccount", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione a conta..." />
                      </SelectTrigger>
                      <SelectContent>
                        {bankAccountOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                <div className="col-span-1">
                  <div className="flex items-center justify-between h-5">
                    <Label>Recorrência</Label>
                    {!isReadOnly && (
                      <Button 
                        type="button"
                        variant="ghost" 
                        size="icon" 
                        className="h-5 w-5" 
                        onClick={() => refreshFieldOptions("recurrence")}
                        disabled={refreshingFields.recurrence}
                        title="Atualizar opções"
                      >
                        <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.recurrence ? 'animate-spin' : ''}`} />
                      </Button>
                    )}
                    {isReadOnly && <div className="w-5 h-5"></div>}
                  </div>
                  {isReadOnly ? (
                    <Input
                      value={recurrenceOptions.find(r => r.value === formData.recurrence)?.label || formData.recurrence}
                      className="mt-1"
                      readOnly
                    />
                  ) : (
                    <Select 
                      value={formData.recurrence}
                      onValueChange={(value) => handleChange("recurrence", value)}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Selecione a recorrência..." />
                      </SelectTrigger>
                      <SelectContent>
                        {recurrenceOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                
                {!isReadOnly && (
                  <div className="col-span-2 flex items-center space-x-2 mt-2">
                    <Checkbox 
                      id="markAsPaid" 
                      checked={formData.markAsPaid}
                      onCheckedChange={(checked) => 
                        handleChange("markAsPaid", checked)
                      }
                      disabled={isReadOnly}
                    />
                    <Label htmlFor="markAsPaid" className="cursor-pointer">
                      Marcar como pago
                    </Label>
                  </div>
                )}
                
                <div className="grid md:grid-cols-2 gap-4 mt-6">
                  <div>
                    <TransactionAmountField
                      id="interestAmount"
                      label="Juros (R$)"
                      value={formData.interestAmount}
                      onChange={(value) => handleChange("interestAmount", value)}
                      disabled={isReadOnly}
                    />
                  </div>
                  <div>
                    <TransactionAmountField
                      id="discountAmount"
                      label="Desconto (R$)"
                      value={formData.discountAmount}
                      onChange={(value) => handleChange("discountAmount", value)}
                      disabled={isReadOnly}
                    />
                  </div>
                </div>
                
                <div className="mt-6">
                  <TransactionAmountField
                    id="finalAmount"
                    label="Valor Final (R$)"
                    value={finalAmount()}
                    onChange={() => {}} // Somente leitura
                    disabled={true}
                  />
                </div>
                
                {!isReadOnly && (
                  <div className="col-span-2 flex items-center space-x-2 mt-2">
                    <Checkbox 
                      id="installments" 
                      checked={formData.installments}
                      onCheckedChange={(checked) => 
                        handleChange("installments", !!checked)
                      }
                      disabled={isReadOnly}
                    />
                    <Label htmlFor="installments" className="cursor-pointer">
                      Dividir em parcelas
                    </Label>
                  </div>
                )}
                
                {formData.installments && !isReadOnly && (
                  <div className="col-span-2 border rounded-md p-4 mt-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="col-span-1">
                        <div className="flex items-center justify-between h-5">
                          <Label htmlFor="numberOfInstallments">Número de parcelas</Label>
                          <div className="w-5 h-5"></div>
                        </div>
                        <Input
                          id="numberOfInstallments"
                          type="number"
                          min="2"
                          value={formData.numberOfInstallments}
                          onChange={(e) => handleChange("numberOfInstallments", e.target.value)}
                          placeholder="1"
                          className="mt-1"
                          readOnly={isReadOnly}
                        />
                      </div>
                      
                      <div className="col-span-1">
                        <div className="flex items-center justify-between h-5">
                          <Label>Frequência</Label>
                          <div className="w-5 h-5"></div>
                        </div>
                        <Select 
                          value={formData.installmentFrequency}
                          onValueChange={(value) => handleChange("installmentFrequency", value)}
                          disabled={isReadOnly}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Selecione a frequência..." />
                          </SelectTrigger>
                          <SelectContent>
                            {recurrenceOptions.filter(option => option.value !== "none").map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="col-span-2">
                        <div className="flex items-center justify-between h-5">
                          <Label>Data da primeira parcela</Label>
                          <div className="w-5 h-5"></div>
                        </div>
                        <Popover open={openInstallmentCalendar} onOpenChange={setOpenInstallmentCalendar}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal mt-1"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {formData.firstInstallmentDate ? format(formData.firstInstallmentDate, 'PPP') : <span>Selecione uma data</span>}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={formData.firstInstallmentDate}
                              onSelect={(date) => {
                                handleChange("firstInstallmentDate", date || new Date());
                                setOpenInstallmentCalendar(false);
                              }}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className="col-span-2">
                  <div className="flex items-center justify-between h-5">
                    <Label htmlFor="notes">Observações</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleChange("notes", e.target.value)}
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleBack}>
                Cancelar
              </Button>
              
              {!isReadOnly && (
                <Button onClick={handleSubmit}>
                  {isEditing ? "Salvar Alterações" : "Criar"}
                </Button>
              )}
              
              {isReadOnly && (
                <Button 
                  onClick={() => navigate(`/accounts-payable/${id}/edit`)}
                >
                  Editar
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
