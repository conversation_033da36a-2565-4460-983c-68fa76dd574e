import { Layout } from "@/components/layout/Layout";
import { useLocation, useParams } from "react-router-dom";
import { TransactionDetailHeader } from "@/components/transactions/TransactionDetailHeader";
import { TransactionForm } from "@/components/transactions/TransactionForm";
import { useTransactionDetail } from "@/hooks/useTransactionDetail";

export default function TransactionDetail() {
  const { id, mode } = useParams();
  const location = useLocation();
  
  const {
    transaction,
    isLoading,
    isCreating,
    isEditing,
    isViewing,
    initialType,
    handleSubmit
  } = useTransactionDetail({ id, mode });

  const getPageTitle = () => {
    if (isCreating) {
      return "Nova Transação";
    }
    if (isEditing) return "Editar Transação";
    return "Visualizar Transação";
  };

  return (
    <Layout location={location}>
      <div className="container mx-auto py-6">
        <TransactionDetailHeader title={getPageTitle()} />

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
               <p className="text-gray-500">Carregando...</p>
            </div>
          ) : (
            <TransactionForm
              isCreating={isCreating}
              isEditing={isEditing}
              isViewing={isViewing}
              initialTransaction={transaction}
              initialType={initialType}
              onSubmit={handleSubmit}
            />
          )}
        </div>
      </div>
    </Layout>
  );
}
