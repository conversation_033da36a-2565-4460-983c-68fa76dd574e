import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DataExport } from '@/components/ui/data-export';
import { Filter, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface TransactionListHeaderProps {
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  getActiveFilterCount: () => number;
  getApiParams: () => Record<string, any>;
  activeTab: string;
  onCreateTransaction: () => void;
}

export const TransactionListHeader: React.FC<TransactionListHeaderProps> = ({
  showFilters,
  setShowFilters,
  getActiveFilterCount,
  getApiParams,
  activeTab,
  onCreateTransaction,
}) => {
  return (
    <div className="flex justify-between items-center">
      <h1 className="text-3xl font-bold">Transações</h1>
      
      <div className="flex space-x-2">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center"
        >
          <Filter className="mr-2 h-4 w-4" />
          Filtros
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="ml-2">
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
        
        <DataExport
          endpoint="/transactions/export"
          title="Exportar Transações"
          description="Selecione o formato e as opções para exportar as transações"
          filename="transacoes"
          params={{
            ...getApiParams(),
            type: activeTab === 'all' ? undefined : activeTab === 'income' ? 'INCOME' : 'EXPENSE',
          }}
          exportOptions={[
            {
              id: 'includeDetails',
              label: 'Incluir detalhes completos',
              description: 'Inclui informações detalhadas sobre cada transação',
              checked: true,
            },
            {
              id: 'includeMetadata',
              label: 'Incluir metadados',
              description: 'Inclui informações sobre criação e atualização',
              checked: false,
            },
          ]}
          buttonVariant="outline"
          buttonClassName="flex items-center"
        />
        
        <Button onClick={onCreateTransaction} className="flex items-center">
          <Plus className="mr-2 h-4 w-4" />
          Nova Transação
        </Button>
      </div>
    </div>
  );
}; 