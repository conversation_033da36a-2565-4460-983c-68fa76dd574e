import { ColumnDef } from '@/components/ui/data-table';
import { TransactionListItem } from '@/types/transaction';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const getTransactionColumns = (): ColumnDef<TransactionListItem>[] => [
  {
    accessorKey: 'date',
    header: 'Data',
    cell: (transaction) => format(new Date(transaction.date), 'dd/MM/yyyy', { locale: ptBR }),
    enableSorting: true,
  },
  {
    accessorKey: 'description',
    header: 'Descrição',
    cell: (transaction) => transaction.description,
    enableSorting: true,
  },
  {
    accessorKey: 'amount',
    header: 'Valor',
    cell: (transaction) => {
      const formattedAmount = new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(Math.abs(transaction.amount));
      
      return (
        <span className={transaction.type === 'INCOME' ? 'text-green-600' : 'text-red-600'}>
          {transaction.type === 'INCOME' ? '+' : '-'} {formattedAmount}
        </span>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'category',
    header: 'Categoria',
    cell: (transaction) => transaction.category,
    enableSorting: true,
  },
  {
    accessorKey: 'account.name',
    header: 'Conta',
    cell: (transaction) => transaction.account.name,
    enableSorting: true,
    sortKey: 'accountId',
  },
  {
    accessorKey: 'entity.name',
    header: 'Entidade',
    cell: (transaction) => transaction.entity?.name || '-',
    enableSorting: true,
    sortKey: 'entityId',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (transaction) => {
      let color = 'bg-gray-500';
      let label = 'Desconhecido';
      
      switch (transaction.status) {
        case 'PENDING':
          color = 'bg-yellow-500';
          label = 'Pendente';
          break;
        case 'COMPLETED':
          color = 'bg-green-500';
          label = 'Concluído';
          break;
        case 'CANCELLED':
          color = 'bg-red-500';
          label = 'Cancelado';
          break;
      }
      
      return (
        <Badge className={`${color} text-white`}>
          {label}
        </Badge>
      );
    },
    enableSorting: true,
  },
]; 