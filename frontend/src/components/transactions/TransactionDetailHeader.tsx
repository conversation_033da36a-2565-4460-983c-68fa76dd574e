
import React from "react";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface TransactionDetailHeaderProps {
  title: string;
}

export const TransactionDetailHeader = ({ title }: TransactionDetailHeaderProps) => {
  const navigate = useNavigate();
  
  return (
    <div className="flex items-center mb-6">
      <Button 
        variant="ghost" 
        onClick={() => navigate("/transactions")} 
        className="mr-4"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Voltar
      </Button>
      <h1 className="text-2xl font-bold">{title}</h1>
    </div>
  );
};
