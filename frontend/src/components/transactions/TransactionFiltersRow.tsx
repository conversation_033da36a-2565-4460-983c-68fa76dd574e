
import { Dispatch, SetStateAction } from 'react';
import EnhancedPeriodFilter from "@/components/dashboard/EnhancedPeriodFilter";
import AccountFilter from "@/components/dashboard/AccountFilter";

interface TransactionFiltersRowProps {
  selectedPeriod: string;
  setSelectedPeriod: Dispatch<SetStateAction<string>>;
  selectedAccountId: string;
  setSelectedAccountId: Dispatch<SetStateAction<string>>;
  customDateRange: {
    startDate?: Date;
    endDate?: Date;
  };
  setCustomDateRange: Dispatch<SetStateAction<{
    startDate?: Date;
    endDate?: Date;
  }>>;
  accounts: Array<{ id: string; name: string }>;
}

export default function TransactionFiltersRow({
  selectedPeriod,
  setSelectedPeriod,
  selectedAccountId,
  setSelectedAccountId,
  customDateRange,
  setCustomDateRange,
  accounts
}: TransactionFiltersRowProps) {
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    if (period !== "custom") {
      setCustomDateRange({});
    }
  };

  const handleDateRangeChange = (startDate?: Date, endDate?: Date) => {
    setSelectedPeriod("custom");
    setCustomDateRange({ startDate, endDate });
  };

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      <EnhancedPeriodFilter
        selectedPeriod={selectedPeriod}
        onPeriodChange={handlePeriodChange}
        startDate={customDateRange?.startDate}
        endDate={customDateRange?.endDate}
        onDateRangeChange={handleDateRangeChange}
      />
      
      <AccountFilter
        selectedAccountId={selectedAccountId}
        accounts={accounts}
        onAccountChange={setSelectedAccountId}
      />
    </div>
  );
}
