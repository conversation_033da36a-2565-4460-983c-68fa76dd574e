import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { UseFormReturn } from "react-hook-form";
import { TransactionFormValues } from "../types/TransactionModalTypes";
import TransactionAmountField from "../TransactionAmountField";

interface TransactionBasicInfoSectionProps {
  form: UseFormReturn<TransactionFormValues>;
  disabled?: boolean;
}

export default function TransactionBasicInfoSection({ form, disabled = false }: TransactionBasicInfoSectionProps) {
  return (
    <>
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Descrição</FormLabel>
            <FormControl>
              <Input placeholder="Digite a descrição da transação" {...field} disabled={disabled} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <FormLabel className="block text-left pl-1">Valor</FormLabel>
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem className="m-0 p-0">
                <FormControl>
                  <TransactionAmountField 
                    id="amount"
                    label=""
                    value={field.value}
                    onChange={field.onChange}
                    isMainAmount
                    disabled={disabled}
                    className="h-10"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-2">
          <FormLabel className="block text-left pl-1">Data</FormLabel>
          <FormField
            control={form.control}
            name="transactionDate"
            render={({ field }) => (
              <FormItem className="m-0 p-0">
                <FormControl>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal h-10",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={disabled}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Escolha uma data</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </>
  );
}
