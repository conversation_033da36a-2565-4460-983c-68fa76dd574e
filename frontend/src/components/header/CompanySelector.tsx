import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Building, ChevronDown, Loader2 } from 'lucide-react';

const CompanySelector: React.FC = () => {
  const { companies, activeCompanyId, switchCompany, loading, initialized } = useAuth();

  // Se ainda está carregando ou não foi inicializado, mostrar loading
  if (loading || !initialized) {
    return (
      <Button variant="outline" className="flex items-center gap-2" disabled>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="max-w-[150px] truncate">Carregando...</span>
      </Button>
    );
  }

  // Se não houver empresas ou empresa ativa, não renderizar o componente
  if (!companies || !companies.length || !activeCompanyId) {
    return null;
  }

  // Encontrar a empresa ativa
  const activeCompany = companies.find(company => company.id === activeCompanyId);

  if (!activeCompany) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Building className="h-4 w-4" />
          <span className="max-w-[150px] truncate">{activeCompany.name}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {companies.map(company => (
          <DropdownMenuItem
            key={company.id}
            onClick={() => switchCompany(company.id)}
            className={company.id === activeCompanyId ? 'bg-muted' : ''}
          >
            {company.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CompanySelector;
