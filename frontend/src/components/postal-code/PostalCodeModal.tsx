import { useState, useEffect } from "react";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { usePostalCodeService, PostalCode } from "@/hooks/usePostalCodeService";
import { Search, Save, RefreshCw, X } from "lucide-react";

// Brazilian states list
const brazilianStates = [
  { value: 'AC', label: 'Acre' },
  { value: 'AL', label: 'Alagoas' },
  { value: 'AP', label: 'Amapá' },
  { value: 'AM', label: 'Amazonas' },
  { value: 'BA', label: 'Bahia' },
  { value: 'CE', label: 'Cear<PERSON>' },
  { value: 'DF', label: 'Distrito Federal' },
  { value: 'ES', label: 'Espírito Santo' },
  { value: 'GO', label: 'Goiás' },
  { value: 'MA', label: 'Maranhão' },
  { value: 'MT', label: 'Mato Grosso' },
  { value: 'MS', label: 'Mato Grosso do Sul' },
  { value: 'MG', label: 'Minas Gerais' },
  { value: 'PA', label: 'Pará' },
  { value: 'PB', label: 'Paraíba' },
  { value: 'PR', label: 'Paraná' },
  { value: 'PE', label: 'Pernambuco' },
  { value: 'PI', label: 'Piauí' },
  { value: 'RJ', label: 'Rio de Janeiro' },
  { value: 'RN', label: 'Rio Grande do Norte' },
  { value: 'RS', label: 'Rio Grande do Sul' },
  { value: 'RO', label: 'Rondônia' },
  { value: 'RR', label: 'Roraima' },
  { value: 'SC', label: 'Santa Catarina' },
  { value: 'SP', label: 'São Paulo' },
  { value: 'SE', label: 'Sergipe' },
  { value: 'TO', label: 'Tocantins' }
];

interface PostalCodeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (postalCode: PostalCode) => void;
  postalCode?: Partial<PostalCode>;
  companyId?: string;
}

export function PostalCodeModal({
  open,
  onOpenChange,
  onSave,
  postalCode,
  companyId = ""
}: PostalCodeModalProps) {
  const postalCodeService = usePostalCodeService();
  const [isLoading, setIsLoading] = useState(false);
  const [isSearchingCEP, setIsSearchingCEP] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<Partial<PostalCode>>({
    id: postalCode?.id || '',
    company_id: companyId || postalCode?.company_id || '',
    zip_code: postalCode?.zip_code || '',
    street: postalCode?.street || '',
    neighborhood: postalCode?.neighborhood || '',
    city: postalCode?.city || '',
    state: postalCode?.state || '',
    registration_origin: postalCode?.registration_origin || 'manual',
  });

  // Reset form when postal code changes
  useEffect(() => {
    if (open) {
      setFormData({
        id: postalCode?.id || '',
        company_id: companyId || postalCode?.company_id || '',
        zip_code: postalCode?.zip_code || '',
        street: postalCode?.street || '',
        neighborhood: postalCode?.neighborhood || '',
        city: postalCode?.city || '',
        state: postalCode?.state || '',
        registration_origin: postalCode?.registration_origin || 'manual',
      });
      setErrors({});
    }
  }, [open, postalCode, companyId]);

  // Format CEP as it is typed
  const formatCep = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 5) {
      return digits;
    }
    return `${digits.substring(0, 5)}-${digits.substring(5, 8)}`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Apply special formatting for zip_code
    if (name === 'zip_code') {
      const formattedValue = formatCep(value);
      setFormData({ ...formData, [name]: formattedValue });
      
      // Validate CEP format
      if (formattedValue.length > 0 && !/^\d{5}-?\d{0,3}$/.test(formattedValue)) {
        setErrors({ ...errors, [name]: 'CEP inválido. Use o formato 00000-000' });
      } else {
        // Remove error if field is now valid
        const newErrors = { ...errors };
        delete newErrors[name];
        setErrors(newErrors);
      }
      return;
    }
    
    // Handle character limits
    let newValue = value;
    let hasError = false;
    
    if (name === 'street' && value.length > 100) {
      newValue = value.slice(0, 100);
      hasError = true;
    } else if ((name === 'neighborhood' || name === 'city') && value.length > 50) {
      newValue = value.slice(0, 50);
      hasError = true;
    }
    
    setFormData({ ...formData, [name]: newValue });
    
    if (hasError) {
      setErrors({ ...errors, [name]: `Máximo de ${name === 'street' ? '100' : '50'} caracteres` });
    } else if (errors[name]) {
      // Remove error if field is now valid
      const newErrors = { ...errors };
      delete newErrors[name];
      setErrors(newErrors);
    }
    
    // Validate required fields
    if (newValue.trim() === '' && ['zip_code', 'street', 'neighborhood', 'city', 'state'].includes(name)) {
      setErrors({ ...errors, [name]: 'Campo obrigatório' });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
    
    // Remove error if field is now valid
    if (errors[name]) {
      const newErrors = { ...errors };
      delete newErrors[name];
      setErrors(newErrors);
    }
  };

  const searchViaCEP = async () => {
    if (!formData.zip_code || formData.zip_code.length < 8) {
      setErrors({ ...errors, zip_code: 'CEP incompleto' });
      return;
    }

    setIsSearchingCEP(true);
    try {
      const addressData = await postalCodeService.fetchFromViaCEP(formData.zip_code);
      
      if (addressData) {
        setFormData({
          ...formData,
          street: addressData.street || formData.street,
          neighborhood: addressData.neighborhood || formData.neighborhood,
          city: addressData.city || formData.city,
          state: addressData.state || formData.state,
          registration_origin: 'auto',
        });
        toast({
          title: "CEP encontrado!",
          description: "Os dados do endereço foram preenchidos automaticamente.",
          variant: "default"
        });
      } else {
        toast({
          title: "CEP não encontrado",
          description: "Não foi possível encontrar o endereço para este CEP.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Failed to search CEP:", error);
      toast({
        title: "Erro",
        description: "Falha ao buscar o CEP. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSearchingCEP(false);
    }
  };

  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};
    
    // Validate required fields
    if (!formData.zip_code?.trim()) {
      newErrors.zip_code = 'CEP é obrigatório';
    } else if (!/^\d{5}-\d{3}$/.test(formData.zip_code)) {
      newErrors.zip_code = 'CEP inválido. Use o formato 00000-000';
    }
    
    if (!formData.street?.trim()) {
      newErrors.street = 'Logradouro é obrigatório';
    } else if (formData.street.length > 100) {
      newErrors.street = 'Logradouro deve ter no máximo 100 caracteres';
    }
    
    if (!formData.neighborhood?.trim()) {
      newErrors.neighborhood = 'Bairro é obrigatório';
    } else if (formData.neighborhood.length > 50) {
      newErrors.neighborhood = 'Bairro deve ter no máximo 50 caracteres';
    }
    
    if (!formData.city?.trim()) {
      newErrors.city = 'Cidade é obrigatória';
    } else if (formData.city.length > 50) {
      newErrors.city = 'Cidade deve ter no máximo 50 caracteres';
    }
    
    if (!formData.state?.trim()) {
      newErrors.state = 'Estado é obrigatório';
    }
    
    setErrors(newErrors);
    
    // Check if this CEP already exists (only for new records)
    if (!formData.id && Object.keys(newErrors).length === 0) {
      try {
        const exists = await postalCodeService.checkPostalCodeExists(formData.zip_code || '');
        if (exists) {
          setErrors({ ...newErrors, zip_code: 'Este CEP já está cadastrado' });
          return false;
        }
      } catch (error) {
        console.error("Error checking for duplicate CEP:", error);
      }
    }
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate the form
    const isValid = await validateForm();
    if (!isValid) return;
    
    setIsLoading(true);
    try {
      // In a real app, this would be an API call to save the postal code
      const savedPostalCode = await postalCodeService.savePostalCode(formData);
      
      onSave(savedPostalCode);
      toast({
        title: "Sucesso",
        description: "CEP salvo com sucesso.",
        variant: "default"
      });
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save postal code:", error);
      toast({
        title: "Erro",
        description: "Falha ao salvar o CEP. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    // Reset to initial values or empty form for new entries
    setFormData({
      id: postalCode?.id || '',
      company_id: companyId || postalCode?.company_id || '',
      zip_code: '',
      street: '',
      neighborhood: '',
      city: '',
      state: '',
      registration_origin: 'manual',
    });
    setErrors({});
  };

  // Get the display text for the origin
  const getOriginDisplayText = (origin: string) => {
    return origin === 'auto' ? 'Automático (API)' : 'Manual';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {postalCode?.id ? "Editar CEP" : "Novo CEP"}
            </DialogTitle>
            <DialogDescription>
              Preencha os dados do CEP a ser cadastrado.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="zip_code">
                CEP<span className="text-red-500">*</span>
              </Label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    id="zip_code"
                    name="zip_code"
                    value={formData.zip_code}
                    onChange={handleChange}
                    placeholder="00000-000"
                    maxLength={9}
                    className={errors.zip_code ? "border-red-500" : ""}
                  />
                  {errors.zip_code && (
                    <p className="text-red-500 text-xs mt-1">{errors.zip_code}</p>
                  )}
                </div>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={searchViaCEP}
                  disabled={isSearchingCEP || !formData.zip_code || formData.zip_code.length < 8}
                  className="min-w-[140px]"
                >
                  {isSearchingCEP ? (
                    <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Buscar ViaCEP
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="street">
                Logradouro<span className="text-red-500">*</span>
              </Label>
              <Input
                id="street"
                name="street"
                value={formData.street}
                onChange={handleChange}
                placeholder="Nome da rua, avenida, etc."
                maxLength={100}
                className={errors.street ? "border-red-500" : ""}
              />
              {errors.street && (
                <p className="text-red-500 text-xs mt-1">{errors.street}</p>
              )}
              <p className="text-gray-500 text-xs mt-1">{`${formData.street?.length || 0}/100 caracteres`}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="neighborhood">
                  Bairro<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="neighborhood"
                  name="neighborhood"
                  value={formData.neighborhood}
                  onChange={handleChange}
                  placeholder="Bairro"
                  maxLength={50}
                  className={errors.neighborhood ? "border-red-500" : ""}
                />
                {errors.neighborhood && (
                  <p className="text-red-500 text-xs mt-1">{errors.neighborhood}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">{`${formData.neighborhood?.length || 0}/50 caracteres`}</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="city">
                  Cidade<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  placeholder="Cidade"
                  maxLength={50}
                  className={errors.city ? "border-red-500" : ""}
                />
                {errors.city && (
                  <p className="text-red-500 text-xs mt-1">{errors.city}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">{`${formData.city?.length || 0}/50 caracteres`}</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="state">
                  Estado<span className="text-red-500">*</span>
                </Label>
                <Select 
                  value={formData.state}
                  onValueChange={(value) => handleSelectChange('state', value)}
                >
                  <SelectTrigger 
                    id="state"
                    className={errors.state ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Selecione o estado" />
                  </SelectTrigger>
                  <SelectContent>
                    {brazilianStates.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.state && (
                  <p className="text-red-500 text-xs mt-1">{errors.state}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="registration_origin">Origem do Cadastro</Label>
                <div className="h-10 px-3 py-2 border border-input bg-background rounded-md flex items-center">
                  <Badge variant={formData.registration_origin === 'auto' ? 'secondary' : 'outline'}>
                    {getOriginDisplayText(formData.registration_origin || 'manual')}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  A origem é definida automaticamente com base no método de cadastro
                </p>
              </div>
            </div>
          </div>
          
          <DialogFooter className="flex justify-between sm:justify-end gap-2">
            <div className="flex gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
              
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleReset}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Limpar
              </Button>
            </div>
            
            <Button 
              type="submit" 
              disabled={isLoading}
              className="gap-2"
            >
              {isLoading ? (
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              Salvar
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
