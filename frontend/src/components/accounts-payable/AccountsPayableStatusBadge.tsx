
import { Badge } from "@/components/ui/badge";

interface AccountsPayableStatusBadgeProps {
  status: string;
}

export default function AccountsPayableStatusBadge({
  status,
}: AccountsPayableStatusBadgeProps) {
  switch (status) {
    case 'pending':
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Pendente</Badge>;
    case 'partial':
    case 'partially_paid':
      return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Pagamento Parcial</Badge>;
    case 'paid':
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Pago</Badge>;
    case 'overdue':
      return <Badge className="bg-[#ea384c] text-white border-0">Em Atraso</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}
