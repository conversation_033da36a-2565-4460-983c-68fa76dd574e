
import { CalendarIcon, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface PeriodFilterProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

const PeriodFilter = ({ selectedPeriod, onPeriodChange }: PeriodFilterProps) => {
  const [open, setOpen] = useState(false);
  
  const periods = [
    { id: "current-month", label: "Este Mês" },
    { id: "next-month", label: "Próxi<PERSON>" },
    { id: "previous-month", label: "Mês Anterior" },
    { id: "current-week", label: "Esta Semana" },
    { id: "next-week", label: "Próxima Semana" },
    { id: "last-3-months", label: "Últimos 3 Meses" },
    { id: "current-year", label: "<PERSON>ste Ano" },
    { id: "today", label: "Hoje" },
  ];

  const handlePeriodSelect = (period: string) => {
    onPeriodChange(period);
    setOpen(false); // Close the popover after selection
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-2 bg-white dark:bg-dark-card shadow-sm border border-gray-100 dark:border-dark-card-light rounded-md justify-between h-10 px-4 py-2 w-40"
        >
          <div className="flex items-center gap-1.5">
            <CalendarIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-800 dark:text-gray-200 font-medium">
              {periods.find(p => p.id === selectedPeriod)?.label || "Este Mês"}
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-0 shadow-lg rounded-md border border-gray-100 dark:border-dark-card-light dark:bg-dark-card">
        <div className="py-1 max-h-72 overflow-y-auto scrollbar-thin">
          {periods.map((period) => (
            <div 
              key={period.id}
              className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-dark-card-light cursor-pointer"
              onClick={() => handlePeriodSelect(period.id)}
            >
              <div className="flex items-center gap-2">
                {selectedPeriod === period.id ? (
                  <Check className="h-4 w-4 text-gray-800 dark:text-white min-w-4" />
                ) : (
                  <div className="w-4 min-w-4" />
                )}
                <span className="text-sm text-gray-800 dark:text-gray-100 font-medium">{period.label}</span>
              </div>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default PeriodFilter;
