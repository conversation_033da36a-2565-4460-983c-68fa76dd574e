import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { api } from '@/services/api/axios';
import axios from 'axios';
import { toast } from 'sonner';

export function ApiConnectionTest() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error' | 'warning'>('idle');
  const [apiUrl, setApiUrl] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // Captura a URL da API das variáveis de ambiente
    setApiUrl(import.meta.env.VITE_API_URL || 'http://localhost:3000/api');
  }, []);

  const testConnection = async () => {
    setStatus('loading');
    setErrorMessage('');
    
    // Usar o endpoint específico de health check
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const healthEndpoint = `${apiUrl}/health`;
    
    try {
      console.log(`Testando conexão para: ${healthEndpoint}`);
      
      // Fazer a requisição para o endpoint de health check que agora está funcionando
      const response = await axios.get(healthEndpoint, { 
        timeout: 5000
      });
      
      console.log('Resposta do servidor:', response.data);
      
      // Verificar se a resposta contém o status "ok"
      if (response.data && response.data.status === 'ok') {
        setStatus('success');
        
        // Extrair informações adicionais do health check
        const { uptime, version } = response.data;
        const successMessage = `Conexão estabelecida com sucesso! Servidor está online há ${uptime} (v${version})`;
        
        toast.success(successMessage);
      } else {
        // Usar 'success' mas com uma mensagem de aviso
        setStatus('success');
        setErrorMessage('Servidor respondeu, mas status não é "ok"');
        toast.warning('Conexão estabelecida, mas o servidor pode não estar completamente funcional.');
      }
      
    } catch (error: any) {
      // Erro ao conectar com o backend - processar sem log
      
      // Verificação especial: se o erro for ECONNREFUSED, o servidor não está rodando
      if (error.code === 'ECONNREFUSED') {
        setStatus('error');
        setErrorMessage('Servidor não está acessível. Verifique se o backend está em execução.');
        toast.error('Falha na conexão com o backend. Servidor não encontrado.');
      }
      // Qualquer outro erro
      else {
        setStatus('error');
        setErrorMessage(
          error.response?.data?.message || 
          error.message || 
          'Erro desconhecido ao conectar com o backend'
        );
        toast.error('Falha na conexão com o backend. Verifique o console para mais detalhes.');
      }
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Teste de Conexão com API
          {status === 'idle' && <Badge variant="outline">Não testado</Badge>}
          {status === 'loading' && <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Testando...</Badge>}
          {status === 'success' && <Badge variant="outline" className="bg-green-100 text-green-800">Conectado</Badge>}
          {status === 'error' && <Badge variant="outline" className="bg-red-100 text-red-800">Falha</Badge>}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm">
          <p className="font-medium">URL da API:</p>
          <p className="font-mono text-muted-foreground break-all">{apiUrl}</p>
        </div>
        
        {status === 'success' && (
          <div className="flex items-center text-green-600 gap-2">
            <CheckCircle size={18} />
            <p>Conexão estabelecida com sucesso!</p>
          </div>
        )}
        
        {status === 'error' && (
          <div className="space-y-2">
            <div className="flex items-center text-red-600 gap-2">
              <XCircle size={18} />
              <p>Falha na conexão com o backend</p>
            </div>
            {errorMessage && (
              <div className="text-sm bg-red-50 p-2 rounded border border-red-200 text-red-700">
                <p className="font-medium">Mensagem de erro:</p>
                <p className="break-all">{errorMessage}</p>
              </div>
            )}
            <div className="flex items-center text-amber-600 gap-2 mt-2">
              <AlertCircle size={18} />
              <p className="text-sm">
                Verifique se o backend está em execução e se a configuração CORS está correta.
              </p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          onClick={testConnection} 
          className="w-full"
          disabled={status === 'loading'}
        >
          {status === 'loading' ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Testando...
            </>
          ) : (
            'Testar Conexão'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
