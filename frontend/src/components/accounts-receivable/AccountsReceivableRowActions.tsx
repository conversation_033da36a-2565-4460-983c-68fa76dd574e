
import { Button } from "@/components/ui/button";
import { Receipt } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface AccountsReceivableRowActionsProps {
  item: any;
  handleReceive: (item: any) => void;
  handleView: (item: any) => void;
}

export default function AccountsReceivableRowActions({
  item,
  handleReceive,
  handleView,
}: AccountsReceivableRowActionsProps) {
  const isReceived = item.status === 'received';
  
  return (
    <div className="flex space-x-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="default" 
              size="sm" 
              className={`h-8 ${isReceived 
                ? 'bg-[#8A898C] dark:bg-[#403E43] hover:bg-[#8A898C] dark:hover:bg-[#403E43] cursor-not-allowed text-white' 
                : 'bg-[#007FFF] hover:bg-[#0066CC]'}`}
              onClick={() => !isReceived && handleReceive(item)}
              disabled={isReceived}
            >
              <Receipt className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isReceived ? 'Já baixado' : 'Baixar'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
