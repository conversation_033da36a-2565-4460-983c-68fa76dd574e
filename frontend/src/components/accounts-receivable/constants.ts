// Mock options for selects
export const statusOptions = [
  { label: "Pending", value: "pending" },
  { label: "Partial", value: "partial" },
  { label: "Received", value: "received" },
];

export const categoryOptions = [
  { label: "Income Category 0", value: "Income Category 0" },
  { label: "Income Category 1", value: "Income Category 1" },
  { label: "Income Category 2", value: "Income Category 2" },
  { label: "Income Category 3", value: "Income Category 3" },
  { label: "Income Category 4", value: "Income Category 4" },
];

export const projectOptions = [
  { label: "Project 0", value: "Project 0" },
  { label: "Project 2", value: "Project 2" },
  { label: "Project 4", value: "Project 4" },
  { label: "Project 6", value: "Project 6" },
  { label: "Project 8", value: "Project 8" },
];

export const customerOptions = [
  { label: "Customer 1", value: "Customer 1" },
  { label: "Customer 2", value: "Customer 2" },
  { label: "Customer 3", value: "Customer 3" },
  { label: "Customer 4", value: "Customer 4" },
  { label: "Customer 5", value: "Customer 5" },
];

export const bankAccountOptions = [
  { label: "Main Account", value: "main" },
  { label: "Secondary Account", value: "secondary" },
];

// DEPRECATED: Use hooks usePaymentMethodOptions e useRecurrenceTypeOptions instead
// Mantido temporariamente para compatibilidade
export const recurrenceOptions = [
  { label: "Não recorrente", value: "none" },
  { label: "Diário", value: "daily" },
  { label: "Semanal", value: "weekly" },
  { label: "Quinzenal", value: "biweekly" },
  { label: "Mensal", value: "monthly" },
  { label: "Bimestral", value: "bimonthly" },
  { label: "Trimestral", value: "quarterly" },
  { label: "Semestral", value: "semiannually" },
  { label: "Anual", value: "annually" },
];

// DEPRECATED: Use hook usePaymentMethodOptions instead
// Mantido temporariamente para compatibilidade
export const paymentMethodOptions = [
  { label: "Dinheiro", value: "cash" },
  { label: "Cartão de Crédito", value: "credit_card" },
  { label: "Cartão de Débito", value: "debit_card" },
  { label: "Transferência Bancária", value: "bank_transfer" },
  { label: "Boleto", value: "boleto" },
  { label: "Pix", value: "pix" },
  { label: "Cheque", value: "check" },
];

// Define field names for refreshing
export type RefreshableField = 
  | "entity" 
  | "category" 
  | "project" 
  | "bankAccount" 
  | "paymentMethod" 
  | "recurrence";

// Mock data - this would be replaced by actual data fetching
export const MOCK_ACCOUNTS = [
  {
    id: "1",
    description: "Monthly Service Fee",
    entity: "Customer ABC",
    dueDate: new Date(),
    amount: 1500,
    paidAmount: 0,
    status: "pending",
    category: "Services",
    project: "Project X",
    bankAccount: "main",
    notes: "Monthly recurring payment",
    recurrence: "monthly",
    paymentMethod: "bank_transfer",
    invoiceNumber: "INV-001",
    interestAmount: 0,
    discountAmount: 0,
  },
  // More mock data would be here
]; 