import React from 'react';

// Componente simples para capturar erros em React
const ErrorLogger: React.FC<{ componentName: string; children: React.ReactNode }> = ({
  componentName,
  children
}) => {
  try {
    return <>{children}</>;
  } catch (error) {
    // Erro capturado - renderizar fallback sem expor detalhes
    return <div>Erro ao renderizar {componentName}.</div>;
  }
};

export default ErrorLogger;
