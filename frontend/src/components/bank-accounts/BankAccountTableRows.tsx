import React from 'react';
import { TableRow, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/formatters';
import { cn } from '@/lib/utils';
import { BankAccount } from '@/types/api'; // Certifique-se que este tipo existe e está correto

interface BankAccountTableRowsProps {
  accounts: BankAccount[] | undefined; // Permitir undefined
  onEdit: (account: BankAccount) => void;
  onDelete: (id: string) => void;
  companyId: string | null; // Adicionar companyId
}

const BankAccountTableRows: React.FC<BankAccountTableRowsProps> = ({ accounts, onEdit, onDelete, companyId }) => {
  // Logs removidos por segurança

  if (!accounts || accounts.length === 0) {
    return (
      <TableRow>
        <TableCell colSpan={7} className="h-24 text-center">
          Nenhuma conta bancária encontrada.
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {accounts.map((account, index) => { // Adicionar index
        // Logs removidos por segurança
        // Certifique-se de que o return está dentro das chaves do map
        return (
          // Adicionar estilo de fundo alternado para teste visual
          <TableRow key={account.id} style={{ backgroundColor: index % 2 === 0 ? 'rgba(0,0,0,0.02)' : 'transparent' }}>
            <TableCell className="font-medium">
              <div className="flex items-center gap-2">
                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                  {account.bank?.code?.charAt(0) || account.name.charAt(0)}
                </div>
                <span>{account.bank?.name || account.name}</span>
              </div>
            </TableCell>
            {/* Remover ID de teste */}
            <TableCell>{account.accountNumber}</TableCell>
            <TableCell>
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                account.type === "checking" && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                account.type === "savings" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                account.type === "investment" && "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
                account.type === "cash" && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
                account.type === "other" && "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              )}>
                {account.type === "checking" ? "Corrente" :
                 account.type === "savings" ? "Poupança" :
                 account.type === "investment" ? "Investimento" :
                 account.type === "cash" ? "Dinheiro" :
                 account.type === "other" ? "Outro" : account.type}
              </span>
            </TableCell>
            <TableCell className="text-right">
              {formatCurrency(account.currentBalance ?? 0)}
            </TableCell>
            <TableCell className="text-right">
              {account.initialBalance != null && account.initialBalance > 0 ? formatCurrency(account.initialBalance) : '-'}
            </TableCell>
            <TableCell className="text-center text-sm text-muted-foreground">
              {formatDate(new Date(account.updatedAt || new Date()))}
            </TableCell>
            <TableCell>
              <div className="flex justify-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(account)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(account.id)}
                  className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ); // Fechar o return
      })} {/* Fechar o map */}
    </>
  );
};

export default BankAccountTableRows;