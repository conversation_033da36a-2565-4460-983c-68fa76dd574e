import { useEffect, useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import TransactionAmountField from '../transactions/TransactionAmountField';
import { useCreateBankAccount, useUpdateBankAccount } from '@/hooks/api/useBankAccounts';
import { useBanks } from '@/hooks/api/useBanks';
import { useDefaultCurrency } from '@/hooks/api/useCurrencies';
import { useAuth } from '@/contexts/AuthContext';
import { BankAccount, Bank, UpdateBankAccountRequest } from '@/types/api';
import { parseCurrencyToNumber } from '@/utils/currencyUtils';

const accountTypeOptions = [
  { value: 'checking', label: 'Conta Corrente' },
  { value: 'savings', label: 'Conta Poupança' },
  { value: 'investment', label: 'Conta Investimento' },
  { value: 'cash', label: 'Dinheiro' },
  { value: 'other', label: 'Outro' },
];

// Form schema
const bankAccountSchema = z.object({
  name: z.string().min(1, { message: 'Nome da conta é obrigatório' }),
  bankId: z.string().optional().nullable(),
  accountNumber: z.string().optional(),
  type: z.enum(['checking', 'savings', 'investment', 'cash', 'other'] as const, {
    required_error: 'Tipo de conta é obrigatório',
  }),
  initialBalance: z.preprocess(
    (val) => parseCurrencyToNumber(String(val ?? '')),
    z.number()
      .nonnegative({ message: "Saldo inicial não pode ser negativo" })
      .refine((val) => !isNaN(val), { message: "Saldo inicial inválido" })
  ),
  description: z.string().optional(),
});

type BankAccountFormValues = z.infer<typeof bankAccountSchema>;

interface BankAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  account?: BankAccount;
}

const BankAccountModal = ({ isOpen, onClose, account }: BankAccountModalProps) => {
  const isEditing = Boolean(account);
  const { activeCompanyId } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Buscar bancos e moedas válidos
  const { data: banksApiResponse, isLoading: isLoadingBanks } = useBanks();
  const { data: defaultCurrency, isLoading: isLoadingCurrency } = useDefaultCurrency();

  // Extrair a lista de bancos da resposta da API
  const banksList = banksApiResponse?.data || [];

  // Logs removidos por segurança
  useEffect(() => {
    // Efeito para monitorar mudanças - logs removidos
  }, [activeCompanyId, banksApiResponse, banksList, defaultCurrency]);

  const createMutation = useCreateBankAccount();
  const updateMutation = useUpdateBankAccount();

  const form = useForm<BankAccountFormValues>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      name: '',
      bankId: '',
      accountNumber: '',
      type: 'checking',
      initialBalance: account ? account.initialBalance : 0,
      description: '',
    },
  });

  // Reset form when opening the modal
  useEffect(() => {
    if (isOpen) {
      if (account) {
        // Encontra o banco usando a lista da API
        const bank = banksList.find(b => b.id === account.bankId);

        form.reset({
          name: account.name || '',
          bankId: bank?.id || '',
          accountNumber: account.accountNumber || '',
          type: (account.type as any) || 'checking',
          initialBalance: account.initialBalance || 0,
          description: '',
        });
      } else {
        form.reset({
          name: '',
          bankId: '',
          accountNumber: '',
          type: 'checking',
          initialBalance: 0,
          description: '',
        });
      }
    }
  }, [isOpen, account, form.reset, banksList]);

  const onSubmit = async (values: BankAccountFormValues) => {
    setIsSubmitting(true);
    try {
      // --- Validações Iniciais ---
      // companyId será tratado pelo backend a partir do token JWT

      // Verificar se bancos e moeda padrão estão carregados (essencial para criação/edição)
      if (isLoadingBanks || isLoadingCurrency) {
        toast.info('Aguardando dados de bancos e moeda...');
        setIsSubmitting(false);
        return;
      }

      if (!banksList || banksList.length === 0) {
        toast.error('Erro: Lista de bancos não carregada.');
        setIsSubmitting(false);
        return;
      }

      if (!defaultCurrency) {
        toast.error('Erro: Moeda padrão não carregada.');
        setIsSubmitting(false);
        return;
      }
      // --- Fim Validações ---

      // Preparar dados para API
      if (isEditing && account) {
        const updateData: UpdateBankAccountRequest = {
          name: values.name,
          type: values.type,
          accountNumber: values.accountNumber || undefined,
          bankId: values.bankId || undefined,
          // Incluir saldo inicial apenas se não estiver bloqueado
          ...((!account.isInitialBalanceLocked) && { initialBalance: values.initialBalance }),
        };
        console.log('Enviando dados para ATUALIZAR conta bancária:', updateData);
        console.log('Account isInitialBalanceLocked:', account.isInitialBalanceLocked);
        await updateMutation.mutateAsync({ id: account.id, data: updateData });
        toast.success('Conta bancária atualizada com sucesso!');
      } else {
        // Criação
        const createData = { // Removido tipo explícito CreateBankAccountRequest
          // Mapeamento para o DTO do Backend:
          accountType: values.type, // de type para accountType
          initialBalance: values.initialBalance, // Usar o campo correto
          balanceDate: new Date().toISOString(), // Adicionado data atual
          currencyId: defaultCurrency.id, // Adicionado ID da moeda padrão
          name: values.name,
          accountNumber: values.accountNumber || undefined,
          bankId: values.bankId || undefined,
          companyId: activeCompanyId, // Incluir explicitamente o ID da empresa atual
        };
        console.log('Enviando dados para CRIAR conta bancária:', createData);
        await createMutation.mutateAsync(createData);
        toast.success('Conta bancária criada com sucesso!');
      }

      onClose(); 
    } catch (error) {
      console.error('Erro ao salvar conta bancária:', error);
      const errorMessage = error instanceof Error ? error.message : 'Ocorreu um erro desconhecido.';
      toast.error(`Erro ao salvar: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Editar Conta Bancária' : 'Nova Conta Bancária'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Modifique os detalhes da sua conta bancária.' : 'Preencha os detalhes para adicionar uma nova conta bancária.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Conta *</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Conta Principal Bradesco" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="bankId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Banco</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={isLoadingBanks}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={isLoadingBanks ? "Carregando bancos..." : "Selecione um banco"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingBanks ? (
                          <SelectItem value="loading" disabled>Carregando...</SelectItem>
                        ) : (
                          banksList.map((bank: Bank) => (
                            <SelectItem key={bank.id} value={bank.id}>
                              {bank.name} ({bank.code})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número da Conta</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: 12345-6" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
               <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Conta *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {accountTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              <FormField
                control={form.control}
                name="initialBalance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Saldo Inicial *</FormLabel>
                    <TransactionAmountField
                      id="initialBalance"
                      value={field.value ?? 0} // Passar o valor diretamente como número
                      onChange={(numericValue) => {
                        // Atualizar o form state com o valor numérico recebido
                        field.onChange(numericValue);
                      }}
                      disabled={isEditing && account?.isInitialBalanceLocked} // Saldo inicial não editável se estiver bloqueado
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting || isLoadingBanks || isLoadingCurrency}>
                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                {isEditing ? 'Salvar Alterações' : 'Criar Conta'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default BankAccountModal;
