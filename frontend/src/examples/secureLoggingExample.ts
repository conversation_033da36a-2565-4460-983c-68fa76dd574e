/**
 * Exemplos de uso do sistema de logging seguro - FluxoMax
 * 
 * Este arquivo demonstra como usar corretamente o sistema de logging
 * seguro em substituição aos console.log removidos.
 */

import { logger, LogLevel } from '@/utils/secureLogger';

// ❌ ANTES (INSEGURO) - NÃO FAZER
// console.log('User logged in:', { email: '<EMAIL>', token: 'abc123' });
// console.error('API Error:', error);
// console.warn('Invalid data:', sensitiveData);

// ✅ DEPOIS (SEGURO) - FAZER

export class SecureLoggingExamples {
  
  /**
   * Exemplo 1: Logging de autenticação
   */
  static authenticationExample() {
    // ✅ Log de debug para desenvolvimento
    logger.debug('User authentication attempt', {
      component: 'AuthService',
      action: 'login',
      timestamp: new Date().toISOString()
    });

    // ✅ Log de erro com dados sanitizados automaticamente
    const userData = {
      email: '<EMAIL>', // Será automaticamente redacted
      token: 'sensitive-token',   // Será automaticamente redacted
      name: '<PERSON>'          // Dados não sensíveis permanecem
    };
    
    logger.error('Authentication failed', new Error('Invalid credentials'), {
      component: 'AuthService',
      action: 'login',
      userData // Campos sensíveis serão automaticamente filtrados
    });
  }

  /**
   * Exemplo 2: Logging de API
   */
  static apiExample() {
    // ✅ Log de requisição API
    logger.debug('API request initiated', {
      component: 'BankAccountService',
      action: 'fetchAccounts',
      url: '/api/bank-accounts',
      method: 'GET'
    });

    // ✅ Log de erro de API
    const apiError = new Error('Network timeout');
    logger.error('API request failed', apiError, {
      component: 'BankAccountService',
      action: 'fetchAccounts',
      statusCode: 500,
      retryCount: 3
    });

    // ✅ Log crítico para falhas do sistema
    logger.critical('Database connection lost', new Error('Connection timeout'), {
      component: 'DatabaseService',
      action: 'connect',
      severity: 'high'
    });
  }

  /**
   * Exemplo 3: Logging de componentes React
   */
  static componentExample() {
    // ✅ Log de ciclo de vida do componente
    logger.debug('Component mounted', {
      component: 'BankAccountModal',
      action: 'mount',
      props: {
        isOpen: true,
        mode: 'create'
      }
    });

    // ✅ Log de erro em componente
    logger.error('Component render failed', new Error('Invalid props'), {
      component: 'BankAccountTable',
      action: 'render',
      propsReceived: true
    });
  }

  /**
   * Exemplo 4: Logging de dados bancários (sensíveis)
   */
  static bankingDataExample() {
    const bankAccount = {
      id: '123',
      accountNumber: '12345-6',     // Sensível - será redacted
      balance: 1000.50,
      bankName: 'Banco Exemplo',
      userId: 'user-456'
    };

    // ✅ Log seguro - dados sensíveis serão filtrados
    logger.info('Bank account created', {
      component: 'BankAccountService',
      action: 'create',
      accountData: bankAccount // accountNumber será automaticamente redacted
    });
  }

  /**
   * Exemplo 5: Logging de performance
   */
  static performanceExample() {
    const startTime = performance.now();
    
    // Simular operação
    setTimeout(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // ✅ Log de performance
      logger.info('Operation completed', {
        component: 'DataProcessor',
        action: 'processLargeDataset',
        duration: `${duration}ms`,
        recordsProcessed: 1000,
        performance: duration > 1000 ? 'slow' : 'fast'
      });
    }, 100);
  }

  /**
   * Exemplo 6: Logging de navegação
   */
  static navigationExample() {
    // ✅ Log de navegação
    logger.debug('Route changed', {
      component: 'Router',
      action: 'navigate',
      from: '/dashboard',
      to: '/bank-accounts',
      userId: 'user-123'
    });

    // ✅ Log de acesso negado
    logger.warn('Access denied', {
      component: 'ProtectedRoute',
      action: 'access_check',
      route: '/admin',
      reason: 'insufficient_permissions'
    });
  }

  /**
   * Exemplo 7: Logging de cache
   */
  static cacheExample() {
    // ✅ Log de cache hit
    logger.debug('Cache hit', {
      component: 'CacheService',
      action: 'get',
      key: 'bank-accounts-user-123',
      ttl: 300
    });

    // ✅ Log de cache miss
    logger.debug('Cache miss', {
      component: 'CacheService',
      action: 'get',
      key: 'user-profile-456',
      fallbackToApi: true
    });
  }

  /**
   * Exemplo 8: Logging de validação
   */
  static validationExample() {
    // ✅ Log de erro de validação
    logger.warn('Validation failed', {
      component: 'FormValidator',
      action: 'validate',
      field: 'email',
      value: '[REDACTED]', // Não incluir valor real
      rule: 'email_format'
    });

    // ✅ Log de dados inválidos
    logger.error('Invalid data received', new Error('Schema validation failed'), {
      component: 'ApiController',
      action: 'validateInput',
      schema: 'CreateBankAccountSchema',
      errors: ['missing_required_field', 'invalid_format']
    });
  }

  /**
   * Exemplo 9: Configuração dinâmica do logger
   */
  static configurationExample() {
    // ✅ Configurar logger para ambiente específico
    if (import.meta.env.PROD) {
      // Em produção, apenas logs críticos
      logger.updateConfig({
        maxLogLevel: LogLevel.ERROR,
        enableConsoleInDev: false
      });
    } else {
      // Em desenvolvimento, todos os logs
      logger.updateConfig({
        maxLogLevel: LogLevel.DEBUG,
        enableConsoleInDev: true
      });
    }

    // ✅ Adicionar campos sensíveis customizados
    logger.updateConfig({
      sensitiveFields: [
        'password', 'token', 'secret',
        'customSensitiveField', 'internalId'
      ]
    });
  }

  /**
   * Exemplo 10: Logging de monitoramento
   */
  static monitoringExample() {
    // ✅ Log de métrica de negócio
    logger.info('Business metric', {
      component: 'MetricsCollector',
      action: 'record',
      metric: 'daily_transactions',
      value: 150,
      date: new Date().toISOString().split('T')[0]
    });

    // ✅ Log de evento de sistema
    logger.info('System event', {
      component: 'SystemMonitor',
      action: 'health_check',
      status: 'healthy',
      uptime: '24h',
      memoryUsage: '45%'
    });
  }
}

// Exemplo de uso em um componente React
export const ExampleComponent = () => {
  const handleSubmit = async (formData: any) => {
    try {
      logger.debug('Form submission started', {
        component: 'ExampleComponent',
        action: 'submit',
        formFields: Object.keys(formData)
      });

      // Simular chamada API
      const result = await fetch('/api/submit', {
        method: 'POST',
        body: JSON.stringify(formData)
      });

      if (!result.ok) {
        throw new Error(`HTTP ${result.status}`);
      }

      logger.info('Form submitted successfully', {
        component: 'ExampleComponent',
        action: 'submit',
        success: true
      });

    } catch (error) {
      logger.error('Form submission failed', error as Error, {
        component: 'ExampleComponent',
        action: 'submit',
        formData: formData // Dados sensíveis serão automaticamente filtrados
      });
    }
  };

  return null; // Componente de exemplo
};

// Exportar exemplos para uso em outros arquivos
export default SecureLoggingExamples;
