import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ErrorBoundary from "@/components/ErrorBoundary";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import BankAccounts from "./pages/BankAccounts";
import BankAccountsTemp from "./pages/BankAccountsTemp";
import Customers from "./pages/Customers";
import Suppliers from "./pages/Suppliers";
import AccountsPayable from "./pages/AccountsPayable";
import AccountsReceivable from "./pages/AccountsReceivable";
import AccountsPayableDetail from "./pages/AccountsPayableDetail";
import AccountsReceivableDetail from "./pages/AccountsReceivableDetail";
import Transactions from "./pages/Transactions";
import TransactionDetail from "./pages/TransactionDetail";
import Projects from "./pages/Projects";
import Analytics from "./pages/Analytics";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";
import CompanySettings from "./pages/CompanySettings";
import CompanyPeriods from "./pages/CompanyPeriods";
import UserSettings from "./pages/UserSettings";
import SecuritySettings from "./pages/SecuritySettings";
import GeneralSettings from "./pages/GeneralSettings";
import BankSettings from "./pages/BankSettings";
import Profile from "./pages/Profile";
import AddressesPage from "./pages/AddressesPage";
import CategoriesPage from "./pages/CategoriesPage";
import PostalCodesPage from "./pages/PostalCodesPage";
import DiagnosticoPage from "./pages/Diagnostico";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";

// Removida importação do serviço de monitoramento para evitar referência circular

// Importar o componente ProtectedRoute
import ProtectedRoute from "./components/auth/ProtectedRoute";

// Componente interno que usa o AuthProvider
const AppRoutes = () => {
  return (
    <Routes>
      {/* Rotas públicas */}
      <Route path="/login" element={<ProtectedRoute requiresAuth={false}><Login /></ProtectedRoute>} />
      <Route path="/register" element={<ProtectedRoute requiresAuth={false}><Register /></ProtectedRoute>} />
      <Route path="/forgot-password" element={<ProtectedRoute requiresAuth={false}><ForgotPassword /></ProtectedRoute>} />
      <Route path="/reset-password/:token" element={<ProtectedRoute requiresAuth={false}><ResetPassword /></ProtectedRoute>} />
      <Route path="/diagnostico" element={<DiagnosticoPage />} />

      {/* Rotas protegidas */}
      <Route path="/" element={<ProtectedRoute><Index /></ProtectedRoute>} />
      <Route path="/bank-accounts" element={<ProtectedRoute><BankAccounts /></ProtectedRoute>} />
      <Route path="/customers" element={<ProtectedRoute><Customers /></ProtectedRoute>} />
      <Route path="/suppliers" element={<ProtectedRoute><Suppliers /></ProtectedRoute>} />
      <Route path="/accounts-payable" element={<ProtectedRoute><AccountsPayable /></ProtectedRoute>} />
      <Route path="/accounts-payable/:id/:mode" element={<ProtectedRoute><AccountsPayableDetail /></ProtectedRoute>} />
      <Route path="/accounts-receivable" element={<ProtectedRoute><AccountsReceivable /></ProtectedRoute>} />
      <Route path="/accounts-receivable/:id/:mode" element={<ProtectedRoute><AccountsReceivableDetail /></ProtectedRoute>} />
      <Route path="/transactions" element={<ProtectedRoute><Transactions /></ProtectedRoute>} />
      <Route path="/transactions/:id/:mode" element={<ProtectedRoute><TransactionDetail /></ProtectedRoute>} />
      <Route path="/projects" element={<ProtectedRoute><Projects /></ProtectedRoute>} />
      <Route path="/analytics" element={<ProtectedRoute><Analytics /></ProtectedRoute>} />
      <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
      <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
      <Route path="/settings/companies" element={<ProtectedRoute><CompanySettings /></ProtectedRoute>} />
      <Route path="/settings/companies/:companyId/periods" element={<ProtectedRoute><CompanyPeriods /></ProtectedRoute>} />
      <Route path="/settings/banks" element={<ProtectedRoute><BankSettings /></ProtectedRoute>} />
      <Route path="/settings/users" element={<ProtectedRoute><UserSettings /></ProtectedRoute>} />
      <Route path="/settings/security" element={<ProtectedRoute><SecuritySettings /></ProtectedRoute>} />
      <Route path="/settings/general" element={<ProtectedRoute><GeneralSettings /></ProtectedRoute>} />
      <Route path="/addresses" element={<ProtectedRoute><AddressesPage /></ProtectedRoute>} />
      <Route path="/categories" element={<ProtectedRoute><CategoriesPage /></ProtectedRoute>} />
      <Route path="/postal-codes" element={<ProtectedRoute><PostalCodesPage /></ProtectedRoute>} />

      {/* ADICIONE TODAS AS ROTAS PERSONALIZADAS ACIMA DA ROTA CATCH-ALL "*" */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

// Componente App com hot-reloading - modificado novamente
const App = () => (
  <ErrorBoundary>
      <TooltipProvider>
        <Sonner />
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
        <BrowserRouter>
          <AuthProvider>
            <AppRoutes />
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    {/* </QueryClientContext> is removed as it's already in main.tsx */}
  </ErrorBoundary>
);

export default App;
