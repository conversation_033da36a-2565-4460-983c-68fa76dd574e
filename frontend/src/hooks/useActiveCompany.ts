import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { queryClientInstance } from '@/contexts/QueryClientContext';

/**
 * Hook personalizado para gerenciar a empresa ativa e invalidar queries quando ela muda
 * @returns O ID da empresa ativa
 */
export function useActiveCompany() {
  const { activeCompanyId } = useAuth();

  // Efeito para invalidar queries quando a empresa ativa muda
  useEffect(() => {
    if (activeCompanyId && queryClientInstance) {
      // Empresa ativa mudou - invalidar queries relacionadas
      
      // Invalidar queries específicas que dependem da empresa
      queryClientInstance.invalidateQueries({ queryKey: ['entities'] });
      queryClientInstance.invalidateQueries({ queryKey: ['categories'] });
      queryClientInstance.invalidateQueries({ queryKey: ['projects'] });
      queryClientInstance.invalidateQueries({ queryKey: ['bankAccounts'] });
      queryClientInstance.invalidateQueries({ queryKey: ['transactions'] });
      queryClientInstance.invalidateQueries({ queryKey: ['accountsPayable'] });
      queryClientInstance.invalidateQueries({ queryKey: ['accountsReceivable'] });
      queryClientInstance.invalidateQueries({ queryKey: ['customPeriods'] });
      
      // Invalidar também as coleções específicas
      queryClientInstance.invalidateQueries({ queryKey: ['entities', 'clients'] });
      queryClientInstance.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
      queryClientInstance.invalidateQueries({ queryKey: ['categories', 'tree'] });
    }
  }, [activeCompanyId]);

  return activeCompanyId;
}
