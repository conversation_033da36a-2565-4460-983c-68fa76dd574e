// DEPRECATED: Use hooks individuais useBankAccounts, usePaymentMethodOptions, useRecurrenceTypeOptions instead
// Este hook será removido em versões futuras
import { useBankAccounts, usePaymentMethodOptions, useRecurrenceTypeOptions } from '@/hooks/api';
import { useActiveCompany } from '@/hooks/useActiveCompany';

interface TransactionFormData {
  bankAccounts: { id: string; name: string }[];
  paymentMethods: { id: string; name: string }[];
  recurrenceOptions: { id: string; name: string }[];
  loadingBankAccounts: boolean;
  loadingPaymentMethods: boolean;
  loadingRecurrenceOptions: boolean;
  errorBankAccounts: string | null;
  errorPaymentMethods: string | null;
  errorRecurrenceOptions: string | null;
}

export const useTransactionFormData = (): TransactionFormData => {
  const activeCompanyId = useActiveCompany();
  
  // Usar os novos hooks individuais
  const { data: bankAccountsData, isLoading: loadingBankAccounts, error: errorBankAccounts } = useBankAccounts(
    1, 100, { companyId: activeCompanyId }
  );
  
  const { data: paymentMethodsData, isLoading: loadingPaymentMethods, error: errorPaymentMethods } = usePaymentMethodOptions();
  
  const { data: recurrenceOptionsData, isLoading: loadingRecurrenceOptions, error: errorRecurrenceOptions } = useRecurrenceTypeOptions();

  // Mapear para o formato esperado (compatibilidade com código existente)
  const bankAccounts = bankAccountsData?.data?.map(account => ({
    id: account.id,
    name: account.name
  })) || [];

  const paymentMethods = paymentMethodsData?.map(method => ({
    id: method.id,
    name: method.name
  })) || [];

  const recurrenceOptions = recurrenceOptionsData?.map(option => ({
    id: option.id,
    name: option.name
  })) || [];

  return {
    bankAccounts,
    paymentMethods,
    recurrenceOptions,
    loadingBankAccounts,
    loadingPaymentMethods,
    loadingRecurrenceOptions,
    errorBankAccounts: errorBankAccounts ? 'Erro ao carregar contas bancárias' : null,
    errorPaymentMethods: errorPaymentMethods ? 'Erro ao carregar métodos de pagamento' : null,
    errorRecurrenceOptions: errorRecurrenceOptions ? 'Erro ao carregar opções de recorrência' : null
  };
};
