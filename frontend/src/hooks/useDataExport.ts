import { useState, useCallback } from 'react';
import { api } from '@/services/api/axios';
import { toast } from 'sonner';

type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json';

interface ExportOptions {
  endpoint: string;
  filename?: string;
  format?: ExportFormat;
  params?: Record<string, any>;
  onSuccess?: (blob: Blob) => void;
  onError?: (error: any) => void;
}

interface UseDataExportReturn {
  exporting: boolean;
  exportData: (options?: Partial<ExportOptions>) => Promise<void>;
  exportFormats: ExportFormat[];
  selectedFormat: ExportFormat;
  setSelectedFormat: (format: ExportFormat) => void;
}

export function useDataExport(defaultOptions?: ExportOptions): UseDataExportReturn {
  const [exporting, setExporting] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>(defaultOptions?.format || 'csv');
  
  const exportFormats: ExportFormat[] = ['csv', 'excel', 'pdf', 'json'];
  
  const getContentType = (format: ExportFormat): string => {
    switch (format) {
      case 'csv':
        return 'text/csv';
      case 'excel':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'pdf':
        return 'application/pdf';
      case 'json':
        return 'application/json';
      default:
        return 'application/octet-stream';
    }
  };
  
  const getFileExtension = (format: ExportFormat): string => {
    switch (format) {
      case 'csv':
        return '.csv';
      case 'excel':
        return '.xlsx';
      case 'pdf':
        return '.pdf';
      case 'json':
        return '.json';
      default:
        return '';
    }
  };
  
  const exportData = useCallback(
    async (options?: Partial<ExportOptions>) => {
      const mergedOptions: ExportOptions = {
        endpoint: options?.endpoint || defaultOptions?.endpoint || '',
        filename: options?.filename || defaultOptions?.filename || 'export',
        format: options?.format || selectedFormat,
        params: {
          ...(defaultOptions?.params || {}),
          ...(options?.params || {}),
          format: options?.format || selectedFormat,
        },
        onSuccess: options?.onSuccess || defaultOptions?.onSuccess,
        onError: options?.onError || defaultOptions?.onError,
      };
      
      if (!mergedOptions.endpoint) {
        toast.error('Endpoint de exportação não especificado');
        return;
      }
      
      try {
        setExporting(true);
        
        const response = await api.get(mergedOptions.endpoint, {
          params: mergedOptions.params,
          responseType: 'blob',
        });
        
        const contentType = getContentType(mergedOptions.format);
        const fileExtension = getFileExtension(mergedOptions.format);
        const blob = new Blob([response.data], { type: contentType });
        
        // Criar um link para download e clicar nele automaticamente
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${mergedOptions.filename}${fileExtension}`);
        document.body.appendChild(link);
        link.click();
        
        // Limpar o link após o download
        link.parentNode?.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        toast.success('Exportação concluída com sucesso');
        
        if (mergedOptions.onSuccess) {
          mergedOptions.onSuccess(blob);
        }
      } catch (error) {
        console.error('Erro ao exportar dados:', error);
        toast.error('Erro ao exportar dados. Tente novamente mais tarde.');
        
        if (mergedOptions.onError) {
          mergedOptions.onError(error);
        }
      } finally {
        setExporting(false);
      }
    },
    [defaultOptions, selectedFormat]
  );
  
  return {
    exporting,
    exportData,
    exportFormats,
    selectedFormat,
    setSelectedFormat,
  };
}
