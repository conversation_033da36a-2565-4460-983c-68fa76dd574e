import { useState, useCallback, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

export interface FilterOption<T = any> {
  id: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number' | 'numberRange' | 'boolean';
  options?: Array<{ label: string; value: string | number | boolean }>;
  defaultValue?: T;
  placeholder?: string;
  transform?: (value: any) => any;
}

export interface FilterState {
  [key: string]: any;
}

interface UseFiltersOptions {
  filters: FilterOption[];
  initialState?: FilterState;
  syncWithUrl?: boolean;
  onChange?: (filters: FilterState) => void;
}

export function useFilters({
  filters,
  initialState = {},
  syncWithUrl = false,
  onChange,
}: UseFiltersOptions) {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Inicializar o estado com valores padrão dos filtros ou valores da URL
  const getInitialFilterState = useCallback(() => {
    const state: FilterState = {};
    
    // Primeiro, definir valores padrão dos filtros
    filters.forEach((filter) => {
      state[filter.id] = filter.defaultValue ?? null;
    });
    
    // Sobrescrever com valores do estado inicial fornecido
    Object.keys(initialState).forEach((key) => {
      state[key] = initialState[key];
    });
    
    // Se sincronizar com URL, sobrescrever com valores da URL
    if (syncWithUrl) {
      filters.forEach((filter) => {
        const paramValue = searchParams.get(filter.id);
        
        if (paramValue !== null) {
          // Converter o valor da URL para o tipo apropriado
          switch (filter.type) {
            case 'number':
              state[filter.id] = parseFloat(paramValue);
              break;
            case 'boolean':
              state[filter.id] = paramValue === 'true';
              break;
            case 'numberRange':
              try {
                state[filter.id] = JSON.parse(paramValue);
              } catch {
                state[filter.id] = null;
              }
              break;
            case 'dateRange':
              try {
                const range = JSON.parse(paramValue);
                state[filter.id] = {
                  from: range.from ? new Date(range.from) : null,
                  to: range.to ? new Date(range.to) : null,
                };
              } catch {
                state[filter.id] = null;
              }
              break;
            case 'date':
              try {
                state[filter.id] = paramValue ? new Date(paramValue) : null;
              } catch {
                state[filter.id] = null;
              }
              break;
            default:
              state[filter.id] = paramValue;
          }
        }
      });
    }
    
    return state;
  }, [filters, initialState, searchParams, syncWithUrl]);
  
  // Estado dos filtros
  const [filterState, setFilterState] = useState<FilterState>(getInitialFilterState);
  
  // Atualizar a URL quando os filtros mudarem
  useEffect(() => {
    if (syncWithUrl) {
      const newParams = new URLSearchParams(searchParams);
      
      // Atualizar ou remover parâmetros da URL com base no estado dos filtros
      Object.entries(filterState).forEach(([key, value]) => {
        if (value === null || value === undefined || value === '') {
          newParams.delete(key);
        } else if (typeof value === 'object') {
          // Para intervalos de datas ou números
          newParams.set(key, JSON.stringify(value));
        } else {
          newParams.set(key, String(value));
        }
      });
      
      // Atualizar a URL sem recarregar a página
      setSearchParams(newParams);
    }
    
    // Chamar o callback onChange se fornecido
    if (onChange) {
      onChange(filterState);
    }
  }, [filterState, syncWithUrl, setSearchParams, searchParams, onChange]);
  
  // Função para atualizar um único filtro
  const setFilter = useCallback((id: string, value: any) => {
    setFilterState((prev) => ({
      ...prev,
      [id]: value,
    }));
  }, []);
  
  // Função para atualizar múltiplos filtros de uma vez
  const setFilters = useCallback((newFilters: FilterState) => {
    setFilterState((prev) => ({
      ...prev,
      ...newFilters,
    }));
  }, []);
  
  // Função para limpar todos os filtros
  const clearFilters = useCallback(() => {
    const clearedState: FilterState = {};
    
    // Resetar para os valores padrão dos filtros
    filters.forEach((filter) => {
      clearedState[filter.id] = filter.defaultValue ?? null;
    });
    
    setFilterState(clearedState);
  }, [filters]);
  
  // Função para verificar se algum filtro está ativo
  const hasActiveFilters = useCallback(() => {
    return filters.some((filter) => {
      const value = filterState[filter.id];
      const defaultValue = filter.defaultValue ?? null;
      
      // Verificar se o valor atual é diferente do valor padrão
      if (value === null || value === undefined || value === '') {
        return defaultValue !== null && defaultValue !== undefined && defaultValue !== '';
      }
      
      if (typeof value === 'object' && value !== null) {
        // Para intervalos de datas ou números
        if ('from' in value || 'to' in value) {
          return (value.from !== null && value.from !== undefined) || 
                 (value.to !== null && value.to !== undefined);
        }
        
        // Para outros objetos, comparar como JSON
        return JSON.stringify(value) !== JSON.stringify(defaultValue);
      }
      
      return value !== defaultValue;
    });
  }, [filters, filterState]);
  
  // Função para obter o número de filtros ativos
  const getActiveFilterCount = useCallback(() => {
    return filters.reduce((count, filter) => {
      const value = filterState[filter.id];
      const defaultValue = filter.defaultValue ?? null;
      
      // Verificar se o valor atual é diferente do valor padrão
      if (value === null || value === undefined || value === '') {
        return count;
      }
      
      if (typeof value === 'object' && value !== null) {
        // Para intervalos de datas ou números
        if ('from' in value || 'to' in value) {
          if ((value.from !== null && value.from !== undefined) || 
              (value.to !== null && value.to !== undefined)) {
            return count + 1;
          }
          return count;
        }
        
        // Para outros objetos, comparar como JSON
        if (JSON.stringify(value) !== JSON.stringify(defaultValue)) {
          return count + 1;
        }
        return count;
      }
      
      if (value !== defaultValue) {
        return count + 1;
      }
      
      return count;
    }, 0);
  }, [filters, filterState]);
  
  // Função para transformar os filtros em parâmetros para a API
  const getApiParams = useCallback(() => {
    const params: Record<string, any> = {};
    
    filters.forEach((filter) => {
      const value = filterState[filter.id];
      
      if (value === null || value === undefined || value === '') {
        return;
      }
      
      // Aplicar transformação personalizada se fornecida
      if (filter.transform) {
        const transformedValue = filter.transform(value);
        if (transformedValue !== null && transformedValue !== undefined) {
          params[filter.id] = transformedValue;
        }
        return;
      }
      
      // Transformações padrão com base no tipo
      switch (filter.type) {
        case 'dateRange':
          if (value.from) {
            params[`${filter.id}From`] = value.from.toISOString().split('T')[0];
          }
          if (value.to) {
            params[`${filter.id}To`] = value.to.toISOString().split('T')[0];
          }
          break;
        case 'numberRange':
          if (value.from !== null && value.from !== undefined) {
            params[`${filter.id}Min`] = value.from;
          }
          if (value.to !== null && value.to !== undefined) {
            params[`${filter.id}Max`] = value.to;
          }
          break;
        case 'date':
          if (value instanceof Date) {
            params[filter.id] = value.toISOString().split('T')[0];
          }
          break;
        default:
          params[filter.id] = value;
      }
    });
    
    return params;
  }, [filters, filterState]);
  
  return {
    filters: filterState,
    setFilter,
    setFilters,
    clearFilters,
    hasActiveFilters,
    getActiveFilterCount,
    getApiParams,
  };
}
