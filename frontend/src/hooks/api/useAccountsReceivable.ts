import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { accountsReceivableService } from '@/services/api';
import { CreateAccountReceivableRequest, UpdateAccountReceivableRequest } from '@/types/api';
import { toast } from 'sonner';
import { useCreateTransaction } from './useTransactions';

// Hook para listar contas a receber com paginação e filtros
export const useAccountsReceivable = (
  page = 1,
  limit = 10,
  filters?: {
    search?: string,
    status?: string,
    startDate?: string,
    endDate?: string,
    categoryId?: string,
    entityId?: string
  }
) => {
  const { search, status, startDate, endDate, categoryId, entityId } = filters || {};

  return useQuery({
    queryKey: ['accountsReceivable', { page, limit, search, status, startDate, endDate, categoryId, entityId }],
    queryFn: () => accountsReceivableService.getAccountsReceivable(
      page, limit, search, status, startDate, endDate, categoryId, entityId
    ),
    placeholderData: (previousData) => previousData,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar uma conta a receber específica por ID
export const useAccountReceivable = (id: string) => {
  return useQuery({
    queryKey: ['accountsReceivable', id],
    queryFn: () => accountsReceivableService.getAccountReceivableById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para criar uma nova conta a receber
export const useCreateAccountReceivable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAccountReceivableRequest) => accountsReceivableService.createAccountReceivable(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accountsReceivable'] });
      toast.success('Conta a receber criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao criar conta a receber. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma conta a receber existente
export const useUpdateAccountReceivable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateAccountReceivableRequest }) =>
      accountsReceivableService.updateAccountReceivable(id, data),
    onSuccess: (updatedAccountReceivable) => {
      queryClient.invalidateQueries({ queryKey: ['accountsReceivable'] });
      queryClient.setQueryData(['accountsReceivable', updatedAccountReceivable.id], updatedAccountReceivable);
      toast.success('Conta a receber atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao atualizar conta a receber. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma conta a receber
export const useDeleteAccountReceivable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => accountsReceivableService.deleteAccountReceivable(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['accountsReceivable'] });
      queryClient.removeQueries({ queryKey: ['accountsReceivable', id] });
      toast.success('Conta a receber excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir conta a receber. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para marcar uma conta a receber como recebida (DEPRECATED)
// Este hook foi mantido para compatibilidade, mas será removido em versões futuras
// Use o hook useCreateTransaction para registrar recebimentos
export const useReceiveAccountReceivable = () => {
  const queryClient = useQueryClient();
  const { mutate: createTransaction } = useCreateTransaction();

  console.warn('O hook useReceiveAccountReceivable está depreciado. Use o hook useCreateTransaction para registrar recebimentos.');

  return useMutation({
    mutationFn: async ({
      id,
      receiveDate,
      bankAccountId,
      amount,
      description,
      paymentMethodId,
      notes
    }: {
      id: string,
      receiveDate?: string,
      bankAccountId: string,
      amount?: number,
      description?: string,
      paymentMethodId?: string,
      notes?: string
    }) => {
      // Obter a conta a receber para ter acesso aos dados necessários
      const accountReceivable = await accountsReceivableService.getAccountReceivableById(id);

      // Criar uma transação do tipo income vinculada à conta a receber
      createTransaction({
        type: 'income',
        amount: amount || accountReceivable.amount,
        description: description || `Recebimento: ${accountReceivable.description}`,
        transactionDate: receiveDate || new Date().toISOString().split('T')[0],
        bankAccountId,
        categoryId: accountReceivable.categoryId,
        entityId: accountReceivable.entityId,
        projectId: accountReceivable.projectId,
        paymentMethodId: paymentMethodId || accountReceivable.paymentMethodId,
        accountsReceivableId: id,
        notes
      });

      return accountReceivable; // Retornar a conta a receber sem modificá-la
    },
    onSuccess: (accountReceivable) => {
      queryClient.invalidateQueries({ queryKey: ['accountsReceivable'] });
      queryClient.setQueryData(['accountsReceivable', accountReceivable.id], accountReceivable);
      toast.success('Conta a receber baixada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao baixar conta a receber. Por favor, tente novamente.'
      );
    }
  });
};
