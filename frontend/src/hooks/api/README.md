# Hooks de Payment Methods e Recurrence Types

## Visão Geral

Este documento descreve os novos hooks criados para substituir os arrays hard-coded de Payment Methods e Recurrence Types no projeto.

## Hooks Criados

### usePaymentMethods
Hook para gerenciar métodos de pagamento com operações CRUD completas.

```typescript
import { usePaymentMethods, usePaymentMethodOptions } from '@/hooks/api';

// Para listagem com paginação
const { data, isLoading, error } = usePaymentMethods(page, limit, search, isActive);

// Para obter apenas opções ativas (mais comum em formulários)
const { data: options, isLoading } = usePaymentMethodOptions();
```

### useRecurrenceTypes
Hook para gerenciar tipos de recorrência com operações CRUD completas.

```typescript
import { useRecurrenceTypes, useRecurrenceTypeOptions } from '@/hooks/api';

// Para listagem com paginação
const { data, isLoading, error } = useRecurrenceTypes(page, limit, search, isActive);

// Para obter apenas opções ativas (mais comum em formulários)
const { data: options, isLoading } = useRecurrenceTypeOptions();
```

## Hooks Utilitários

### usePaymentMethodOptions
Retorna os métodos de pagamento ativos formatados para uso em selects:

```typescript
const { data: options, isLoading } = usePaymentMethodOptions();

// Formato retornado:
// [
//   { id: "uuid", name: "Dinheiro", label: "Dinheiro", value: "uuid" },
//   { id: "uuid", name: "Cartão de Crédito", label: "Cartão de Crédito", value: "uuid" }
// ]
```

### useRecurrenceTypeOptions
Retorna os tipos de recorrência ativos formatados para uso em selects:

```typescript
const { data: options, isLoading } = useRecurrenceTypeOptions();

// Formato retornado:
// [
//   { id: "uuid", name: "Mensal", label: "Mensal", value: "uuid", code: "monthly", intervalDays: 30 }
// ]
```

## Migração de Arrays Hard-coded

### Antes (DEPRECATED)
```typescript
const paymentMethods = [
  { id: "cash", name: "Dinheiro" },
  { id: "credit_card", name: "Cartão de Crédito" },
  // ...
];

const recurrenceOptions = [
  { id: "none", name: "Não recorrente" },
  { id: "monthly", name: "Mensal" },
  // ...
];
```

### Depois (RECOMENDADO)
```typescript
import { usePaymentMethodOptions, useRecurrenceTypeOptions } from '@/hooks/api';

function MyComponent() {
  const { data: paymentMethods, isLoading: loadingPaymentMethods } = usePaymentMethodOptions();
  const { data: recurrenceOptions, isLoading: loadingRecurrenceTypes } = useRecurrenceTypeOptions();

  if (loadingPaymentMethods || loadingRecurrenceTypes) {
    return <div>Carregando...</div>;
  }

  return (
    <Select>
      {paymentMethods?.map((method) => (
        <SelectItem key={method.id} value={method.id}>
          {method.name}
        </SelectItem>
      ))}
    </Select>
  );
}
```

## Arquivos Atualizados

### Hooks Criados
- `frontend/src/hooks/api/usePaymentMethods.ts` - Novo hook para Payment Methods
- `frontend/src/hooks/api/useRecurrenceTypes.ts` - Novo hook para Recurrence Types
- `frontend/src/hooks/api/index.ts` - Exportações atualizadas

### Componentes Migrados
- `frontend/src/components/transactions/TransactionForm.tsx`
- `frontend/src/components/accounts-payable/AccountsPayableModal.tsx`
- `frontend/src/components/accounts-receivable/AccountsReceivableModal.tsx`
- `frontend/src/pages/AccountsPayableDetail.tsx`

### Hooks Atualizados
- `frontend/src/hooks/useTransactionFormData.ts` - Refatorado para usar os novos hooks

### Constantes Marcadas como DEPRECATED
- `frontend/src/components/accounts-receivable/constants.ts` - Arrays marcados como deprecated

## Benefícios da Migração

1. **Dados Dinâmicos**: Os dados agora vêm da API em tempo real
2. **Cache Inteligente**: React Query gerencia cache e revalidação automaticamente
3. **Estados de Loading**: Controle adequado de estados de carregamento
4. **Tratamento de Erros**: Melhor handling de erros de API
5. **Consistência**: Dados sempre sincronizados com o backend
6. **Performance**: Cache reduz chamadas desnecessárias à API
7. **Manutenibilidade**: Código mais limpo e reutilizável

## Próximos Passos

1. Testar todos os componentes migrados
2. Remover arrays hard-coded após confirmação de funcionamento
3. Considerar migração de outros dados estáticos para hooks similares
4. Documentar padrões para futuras implementações

## Troubleshooting

### Erro: "Cannot find name 'paymentMethodOptions'"
Certifique-se de importar e usar o hook:
```typescript
import { usePaymentMethodOptions } from '@/hooks/api';

const { data: paymentMethodOptions } = usePaymentMethodOptions();
```

### Dados não carregam
Verifique se:
1. A API está funcionando corretamente
2. Os endpoints `/payment-methods/active` e `/recurrence-types/active` existem
3. O usuário tem permissões adequadas
4. O React Query Provider está configurado corretamente