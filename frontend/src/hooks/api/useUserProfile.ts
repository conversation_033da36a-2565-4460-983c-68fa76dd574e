import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/api';
import { UserProfile } from '@/types/api';
import { toast } from 'sonner';

// Hook para buscar o perfil do usuário atual
export const useUserProfile = () => {
  return useQuery({
    queryKey: ['userProfile'],
    queryFn: async () => {
      try {
        const data = await userService.getProfile();
        // Garantir que nunca retorne undefined
        if (!data) {
          throw new Error('Perfil do usuário não encontrado');
        }
        return data;
      } catch (error) {
        // Erro ao buscar perfil do usuário - propagar erro sem log
        throw error;
      }
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para atualizar o perfil do usuário
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<UserProfile>) => userService.updateProfile(data),
    onSuccess: (updatedUser) => {
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      toast.success('Perfil atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao atualizar perfil. Por favor, tente novamente.'
      );
    }
  });
};
