import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { paymentMethodService, PaymentMethod, CreatePaymentMethodRequest, UpdatePaymentMethodRequest } from '@/services/api/paymentMethodService';
import { toast } from 'sonner';

// Hook para listar métodos de pagamento com paginação
export const usePaymentMethods = (page = 1, limit = 50, search?: string) => {
  return useQuery({
    queryKey: ['paymentMethods', { page, limit, search }],
    queryFn: () => paymentMethodService.getPaymentMethods(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um método de pagamento específico por ID
export const usePaymentMethod = (id: string) => {
  return useQuery({
    queryKey: ['paymentMethods', id],
    queryFn: () => paymentMethodService.getPaymentMethodById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar todos os métodos de pagamento ativos (sem paginação)
export const useActivePaymentMethods = () => {
  return useQuery({
    queryKey: ['paymentMethods', 'active'],
    queryFn: () => paymentMethodService.getAllActivePaymentMethods(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo método de pagamento
export const useCreatePaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreatePaymentMethodRequest) => paymentMethodService.createPaymentMethod(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
      toast.success('Método de pagamento criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar método de pagamento. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um método de pagamento existente
export const useUpdatePaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdatePaymentMethodRequest }) => 
      paymentMethodService.updatePaymentMethod(id, data),
    onSuccess: (updatedPaymentMethod) => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
      queryClient.setQueryData(['paymentMethods', updatedPaymentMethod.id], updatedPaymentMethod);
      toast.success('Método de pagamento atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar método de pagamento. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um método de pagamento
export const useDeletePaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => paymentMethodService.deletePaymentMethod(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
      queryClient.removeQueries({ queryKey: ['paymentMethods', id] });
      toast.success('Método de pagamento excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir método de pagamento. Por favor, tente novamente.'
      );
    }
  });
};

// Hook utilitário para transformar PaymentMethod em formato de opção para selects
export const usePaymentMethodOptions = () => {
  const { data: paymentMethods, ...rest } = useActivePaymentMethods();
  
  const options = paymentMethods?.map(method => ({
    id: method.id,
    name: method.name,
    label: method.name,
    value: method.id
  })) || [];
  
  return {
    data: options,
    paymentMethods,
    ...rest
  };
};