import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { recurrenceTypeService, RecurrenceType, CreateRecurrenceTypeRequest, UpdateRecurrenceTypeRequest } from '@/services/api/recurrenceTypeService';
import { toast } from 'sonner';

// Hook para listar tipos de recorrência com paginação
export const useRecurrenceTypes = (page = 1, limit = 50, search?: string) => {
  return useQuery({
    queryKey: ['recurrenceTypes', { page, limit, search }],
    queryFn: () => recurrenceTypeService.getRecurrenceTypes(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar um tipo de recorrência específico por ID
export const useRecurrenceType = (id: string) => {
  return useQuery({
    queryKey: ['recurrenceTypes', id],
    queryFn: () => recurrenceTypeService.getRecurrenceTypeById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar todos os tipos de recorrência ativos (sem paginação)
export const useActiveRecurrenceTypes = () => {
  return useQuery({
    queryKey: ['recurrenceTypes', 'active'],
    queryFn: () => recurrenceTypeService.getAllActiveRecurrenceTypes(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo tipo de recorrência
export const useCreateRecurrenceType = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateRecurrenceTypeRequest) => recurrenceTypeService.createRecurrenceType(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recurrenceTypes'] });
      toast.success('Tipo de recorrência criado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar tipo de recorrência. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar um tipo de recorrência existente
export const useUpdateRecurrenceType = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateRecurrenceTypeRequest }) => 
      recurrenceTypeService.updateRecurrenceType(id, data),
    onSuccess: (updatedRecurrenceType) => {
      queryClient.invalidateQueries({ queryKey: ['recurrenceTypes'] });
      queryClient.setQueryData(['recurrenceTypes', updatedRecurrenceType.id], updatedRecurrenceType);
      toast.success('Tipo de recorrência atualizado com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar tipo de recorrência. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir um tipo de recorrência
export const useDeleteRecurrenceType = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => recurrenceTypeService.deleteRecurrenceType(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['recurrenceTypes'] });
      queryClient.removeQueries({ queryKey: ['recurrenceTypes', id] });
      toast.success('Tipo de recorrência excluído com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir tipo de recorrência. Por favor, tente novamente.'
      );
    }
  });
};

// Hook utilitário para transformar RecurrenceType em formato de opção para selects
export const useRecurrenceTypeOptions = () => {
  const { data: recurrenceTypes, ...rest } = useActiveRecurrenceTypes();
  
  const options = recurrenceTypes?.map(type => ({
    id: type.id,
    name: type.name,
    label: type.name,
    value: type.id,
    description: type.description
  })) || [];
  
  return {
    data: options,
    recurrenceTypes,
    ...rest
  };
};