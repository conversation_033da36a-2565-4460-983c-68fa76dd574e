import { useQuery } from '@tanstack/react-query';
import { reportService } from '@/services/api';
import api from '@/services/api/axios';
import { toast } from 'sonner';

// Interceptador para tratar erros específicos de relatórios
const handleReportError = (error: any) => {
  if (error.config?.url?.includes('/reports')) {
    toast.error(
      error.response?.data?.message || 
      'Falha ao obter relatório. Por favor, tente novamente.'
    );
  }
  return Promise.reject(error);
};

// Hook para obter relatório de fluxo de caixa
export const useCashFlowReport = (
  startDate: string,
  endDate: string,
  companyId?: string,
  bankAccountIds?: string[],
  categoryIds?: string[],
  periodType?: 'daily' | 'weekly' | 'monthly'
) => {
  return useQuery({
    queryKey: [
      'reports', 
      'cashFlow', 
      { startDate, endDate, companyId, bankAccountIds, categoryIds, periodType }
    ],
    queryFn: async () => {
      try {
        return await reportService.getCashFlowReport(
          startDate, 
          endDate, 
          companyId || '', 
          bankAccountIds, 
          categoryIds, 
          periodType
        );
      } catch (error) {
        return handleReportError(error);
      }
    },
    enabled: !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 1
  });
};



// Hook para obter relatório de balanço patrimonial
export const useBalanceSheetReport = (
  date: string,
  companyId?: string
) => {
  return useQuery({
    queryKey: ['reports', 'balanceSheet', { date, companyId }],
    queryFn: async () => {
      try {
        return await reportService.getBalanceSheetReport(date, companyId || '');
      } catch (error) {
        return handleReportError(error);
      }
    },
    enabled: !!date,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 1
  });
};



// Hook para obter demonstração de resultados
export const useIncomeStatementReport = (
  startDate: string,
  endDate: string,
  companyId?: string,
  compareWithPreviousPeriod?: boolean
) => {
  return useQuery({
    queryKey: [
      'reports', 
      'incomeStatement', 
      { startDate, endDate, companyId, compareWithPreviousPeriod }
    ],
    queryFn: async () => {
      try {
        return await reportService.getIncomeStatementReport(
          startDate, 
          endDate, 
          companyId || '', 
          compareWithPreviousPeriod
        );
      } catch (error) {
        return handleReportError(error);
      }
    },
    enabled: !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 1
  });
};


