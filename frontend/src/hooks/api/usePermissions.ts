import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { permissionService } from '@/services/api';
import { CreatePermissionRequest, UpdatePermissionRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar permissões com paginação
export const usePermissions = (page = 1, limit = 10, search?: string) => {
  return useQuery({
    queryKey: ['permissions', { page, limit, search }],
    queryFn: () => permissionService.getPermissions(page, limit, search),
    placeholderData: (previousData) => previousData,
    staleTime: 10 * 60 * 1000, // 10 minutos (dados não mudam com frequência)
  });
};

// Hook para buscar uma permissão específica por ID
export const usePermission = (id: string) => {
  return useQuery({
    queryKey: ['permissions', id],
    queryFn: () => permissionService.getPermissionById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para criar uma nova permissão
export const useCreatePermission = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreatePermissionRequest) => permissionService.createPermission(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      toast.success('Permissão criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar permissão. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma permissão existente
export const useUpdatePermission = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdatePermissionRequest }) => 
      permissionService.updatePermission(id, data),
    onSuccess: (updatedPermission) => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      queryClient.setQueryData(['permissions', updatedPermission.id], updatedPermission);
      toast.success('Permissão atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar permissão. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma permissão
export const useDeletePermission = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => permissionService.deletePermission(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      queryClient.removeQueries({ queryKey: ['permissions', id] });
      toast.success('Permissão excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao excluir permissão. Por favor, tente novamente.'
      );
    }
  });
};
