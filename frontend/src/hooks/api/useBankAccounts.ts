import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { bankAccountService } from '@/services/api';
import { CreateBankAccountRequest, UpdateBankAccountRequest } from '@/types/api';
import { toast } from 'sonner';

// Vamos remover esta abordagem e usar uma solução diferente

// Hook para listar contas bancárias com paginação e filtros
export const useBankAccounts = (
  page = 1,
  limit = 10,
  filters?: {
    search?: string,
    type?: string,
    bankId?: string,
    companyId?: string
  }
) => {
  const { search, type, bankId, companyId } = filters || {};

  return useQuery({
    queryKey: ['bankAccounts', { page, limit, search, type, bankId, companyId }],
    queryFn: async () => {
      try {
        const result = await bankAccountService.getBankAccounts(
          page, limit, search, type, bankId, companyId
        );
        return result;
      } catch (error) {
        // Erro na consulta de contas bancárias - propagar erro sem log
        throw error;
      }
    },
    enabled: !!companyId, // Só executar se tiver companyId
    placeholderData: (previousData) => previousData,
    staleTime: 0, // Dados ficam stale imediatamente para garantir atualização
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchInterval: false, // Remover intervalo automático para evitar conflitos
  });
};

// Hook para buscar uma conta bancária específica por ID
export const useBankAccount = (id: string) => {
  return useQuery({
    queryKey: ['bankAccounts', id],
    queryFn: () => bankAccountService.getBankAccountById(id),
    enabled: !!id,
    staleTime: 3 * 60 * 1000, // 3 minutos
  });
};

// Hook para obter o saldo de uma conta bancária
export const useBankAccountBalance = (id: string) => {
  return useQuery({
    queryKey: ['bankAccounts', id, 'balance'],
    queryFn: () => bankAccountService.getBankAccountBalance(id),
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minuto (atualizações mais frequentes para saldos)
  });
};

// Hook para criar uma nova conta bancária
export const useCreateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => bankAccountService.createBankAccount(data),
    onSuccess: () => {
      // Invalidar todas as consultas de contas bancárias
      queryClient.invalidateQueries({ queryKey: ['bankAccounts'] });

      // Forçar uma revalidação imediata de todas as consultas de contas bancárias
      // Usar refetchQueries com exact: false para garantir que todas as consultas relacionadas sejam revalidadas
      queryClient.refetchQueries({ queryKey: ['bankAccounts'], exact: false });

      toast.success('Conta bancária criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao criar conta bancária. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma conta bancária existente
export const useUpdateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateBankAccountRequest }) =>
      bankAccountService.updateBankAccount(id, data),
    onSuccess: (updatedBankAccount) => {
      console.log('Conta bancária atualizada com sucesso:', updatedBankAccount);

      // Estratégia otimizada de invalidação
      // 1. Atualizar o cache específico da conta atualizada
      queryClient.setQueryData(['bankAccounts', updatedBankAccount.id], updatedBankAccount);

      // 2. Invalidar todas as consultas de listagem de contas bancárias
      queryClient.invalidateQueries({
        queryKey: ['bankAccounts'],
        exact: false,
        refetchType: 'active'
      });

      // 3. Forçar refetch das queries ativas para garantir sincronização
      queryClient.refetchQueries({
        queryKey: ['bankAccounts'],
        exact: false,
        type: 'active'
      });

      toast.success('Conta bancária atualizada com sucesso!');
    },
    onError: (error: any) => {
      console.error('Erro ao atualizar conta bancária:', error);
      toast.error(
        error.response?.data?.message ||
        'Falha ao atualizar conta bancária. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma conta bancária
export const useDeleteBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => bankAccountService.deleteBankAccount(id),
    onSuccess: (_data, id) => {
      console.log('Conta bancária excluída com sucesso:', id);

      // Usar apenas invalidateQueries para evitar múltiplas requisições
      // Isso é suficiente para forçar o React Query a buscar novos dados
      queryClient.invalidateQueries({ queryKey: ['bankAccounts'] });

      // Remover a consulta específica da conta excluída
      queryClient.removeQueries({ queryKey: ['bankAccounts', id] });

      // Usar um pequeno atraso antes de mostrar a mensagem de sucesso
      // para garantir que a interface seja atualizada corretamente
      setTimeout(() => {
        toast.success('Conta bancária excluída com sucesso!');
      }, 100);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir conta bancária. Por favor, tente novamente.'
      );
    }
  });
};
