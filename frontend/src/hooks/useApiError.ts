import { useCallback } from 'react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

interface ApiErrorOptions {
  showToast?: boolean;
  redirectOnAuthError?: boolean;
  customErrorMessages?: Record<number, string>;
}

const defaultOptions: ApiErrorOptions = {
  showToast: true,
  redirectOnAuthError: true,
  customErrorMessages: {}
};

// Mensagens de erro padrão por código HTTP
const defaultErrorMessages: Record<number, string> = {
  400: 'Dados inválidos. Verifique as informações enviadas.',
  401: 'Sessão expirada. Por favor, faça login novamente.',
  403: 'Você não tem permissão para acessar este recurso.',
  404: 'Recurso não encontrado.',
  409: 'Conflito com o estado atual do recurso.',
  422: 'Não foi possível processar os dados enviados.',
  429: 'Muitas requisições. Por favor, aguarde um momento e tente novamente.',
  500: 'Erro interno do servidor. Tente novamente mais tarde.',
  502: 'Serviço temporariamente indisponível. Tente novamente mais tarde.',
  503: 'Serviço indisponível. Tente novamente mais tarde.',
  504: 'Tempo de resposta excedido. Verifique sua conexão e tente novamente.'
};

export function useApiError(options: ApiErrorOptions = {}, logoutFn?: () => Promise<void>) {
  const navigate = useNavigate();
  
  // Mesclar opções padrão com as opções fornecidas
  const mergedOptions = { ...defaultOptions, ...options };
  
  // Mesclar mensagens de erro padrão com as mensagens personalizadas
  const errorMessages = { ...defaultErrorMessages, ...mergedOptions.customErrorMessages };

  const handleApiError = useCallback((error: any) => {
    // Extrair informações do erro
    const status = error?.response?.status;
    const data = error?.response?.data;
    const message = data?.message || errorMessages[status] || 'Ocorreu um erro inesperado.';
    
    // Erro de API - processar sem log no console
    
    // Mostrar toast de erro, se configurado
    if (mergedOptions.showToast) {
      toast.error(message);
    }
    
    // Tratamento específico por código de erro
    switch (status) {
      case 401:
        // Erro de autenticação
        if (mergedOptions.redirectOnAuthError && logoutFn) {
          logoutFn();
        } else if (mergedOptions.redirectOnAuthError) {
          // Se não tiver função de logout, apenas redireciona para login
          navigate('/login');
        }
        break;
        
      case 403:
        // Erro de permissão
        // Pode-se redirecionar para uma página de acesso negado
        break;
        
      case 404:
        // Recurso não encontrado
        // Pode-se redirecionar para uma página 404 personalizada
        break;
        
      case 429:
        // Rate limiting
        toast.warning('Muitas requisições. Aguarde um momento antes de tentar novamente.');
        break;
        
      case 500:
      case 502:
      case 503:
      case 504:
        // Erros de servidor
        // Pode-se mostrar uma mensagem mais amigável ou uma página de manutenção
        break;
    }
    
    // Retornar o erro para processamento adicional, se necessário
    return { error, status, message, data };
  }, [navigate, logoutFn, mergedOptions, errorMessages]);

  // Função para envolver chamadas de API com tratamento de erro
  const withErrorHandling = useCallback(<T>(promise: Promise<T>): Promise<T> => {
    return promise.catch(error => {
      handleApiError(error);
      throw error; // Re-throw para permitir tratamento adicional
    });
  }, [handleApiError]);

  return {
    handleApiError,
    withErrorHandling
  };
}
